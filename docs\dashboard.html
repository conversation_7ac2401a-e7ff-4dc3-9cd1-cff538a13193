<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure VPN - Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .dashboard {
            padding: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-card h3 {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .stat-card p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1em;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:active {
            transform: translateY(0);
        }

        .user-list {
            background: white;
            border-radius: 5px;
            overflow: hidden;
        }

        .user-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .user-item:last-child {
            border-bottom: none;
        }

        .user-info h4 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .user-info p {
            color: #666;
            font-size: 0.9em;
        }

        .plan-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }

        .plan-free { background: #95a5a6; color: white; }
        .plan-basic { background: #3498db; color: white; }
        .plan-premium { background: #e74c3c; color: white; }
        .plan-enterprise { background: #f39c12; color: white; }

        .response {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }

        .response.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .response.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .api-endpoint {
            background: #2c3e50;
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 Secure VPN</h1>
            <p>Commercial VPN Service Administration Dashboard</p>
        </div>

        <div class="dashboard">
            <div class="stats-grid">
                <div class="stat-card">
                    <h3 id="totalUsers">0</h3>
                    <p>Total Users</p>
                </div>
                <div class="stat-card">
                    <h3 id="activeConnections">0</h3>
                    <p>Active Connections</p>
                </div>
                <div class="stat-card">
                    <h3 id="totalBandwidth">0 GB</h3>
                    <p>Total Bandwidth</p>
                </div>
                <div class="stat-card">
                    <h3 id="revenue">$0</h3>
                    <p>Monthly Revenue</p>
                </div>
            </div>

            <div class="section">
                <h2>🆕 Create New User</h2>
                <div class="api-endpoint">POST /api/users</div>
                <form id="createUserForm">
                    <div class="form-group">
                        <label for="email">Email Address:</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password:</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <div class="form-group">
                        <label for="plan">Subscription Plan:</label>
                        <select id="plan" name="plan" required>
                            <option value="free">Free (1 device, 1GB/day)</option>
                            <option value="basic">Basic (3 devices, 10GB/day) - $5/month</option>
                            <option value="premium">Premium (5 devices, 100GB/day) - $10/month</option>
                            <option value="enterprise">Enterprise (10 devices, unlimited) - $25/month</option>
                        </select>
                    </div>
                    <button type="submit" class="btn">Create User</button>
                    <div id="createUserResponse" class="response"></div>
                </form>
            </div>

            <div class="section">
                <h2>🔑 Generate License</h2>
                <div class="api-endpoint">POST /api/licenses</div>
                <form id="generateLicenseForm">
                    <div class="form-group">
                        <label for="userId">User ID:</label>
                        <input type="number" id="userId" name="userId" required>
                    </div>
                    <div class="form-group">
                        <label for="deviceName">Device Name:</label>
                        <input type="text" id="deviceName" name="deviceName" placeholder="e.g., John's iPhone" required>
                    </div>
                    <div class="form-group">
                        <label for="duration">Duration (days):</label>
                        <input type="number" id="duration" name="duration" value="30" required>
                    </div>
                    <button type="submit" class="btn">Generate License</button>
                    <div id="generateLicenseResponse" class="response"></div>
                </form>
            </div>

            <div class="section">
                <h2>👥 User Management</h2>
                <div class="api-endpoint">GET /api/users</div>
                <button onclick="loadUsers()" class="btn">Refresh User List</button>
                <div id="userList" class="user-list" style="margin-top: 20px;">
                    <!-- Users will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';

        // Create user
        document.getElementById('createUserForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const userData = Object.fromEntries(formData);

            try {
                const response = await fetch(`${API_BASE}/users`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(userData)
                });

                const result = await response.json();
                const responseDiv = document.getElementById('createUserResponse');

                if (response.ok) {
                    responseDiv.className = 'response success';
                    responseDiv.textContent = `User created successfully! User ID: ${result.user_id}`;
                    e.target.reset();
                    loadUsers(); // Refresh user list
                } else {
                    responseDiv.className = 'response error';
                    responseDiv.textContent = result.error || 'Failed to create user';
                }
                responseDiv.style.display = 'block';
            } catch (error) {
                const responseDiv = document.getElementById('createUserResponse');
                responseDiv.className = 'response error';
                responseDiv.textContent = 'Network error: ' + error.message;
                responseDiv.style.display = 'block';
            }
        });

        // Generate license
        document.getElementById('generateLicenseForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const licenseData = Object.fromEntries(formData);

            try {
                const response = await fetch(`${API_BASE}/licenses`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(licenseData)
                });

                const result = await response.json();
                const responseDiv = document.getElementById('generateLicenseResponse');

                if (response.ok) {
                    responseDiv.className = 'response success';
                    responseDiv.innerHTML = `License generated successfully!<br><strong>License Key:</strong> ${result.license_key}`;
                } else {
                    responseDiv.className = 'response error';
                    responseDiv.textContent = result.error || 'Failed to generate license';
                }
                responseDiv.style.display = 'block';
            } catch (error) {
                const responseDiv = document.getElementById('generateLicenseResponse');
                responseDiv.className = 'response error';
                responseDiv.textContent = 'Network error: ' + error.message;
                responseDiv.style.display = 'block';
            }
        });

        // Load REAL users from API
        async function loadUsers() {
            try {
                const response = await fetch(`${API_BASE}/users`);
                const users = await response.json();

                const userListDiv = document.getElementById('userList');

                if (users.length === 0) {
                    userListDiv.innerHTML = '<div class="user-item"><p>No users found</p></div>';
                    return;
                }

                userListDiv.innerHTML = users.map(user => `
                    <div class="user-item">
                        <div class="user-info">
                            <h4>${user.email}</h4>
                            <p>User ID: ${user.user_id} | Created: ${user.created_at} | Last Login: ${user.last_login}</p>
                        </div>
                        <span class="plan-badge plan-${user.plan}">${user.plan.toUpperCase()}</span>
                    </div>
                `).join('');

            } catch (error) {
                console.error('Error loading users:', error);
                document.getElementById('userList').innerHTML =
                    '<div class="user-item"><p>Error loading users</p></div>';
            }
        }

        // Load REAL stats from API
        async function loadStats() {
            try {
                const response = await fetch(`${API_BASE}/stats`);
                const stats = await response.json();

                document.getElementById('totalUsers').textContent = stats.total_users;
                document.getElementById('activeConnections').textContent = stats.active_connections;
                document.getElementById('totalBandwidth').textContent = stats.total_bandwidth_gb + ' GB';
                document.getElementById('revenue').textContent = '$' + stats.monthly_revenue;

                // Update page title with real-time info
                document.title = `Secure VPN - ${stats.active_connections} Active | ${stats.total_users} Users`;

            } catch (error) {
                console.error('Error loading stats:', error);
                document.getElementById('totalUsers').textContent = 'Error';
                document.getElementById('activeConnections').textContent = 'Error';
                document.getElementById('totalBandwidth').textContent = 'Error';
                document.getElementById('revenue').textContent = 'Error';
            }
        }

        // Load initial data
        loadUsers();
        loadStats();

        // Auto-refresh every 5 seconds
        setInterval(loadStats, 5000);
        setInterval(loadUsers, 30000);
    </script>
</body>
</html>
