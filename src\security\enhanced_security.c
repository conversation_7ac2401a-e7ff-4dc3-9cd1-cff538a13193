#include "enhanced_security.h"
#include "securevpn.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <openssl/rand.h>
#include <openssl/evp.h>
#include <openssl/sha.h>
#include <sys/mman.h>

// Default security configurations
static const rate_limit_config_t DEFAULT_RATE_LIMIT_CONFIG = {
    .max_requests_per_minute = 60,
    .max_requests_per_hour = 1000,
    .max_failed_auth_attempts = 5,
    .ban_duration_seconds = 3600,  // 1 hour
    .enable_progressive_delays = true
};

static const ddos_config_t DEFAULT_DDOS_CONFIG = {
    .max_connections_per_ip = 10,
    .max_new_connections_per_second = 5,
    .packet_rate_threshold = 1000,
    .bandwidth_threshold_mbps = 100,
    .enable_syn_flood_protection = true,
    .enable_packet_inspection = true
};

static const ids_config_t DEFAULT_IDS_CONFIG = {
    .enable_port_scan_detection = true,
    .enable_brute_force_detection = true,
    .enable_anomaly_detection = true,
    .port_scan_threshold = 10,
    .brute_force_threshold = 5,
    .anomaly_score_threshold = 80
};

static const password_policy_t DEFAULT_PASSWORD_POLICY = {
    .min_length = 8,
    .require_uppercase = true,
    .require_lowercase = true,
    .require_numbers = true,
    .require_special_chars = true,
    .max_age_days = 90,
    .history_count = 5,
    .lockout_threshold = 5,
    .lockout_duration_minutes = 30
};

security_error_t security_init(security_context_t *ctx, const rate_limit_config_t *rate_config,
                              const ddos_config_t *ddos_config, const ids_config_t *ids_config) {
    if (!ctx) {
        return SECURITY_ERROR_INVALID_PARAM;
    }

    memset(ctx, 0, sizeof(security_context_t));

    // Use provided configs or defaults
    ctx->rate_limit_config = rate_config ? *rate_config : DEFAULT_RATE_LIMIT_CONFIG;
    ctx->ddos_config = ddos_config ? *ddos_config : DEFAULT_DDOS_CONFIG;
    ctx->ids_config = ids_config ? *ids_config : DEFAULT_IDS_CONFIG;
    ctx->password_policy = DEFAULT_PASSWORD_POLICY;

    // Allocate rate limiting table
    ctx->rate_limit_table_size = 1024;  // Start with 1024 entries
    ctx->rate_limit_table = calloc(ctx->rate_limit_table_size, sizeof(rate_limit_entry_t));
    if (!ctx->rate_limit_table) {
        return SECURITY_ERROR_MEMORY;
    }

    // Allocate event log
    ctx->event_log_size = 10000;  // Store up to 10,000 events
    ctx->event_log = calloc(ctx->event_log_size, sizeof(security_event_t));
    if (!ctx->event_log) {
        free(ctx->rate_limit_table);
        return SECURITY_ERROR_MEMORY;
    }

    ctx->is_initialized = true;
    return SECURITY_SUCCESS;
}

void security_cleanup(security_context_t *ctx) {
    if (!ctx || !ctx->is_initialized) {
        return;
    }

    if (ctx->rate_limit_table) {
        free(ctx->rate_limit_table);
        ctx->rate_limit_table = NULL;
    }

    if (ctx->event_log) {
        free(ctx->event_log);
        ctx->event_log = NULL;
    }

    ctx->is_initialized = false;
}

// Hash function for IP addresses
static uint32_t hash_ip_address(const struct sockaddr_in *addr) {
    uint32_t ip = ntohl(addr->sin_addr.s_addr);
    // Simple hash function
    ip ^= (ip >> 16);
    ip ^= (ip >> 8);
    return ip;
}

// Find or create rate limit entry for IP
static rate_limit_entry_t* get_rate_limit_entry(security_context_t *ctx, 
                                               const struct sockaddr_in *client_addr) {
    uint32_t hash = hash_ip_address(client_addr) % ctx->rate_limit_table_size;
    uint32_t original_hash = hash;

    // Linear probing to find entry
    while (ctx->rate_limit_table[hash].client_addr.sin_addr.s_addr != 0) {
        if (ctx->rate_limit_table[hash].client_addr.sin_addr.s_addr == client_addr->sin_addr.s_addr) {
            return &ctx->rate_limit_table[hash];
        }
        hash = (hash + 1) % ctx->rate_limit_table_size;
        if (hash == original_hash) {
            // Table is full
            return NULL;
        }
    }

    // Create new entry
    ctx->rate_limit_table[hash].client_addr = *client_addr;
    ctx->rate_limit_table[hash].last_request_time = time(NULL);
    ctx->rate_limit_table[hash].minute_window_start = time(NULL);
    ctx->rate_limit_table[hash].hour_window_start = time(NULL);
    
    return &ctx->rate_limit_table[hash];
}

security_error_t security_check_rate_limit(security_context_t *ctx, 
                                          const struct sockaddr_in *client_addr) {
    if (!ctx || !ctx->is_initialized || !client_addr) {
        return SECURITY_ERROR_INVALID_PARAM;
    }

    rate_limit_entry_t *entry = get_rate_limit_entry(ctx, client_addr);
    if (!entry) {
        return SECURITY_ERROR_MEMORY;
    }

    time_t now = time(NULL);

    // Check if IP is banned
    if (entry->is_banned && now < entry->ban_until) {
        return SECURITY_ERROR_BLOCKED;
    } else if (entry->is_banned && now >= entry->ban_until) {
        // Unban IP
        entry->is_banned = false;
        entry->failed_auth_attempts = 0;
        entry->threat_level = THREAT_LEVEL_LOW;
    }

    // Reset minute window if needed
    if (now - entry->minute_window_start >= 60) {
        entry->requests_this_minute = 0;
        entry->minute_window_start = now;
    }

    // Reset hour window if needed
    if (now - entry->hour_window_start >= 3600) {
        entry->requests_this_hour = 0;
        entry->hour_window_start = now;
    }

    // Check rate limits
    if (entry->requests_this_minute >= ctx->rate_limit_config.max_requests_per_minute) {
        security_log_event(ctx, client_addr, ATTACK_TYPE_DOS, THREAT_LEVEL_MEDIUM,
                          "Rate limit exceeded (per minute)");
        return SECURITY_ERROR_RATE_LIMITED;
    }

    if (entry->requests_this_hour >= ctx->rate_limit_config.max_requests_per_hour) {
        security_log_event(ctx, client_addr, ATTACK_TYPE_DOS, THREAT_LEVEL_HIGH,
                          "Rate limit exceeded (per hour)");
        return SECURITY_ERROR_RATE_LIMITED;
    }

    // Update counters
    entry->requests_this_minute++;
    entry->requests_this_hour++;
    entry->last_request_time = now;

    return SECURITY_SUCCESS;
}

security_error_t security_record_failed_auth(security_context_t *ctx,
                                            const struct sockaddr_in *client_addr) {
    if (!ctx || !ctx->is_initialized || !client_addr) {
        return SECURITY_ERROR_INVALID_PARAM;
    }

    rate_limit_entry_t *entry = get_rate_limit_entry(ctx, client_addr);
    if (!entry) {
        return SECURITY_ERROR_MEMORY;
    }

    entry->failed_auth_attempts++;

    // Check if we should ban this IP
    if (entry->failed_auth_attempts >= ctx->rate_limit_config.max_failed_auth_attempts) {
        time_t now = time(NULL);
        entry->is_banned = true;
        entry->ban_until = now + ctx->rate_limit_config.ban_duration_seconds;
        entry->threat_level = THREAT_LEVEL_HIGH;

        ctx->total_banned_ips++;

        security_log_event(ctx, client_addr, ATTACK_TYPE_BRUTE_FORCE, THREAT_LEVEL_HIGH,
                          "IP banned due to excessive failed authentication attempts");

        return SECURITY_ERROR_BLOCKED;
    }

    // Increase threat level based on failed attempts
    if (entry->failed_auth_attempts >= 3) {
        entry->threat_level = THREAT_LEVEL_MEDIUM;
    } else if (entry->failed_auth_attempts >= 2) {
        entry->threat_level = THREAT_LEVEL_LOW;
    }

    security_log_event(ctx, client_addr, ATTACK_TYPE_INVALID_AUTH, THREAT_LEVEL_LOW,
                      "Failed authentication attempt");

    return SECURITY_SUCCESS;
}

security_error_t security_ban_ip(security_context_t *ctx, const struct sockaddr_in *client_addr,
                                uint32_t duration_seconds, const char *reason) {
    if (!ctx || !ctx->is_initialized || !client_addr) {
        return SECURITY_ERROR_INVALID_PARAM;
    }

    rate_limit_entry_t *entry = get_rate_limit_entry(ctx, client_addr);
    if (!entry) {
        return SECURITY_ERROR_MEMORY;
    }

    time_t now = time(NULL);
    entry->is_banned = true;
    entry->ban_until = now + duration_seconds;
    entry->threat_level = THREAT_LEVEL_CRITICAL;

    ctx->total_banned_ips++;

    char description[256];
    snprintf(description, sizeof(description), "Manual IP ban: %s", reason ? reason : "No reason provided");
    
    security_log_event(ctx, client_addr, ATTACK_TYPE_DOS, THREAT_LEVEL_CRITICAL, description);

    return SECURITY_SUCCESS;
}

bool security_is_ip_banned(security_context_t *ctx, const struct sockaddr_in *client_addr) {
    if (!ctx || !ctx->is_initialized || !client_addr) {
        return false;
    }

    rate_limit_entry_t *entry = get_rate_limit_entry(ctx, client_addr);
    if (!entry) {
        return false;
    }

    time_t now = time(NULL);
    return entry->is_banned && now < entry->ban_until;
}

security_error_t security_log_event(security_context_t *ctx, const struct sockaddr_in *source_addr,
                                   attack_type_t attack_type, threat_level_t threat_level,
                                   const char *description) {
    if (!ctx || !ctx->is_initialized || !source_addr || !description) {
        return SECURITY_ERROR_INVALID_PARAM;
    }

    // Find next available slot (circular buffer)
    uint32_t index = ctx->event_log_count % ctx->event_log_size;
    security_event_t *event = &ctx->event_log[index];

    // Fill event details
    event->event_id = ctx->total_security_events++;
    event->timestamp = time(NULL);
    event->source_addr = *source_addr;
    event->attack_type = attack_type;
    event->threat_level = threat_level;
    strncpy(event->description, description, sizeof(event->description) - 1);
    event->description[sizeof(event->description) - 1] = '\0';

    // Calculate severity score
    event->severity_score = (uint32_t)threat_level * 25 + (uint32_t)attack_type * 10;

    // Determine if action was taken
    event->action_taken = (threat_level >= THREAT_LEVEL_MEDIUM);
    if (event->action_taken) {
        strncpy(event->action_description, "IP rate limited or banned", 
                sizeof(event->action_description) - 1);
    } else {
        strncpy(event->action_description, "Event logged only", 
                sizeof(event->action_description) - 1);
    }

    ctx->event_log_count++;
    return SECURITY_SUCCESS;
}

security_error_t security_validate_password(const char *password, const password_policy_t *policy) {
    if (!password || !policy) {
        return SECURITY_ERROR_INVALID_PARAM;
    }

    size_t len = strlen(password);
    
    // Check minimum length
    if (len < policy->min_length) {
        return SECURITY_ERROR_INVALID_PARAM;
    }

    bool has_upper = false, has_lower = false, has_digit = false, has_special = false;

    for (size_t i = 0; i < len; i++) {
        char c = password[i];
        if (c >= 'A' && c <= 'Z') has_upper = true;
        else if (c >= 'a' && c <= 'z') has_lower = true;
        else if (c >= '0' && c <= '9') has_digit = true;
        else if (strchr("!@#$%^&*()_+-=[]{}|;:,.<>?", c)) has_special = true;
    }

    if (policy->require_uppercase && !has_upper) return SECURITY_ERROR_INVALID_PARAM;
    if (policy->require_lowercase && !has_lower) return SECURITY_ERROR_INVALID_PARAM;
    if (policy->require_numbers && !has_digit) return SECURITY_ERROR_INVALID_PARAM;
    if (policy->require_special_chars && !has_special) return SECURITY_ERROR_INVALID_PARAM;

    return SECURITY_SUCCESS;
}

security_error_t security_generate_salt(char *salt_out, size_t salt_size) {
    if (!salt_out || salt_size < 16) {
        return SECURITY_ERROR_INVALID_PARAM;
    }

    unsigned char random_bytes[16];
    if (RAND_bytes(random_bytes, sizeof(random_bytes)) != 1) {
        return SECURITY_ERROR_CRYPTO;
    }

    // Convert to hex string
    for (int i = 0; i < 16 && i * 2 + 1 < salt_size; i++) {
        snprintf(&salt_out[i * 2], 3, "%02x", random_bytes[i]);
    }

    return SECURITY_SUCCESS;
}

void* security_malloc_secure(size_t size) {
    void *ptr = malloc(size);
    if (ptr) {
        // Lock memory to prevent swapping
        if (mlock(ptr, size) != 0) {
            // If locking fails, still return the pointer but warn
            fprintf(stderr, "Warning: Failed to lock secure memory\n");
        }
        // Clear memory
        memset(ptr, 0, size);
    }
    return ptr;
}

void security_free_secure(void *ptr, size_t size) {
    if (ptr) {
        // Clear memory before freeing
        memset(ptr, 0, size);
        munlock(ptr, size);
        free(ptr);
    }
}

const char* security_error_string(security_error_t error) {
    switch (error) {
        case SECURITY_SUCCESS:
            return "Success";
        case SECURITY_ERROR_INVALID_PARAM:
            return "Invalid parameter";
        case SECURITY_ERROR_RATE_LIMITED:
            return "Rate limited";
        case SECURITY_ERROR_BLOCKED:
            return "Blocked/Banned";
        case SECURITY_ERROR_CRYPTO:
            return "Cryptographic error";
        case SECURITY_ERROR_MEMORY:
            return "Memory allocation error";
        default:
            return "Unknown error";
    }
}

const char* threat_level_string(threat_level_t level) {
    switch (level) {
        case THREAT_LEVEL_LOW:
            return "Low";
        case THREAT_LEVEL_MEDIUM:
            return "Medium";
        case THREAT_LEVEL_HIGH:
            return "High";
        case THREAT_LEVEL_CRITICAL:
            return "Critical";
        default:
            return "Unknown";
    }
}

const char* attack_type_string(attack_type_t type) {
    switch (type) {
        case ATTACK_TYPE_BRUTE_FORCE:
            return "Brute Force";
        case ATTACK_TYPE_DOS:
            return "DoS";
        case ATTACK_TYPE_DDOS:
            return "DDoS";
        case ATTACK_TYPE_PORT_SCAN:
            return "Port Scan";
        case ATTACK_TYPE_INVALID_AUTH:
            return "Invalid Authentication";
        case ATTACK_TYPE_MALFORMED_PACKET:
            return "Malformed Packet";
        default:
            return "Unknown";
    }
}

void security_get_client_ip_string(const struct sockaddr_in *addr, char *ip_str, size_t size) {
    if (addr && ip_str && size > 0) {
        inet_ntop(AF_INET, &addr->sin_addr, ip_str, size);
    }
}
