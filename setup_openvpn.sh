#!/bin/bash

# Simple OpenVPN Setup for Free VPN Server
# Works with your existing VPN code

set -e

SERVER_IP="${1:-$(curl -s ifconfig.me)}"
echo "🚀 Setting up OpenVPN on server: $SERVER_IP"

# Install OpenVPN
echo "📦 Installing OpenVPN..."
sudo apt-get update
sudo apt-get install -y openvpn easy-rsa

# Setup your VPN with OpenVPN support
echo "🔧 Setting up VPN server..."
python3 openvpn_integration.py

# Configure firewall
echo "🔥 Configuring firewall..."
sudo ufw allow 1194/udp
sudo ufw allow 8443/udp
sudo ufw allow 8080/tcp

# Start OpenVPN server
echo "🎯 Starting OpenVPN server..."
sudo systemctl enable openvpn@server
sudo systemctl start openvpn@server

# Generate sample user profile
echo "👤 Creating sample user..."
python3 vpn_manager_tool.py --create-user <EMAIL> --plan premium
python3 vpn_manager_tool.py --generate-profiles <EMAIL> --server $SERVER_IP

echo "✅ OpenVPN setup complete!"
echo ""
echo "📱 Client files created in vpn_profiles/"
echo "📄 Download: demo_openvpn.ovpn"
echo "🔑 Login: <EMAIL> / password"
echo ""
echo "🎯 Users can now connect with any OpenVPN client!"
echo "   - OpenVPN Connect (official)"
echo "   - Tunnelblick (Mac)"
echo "   - OpenVPN GUI (Windows)"
echo "   - Mobile apps"
