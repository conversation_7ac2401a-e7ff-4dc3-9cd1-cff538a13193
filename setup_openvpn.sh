#!/bin/bash

# Quick OpenVPN Setup - Get Connected in 5 Minutes!
# Run this on your server to enable OpenVPN connections

set -e

SERVER_IP="${1:-$(curl -s ifconfig.me)}"
echo "🚀 Setting up OpenVPN on server: $SERVER_IP"

# Install OpenVPN
echo "📦 Installing OpenVPN..."
sudo apt-get update -qq
sudo apt-get install -y openvpn python3-pip

# Install Python dependencies
pip3 install sqlite3

# Setup OpenVPN certificates and config
echo "🔧 Setting up OpenVPN server..."
python3 openvpn_integration.py

# Copy server config to OpenVPN directory
echo "📋 Configuring OpenVPN service..."
sudo cp openvpn/server/server.conf /etc/openvpn/
sudo cp -r openvpn/ca /etc/openvpn/
sudo cp -r openvpn/server/*.{crt,key,pem} /etc/openvpn/ 2>/dev/null || true

# Enable IP forwarding
echo "🌐 Enabling IP forwarding..."
echo 'net.ipv4.ip_forward=1' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# Configure firewall
echo "🔥 Configuring firewall..."
sudo ufw --force reset
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 1194/udp comment "OpenVPN"
sudo ufw allow 8080/tcp comment "VPN API"
sudo ufw --force enable

# Setup NAT for VPN traffic
echo "🔀 Setting up NAT..."
sudo iptables -t nat -A POSTROUTING -s ********/24 -o eth0 -j MASQUERADE
sudo iptables -A INPUT -i tun+ -j ACCEPT
sudo iptables -A FORWARD -i tun+ -j ACCEPT
sudo iptables -A FORWARD -i tun+ -o eth0 -m state --state RELATED,ESTABLISHED -j ACCEPT
sudo iptables -A FORWARD -i eth0 -o tun+ -m state --state RELATED,ESTABLISHED -j ACCEPT

# Save iptables rules
sudo sh -c "iptables-save > /etc/iptables.rules"
echo '#!/bin/sh' | sudo tee /etc/network/if-pre-up.d/iptables
echo 'iptables-restore < /etc/iptables.rules' | sudo tee -a /etc/network/if-pre-up.d/iptables
sudo chmod +x /etc/network/if-pre-up.d/iptables

# Start OpenVPN server
echo "🎯 Starting OpenVPN server..."
sudo systemctl enable openvpn@server
sudo systemctl start openvpn@server

# Wait a moment for service to start
sleep 3

# Check if OpenVPN is running
if sudo systemctl is-active --quiet openvpn@server; then
    echo "✅ OpenVPN server is running!"
else
    echo "⚠️  OpenVPN server may have issues. Checking logs..."
    sudo journalctl -u openvpn@server --no-pager -l
fi

# Create test user and generate profile
echo "👤 Creating test user..."
python3 vpn_manager_tool.py --create-user <EMAIL> --plan premium
python3 vpn_manager_tool.py --generate-profiles <EMAIL> --server $SERVER_IP

echo ""
echo "🎉 OpenVPN setup complete!"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "📱 Your .ovpn file: vpn_profiles/test_openvpn.ovpn"
echo "🔑 Login credentials:"
echo "   Username: <EMAIL>"
echo "   Password: password"
echo ""
echo "📲 Download OpenVPN client:"
echo "   • Windows/Mac: https://openvpn.net/community-downloads/"
echo "   • Mobile: OpenVPN Connect app"
echo ""
echo "🔌 To connect:"
echo "   1. Download test_openvpn.ovpn file"
echo "   2. Import into OpenVPN client"
echo "   3. Enter username/password above"
echo "   4. Connect!"
echo ""
echo "🌐 Server IP: $SERVER_IP"
echo "🔧 OpenVPN Port: 1194/UDP"
