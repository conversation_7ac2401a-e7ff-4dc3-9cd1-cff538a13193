
WireGuard Setup <NAME_EMAIL>
=======================================

STEP 1: Download WireGuard Client
- Windows: https://www.wireguard.com/install/
- Download "WireGuard for Windows"

STEP 2: Import Configuration
1. Install WireGuard client
2. Open WireGuard application
3. Click "Import tunnel(s) from file"
4. Select: admin_wireguard.conf
5. Click "Activate"

STEP 3: Connect
- Toggle the tunnel ON in WireGuard app
- You should see data transfer statistics

Your Plan: enterprise
Profile File: vpn_profiles\admin_wireguard.conf
Client IP: **********
Server: vpn.mycompany.com:51820

ADVANTAGES:
- No username/password needed
- Better performance than OpenVPN
- Modern cryptography
- Lower battery usage on mobile

Support: <EMAIL>
