# Working OpenVPN Profile for Your VPN Server
# User: <EMAIL>
# Server: *************

client
dev tun
proto tcp
remote ************* 8090
resolv-retry infinite
nobind
persist-key
persist-tun

# Authentication
auth-user-pass
auth-nocache

# Security (compatible settings)
cipher AES-256-CBC
auth SHA1

# Disable certificate verification for testing
auth-nocache
verify-x509-name none
remote-cert-tls none

# Logging
verb 3
mute 20

# Windows compatibility
route-method exe
route-delay 2

# DNS
dhcp-option DNS *******
dhcp-option DNS *******

# Disable problematic features for testing
pull-filter ignore "redirect-gateway"
pull-filter ignore "route-gateway"
