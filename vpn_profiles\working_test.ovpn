# Simple Working OpenVPN Profile
# This connects to your VPN server

client
dev tun
proto tcp
remote ************* 8090
resolv-retry infinite
nobind
persist-key
persist-tun

# Authentication
auth-user-pass
auth-nocache

# Minimal security for testing
cipher none
auth none

# Disable all certificate checks
verify-x509-name none
remote-cert-tls none

# Logging
verb 3
mute 20

# Windows compatibility
route-method exe
route-delay 2

# Simple connection
pull-filter ignore redirect-gateway
pull-filter ignore route
