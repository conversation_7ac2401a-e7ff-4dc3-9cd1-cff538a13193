# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/d/secure-vpn

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/d/secure-vpn/build

# Include any dependencies generated for this target.
include CMakeFiles/keygen.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/keygen.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/keygen.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/keygen.dir/flags.make

CMakeFiles/keygen.dir/tools/keygen.c.o: CMakeFiles/keygen.dir/flags.make
CMakeFiles/keygen.dir/tools/keygen.c.o: ../tools/keygen.c
CMakeFiles/keygen.dir/tools/keygen.c.o: CMakeFiles/keygen.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/d/secure-vpn/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/keygen.dir/tools/keygen.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/keygen.dir/tools/keygen.c.o -MF CMakeFiles/keygen.dir/tools/keygen.c.o.d -o CMakeFiles/keygen.dir/tools/keygen.c.o -c /mnt/d/secure-vpn/tools/keygen.c

CMakeFiles/keygen.dir/tools/keygen.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/keygen.dir/tools/keygen.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /mnt/d/secure-vpn/tools/keygen.c > CMakeFiles/keygen.dir/tools/keygen.c.i

CMakeFiles/keygen.dir/tools/keygen.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/keygen.dir/tools/keygen.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /mnt/d/secure-vpn/tools/keygen.c -o CMakeFiles/keygen.dir/tools/keygen.c.s

# Object files for target keygen
keygen_OBJECTS = \
"CMakeFiles/keygen.dir/tools/keygen.c.o"

# External object files for target keygen
keygen_EXTERNAL_OBJECTS =

keygen: CMakeFiles/keygen.dir/tools/keygen.c.o
keygen: CMakeFiles/keygen.dir/build.make
keygen: libsecurevpn_core.a
keygen: CMakeFiles/keygen.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/mnt/d/secure-vpn/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable keygen"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/keygen.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/keygen.dir/build: keygen
.PHONY : CMakeFiles/keygen.dir/build

CMakeFiles/keygen.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/keygen.dir/cmake_clean.cmake
.PHONY : CMakeFiles/keygen.dir/clean

CMakeFiles/keygen.dir/depend:
	cd /mnt/d/secure-vpn/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /mnt/d/secure-vpn /mnt/d/secure-vpn /mnt/d/secure-vpn/build /mnt/d/secure-vpn/build /mnt/d/secure-vpn/build/CMakeFiles/keygen.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/keygen.dir/depend

