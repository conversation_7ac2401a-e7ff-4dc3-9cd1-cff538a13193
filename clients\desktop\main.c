#include "securevpn.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <gtk/gtk.h>

// GTK-based desktop client for Secure VPN
typedef enum {
    VPN_STATE_DISCONNECTED = 0,
    VPN_STATE_CONNECTING = 1,
    VPN_STATE_CONNECTED = 2,
    VPN_STATE_DISCONNECTING = 3,
    VPN_STATE_ERROR = 4
} vpn_state_t;

typedef struct {
    char server_address[256];
    uint16_t server_port;
    char license_key[1024];
    char username[256];
    char password[256];
    bool auto_connect;
    bool start_minimized;
    bool kill_switch;
    char preferred_protocol[32];
} vpn_config_t;

typedef struct {
    vpn_state_t state;
    char status_message[256];
    char connected_server[256];
    char public_ip[64];
    char virtual_ip[64];
    uint64_t bytes_sent;
    uint64_t bytes_received;
    time_t connected_since;
    double connection_speed_mbps;
} vpn_status_t;

// Global application state
typedef struct {
    GtkApplication *app;
    GtkWidget *main_window;
    GtkWidget *status_label;
    GtkWidget *connect_button;
    GtkWidget *server_combo;
    GtkWidget *progress_bar;
    GtkWidget *stats_grid;
    GtkWidget *tray_icon;
    
    vpn_config_t config;
    vpn_status_t status;
    
    pthread_t vpn_thread;
    bool vpn_thread_running;
    bool should_exit;
} app_state_t;

static app_state_t g_app_state = {0};

// Server list
typedef struct {
    char name[64];
    char address[256];
    uint16_t port;
    char country[32];
    char city[32];
    bool premium_only;
} server_info_t;

static const server_info_t g_servers[] = {
    {"US East (New York)", "us-east.securevpn.com", 8443, "United States", "New York", false},
    {"US West (Los Angeles)", "us-west.securevpn.com", 8443, "United States", "Los Angeles", false},
    {"UK (London)", "uk.securevpn.com", 8443, "United Kingdom", "London", false},
    {"Germany (Frankfurt)", "de.securevpn.com", 8443, "Germany", "Frankfurt", true},
    {"Japan (Tokyo)", "jp.securevpn.com", 8443, "Japan", "Tokyo", true},
    {"Australia (Sydney)", "au.securevpn.com", 8443, "Australia", "Sydney", true},
    {"Canada (Toronto)", "ca.securevpn.com", 8443, "Canada", "Toronto", false},
    {"Netherlands (Amsterdam)", "nl.securevpn.com", 8443, "Netherlands", "Amsterdam", true},
    {"Singapore", "sg.securevpn.com", 8443, "Singapore", "Singapore", true},
    {"Brazil (São Paulo)", "br.securevpn.com", 8443, "Brazil", "São Paulo", true}
};

static const size_t g_server_count = sizeof(g_servers) / sizeof(g_servers[0]);

// Function prototypes
static void update_ui_state(void);
static void* vpn_connection_thread(void *arg);
static void on_connect_button_clicked(GtkWidget *widget, gpointer data);
static void on_server_changed(GtkComboBox *combo, gpointer data);
static void on_settings_clicked(GtkWidget *widget, gpointer data);
static void on_about_clicked(GtkWidget *widget, gpointer data);
static gboolean update_stats_timer(gpointer data);

// Load configuration from file
static void load_config(vpn_config_t *config) {
    FILE *file = fopen("vpn_config.conf", "r");
    if (!file) {
        // Set defaults
        strcpy(config->server_address, "demo.securevpn.com");
        config->server_port = 8443;
        config->auto_connect = false;
        config->start_minimized = false;
        config->kill_switch = true;
        strcpy(config->preferred_protocol, "auto");
        return;
    }

    char line[512];
    while (fgets(line, sizeof(line), file)) {
        char key[128], value[384];
        if (sscanf(line, "%127[^=]=%383s", key, value) == 2) {
            if (strcmp(key, "server_address") == 0) {
                strcpy(config->server_address, value);
            } else if (strcmp(key, "server_port") == 0) {
                config->server_port = atoi(value);
            } else if (strcmp(key, "license_key") == 0) {
                strcpy(config->license_key, value);
            } else if (strcmp(key, "username") == 0) {
                strcpy(config->username, value);
            } else if (strcmp(key, "auto_connect") == 0) {
                config->auto_connect = (strcmp(value, "true") == 0);
            } else if (strcmp(key, "start_minimized") == 0) {
                config->start_minimized = (strcmp(value, "true") == 0);
            } else if (strcmp(key, "kill_switch") == 0) {
                config->kill_switch = (strcmp(value, "true") == 0);
            }
        }
    }
    fclose(file);
}

// Save configuration to file
static void save_config(const vpn_config_t *config) {
    FILE *file = fopen("vpn_config.conf", "w");
    if (!file) return;

    fprintf(file, "server_address=%s\n", config->server_address);
    fprintf(file, "server_port=%d\n", config->server_port);
    fprintf(file, "license_key=%s\n", config->license_key);
    fprintf(file, "username=%s\n", config->username);
    fprintf(file, "auto_connect=%s\n", config->auto_connect ? "true" : "false");
    fprintf(file, "start_minimized=%s\n", config->start_minimized ? "true" : "false");
    fprintf(file, "kill_switch=%s\n", config->kill_switch ? "true" : "false");
    fprintf(file, "preferred_protocol=%s\n", config->preferred_protocol);

    fclose(file);
}

// Update UI based on current state
static void update_ui_state(void) {
    const char *status_text;
    const char *button_text;
    gboolean button_sensitive = TRUE;
    double progress = 0.0;

    switch (g_app_state.status.state) {
        case VPN_STATE_DISCONNECTED:
            status_text = "Disconnected";
            button_text = "Connect";
            progress = 0.0;
            break;
        case VPN_STATE_CONNECTING:
            status_text = "Connecting...";
            button_text = "Cancel";
            progress = 0.5;
            break;
        case VPN_STATE_CONNECTED:
            status_text = "Connected";
            button_text = "Disconnect";
            progress = 1.0;
            break;
        case VPN_STATE_DISCONNECTING:
            status_text = "Disconnecting...";
            button_text = "Disconnect";
            button_sensitive = FALSE;
            progress = 0.5;
            break;
        case VPN_STATE_ERROR:
            status_text = g_app_state.status.status_message;
            button_text = "Connect";
            progress = 0.0;
            break;
    }

    gtk_label_set_text(GTK_LABEL(g_app_state.status_label), status_text);
    gtk_button_set_label(GTK_BUTTON(g_app_state.connect_button), button_text);
    gtk_widget_set_sensitive(g_app_state.connect_button, button_sensitive);
    gtk_progress_bar_set_fraction(GTK_PROGRESS_BAR(g_app_state.progress_bar), progress);

    // Update window title
    char title[256];
    if (g_app_state.status.state == VPN_STATE_CONNECTED) {
        snprintf(title, sizeof(title), "Secure VPN - Connected to %s", 
                g_app_state.status.connected_server);
    } else {
        strcpy(title, "Secure VPN");
    }
    gtk_window_set_title(GTK_WINDOW(g_app_state.main_window), title);
}

// VPN connection thread
static void* vpn_connection_thread(void *arg) {
    g_app_state.vpn_thread_running = true;
    g_app_state.status.state = VPN_STATE_CONNECTING;
    
    // Simulate connection process
    for (int i = 0; i < 10 && !g_app_state.should_exit; i++) {
        sleep(1);
        // Update progress
        gdk_threads_add_idle((GSourceFunc)update_ui_state, NULL);
    }

    if (!g_app_state.should_exit) {
        g_app_state.status.state = VPN_STATE_CONNECTED;
        strcpy(g_app_state.status.connected_server, g_app_state.config.server_address);
        strcpy(g_app_state.status.virtual_ip, "********");
        g_app_state.status.connected_since = time(NULL);
        
        gdk_threads_add_idle((GSourceFunc)update_ui_state, NULL);

        // Keep connection alive
        while (g_app_state.status.state == VPN_STATE_CONNECTED && !g_app_state.should_exit) {
            sleep(1);
            // Update statistics
            g_app_state.status.bytes_sent += 1024 + rand() % 10240;
            g_app_state.status.bytes_received += 2048 + rand() % 20480;
        }
    }

    g_app_state.status.state = VPN_STATE_DISCONNECTED;
    g_app_state.vpn_thread_running = false;
    gdk_threads_add_idle((GSourceFunc)update_ui_state, NULL);
    
    return NULL;
}

// Connect button callback
static void on_connect_button_clicked(GtkWidget *widget, gpointer data) {
    if (g_app_state.status.state == VPN_STATE_DISCONNECTED || 
        g_app_state.status.state == VPN_STATE_ERROR) {
        
        // Start connection
        if (!g_app_state.vpn_thread_running) {
            g_app_state.should_exit = false;
            pthread_create(&g_app_state.vpn_thread, NULL, vpn_connection_thread, NULL);
        }
    } else if (g_app_state.status.state == VPN_STATE_CONNECTED) {
        // Disconnect
        g_app_state.status.state = VPN_STATE_DISCONNECTING;
        g_app_state.should_exit = true;
        update_ui_state();
    } else if (g_app_state.status.state == VPN_STATE_CONNECTING) {
        // Cancel connection
        g_app_state.should_exit = true;
    }
}

// Server selection callback
static void on_server_changed(GtkComboBox *combo, gpointer data) {
    gint active = gtk_combo_box_get_active(combo);
    if (active >= 0 && active < g_server_count) {
        strcpy(g_app_state.config.server_address, g_servers[active].address);
        g_app_state.config.server_port = g_servers[active].port;
        save_config(&g_app_state.config);
    }
}

// Statistics update timer
static gboolean update_stats_timer(gpointer data) {
    if (g_app_state.status.state == VPN_STATE_CONNECTED) {
        // Update statistics display
        char stats_text[512];
        
        double mb_sent = g_app_state.status.bytes_sent / (1024.0 * 1024.0);
        double mb_received = g_app_state.status.bytes_received / (1024.0 * 1024.0);
        time_t duration = time(NULL) - g_app_state.status.connected_since;
        
        snprintf(stats_text, sizeof(stats_text),
                "Connected for: %ld:%02ld:%02ld\n"
                "Data sent: %.2f MB\n"
                "Data received: %.2f MB\n"
                "Virtual IP: %s",
                duration / 3600, (duration % 3600) / 60, duration % 60,
                mb_sent, mb_received, g_app_state.status.virtual_ip);
        
        // Update stats display (would need actual GTK label widget)
    }
    
    return TRUE; // Continue timer
}

// Create main window
static void create_main_window(GtkApplication *app) {
    GtkWidget *window;
    GtkWidget *vbox, *hbox;
    GtkWidget *server_label;
    
    // Create main window
    window = gtk_application_window_new(app);
    gtk_window_set_title(GTK_WINDOW(window), "Secure VPN");
    gtk_window_set_default_size(GTK_WINDOW(window), 400, 300);
    gtk_window_set_resizable(GTK_WINDOW(window), FALSE);
    
    g_app_state.main_window = window;
    
    // Create main layout
    vbox = gtk_box_new(GTK_ORIENTATION_VERTICAL, 10);
    gtk_container_set_border_width(GTK_CONTAINER(vbox), 20);
    gtk_container_add(GTK_CONTAINER(window), vbox);
    
    // Server selection
    hbox = gtk_box_new(GTK_ORIENTATION_HORIZONTAL, 10);
    server_label = gtk_label_new("Server:");
    g_app_state.server_combo = gtk_combo_box_text_new();
    
    // Populate server list
    for (size_t i = 0; i < g_server_count; i++) {
        char server_text[128];
        snprintf(server_text, sizeof(server_text), "%s (%s)", 
                g_servers[i].name, g_servers[i].country);
        gtk_combo_box_text_append_text(GTK_COMBO_BOX_TEXT(g_app_state.server_combo), server_text);
    }
    gtk_combo_box_set_active(GTK_COMBO_BOX(g_app_state.server_combo), 0);
    
    g_signal_connect(g_app_state.server_combo, "changed", G_CALLBACK(on_server_changed), NULL);
    
    gtk_box_pack_start(GTK_BOX(hbox), server_label, FALSE, FALSE, 0);
    gtk_box_pack_start(GTK_BOX(hbox), g_app_state.server_combo, TRUE, TRUE, 0);
    gtk_box_pack_start(GTK_BOX(vbox), hbox, FALSE, FALSE, 0);
    
    // Status label
    g_app_state.status_label = gtk_label_new("Disconnected");
    gtk_box_pack_start(GTK_BOX(vbox), g_app_state.status_label, FALSE, FALSE, 0);
    
    // Progress bar
    g_app_state.progress_bar = gtk_progress_bar_new();
    gtk_box_pack_start(GTK_BOX(vbox), g_app_state.progress_bar, FALSE, FALSE, 0);
    
    // Connect button
    g_app_state.connect_button = gtk_button_new_with_label("Connect");
    g_signal_connect(g_app_state.connect_button, "clicked", G_CALLBACK(on_connect_button_clicked), NULL);
    gtk_box_pack_start(GTK_BOX(vbox), g_app_state.connect_button, FALSE, FALSE, 0);
    
    // Show all widgets
    gtk_widget_show_all(window);
    
    // Start statistics timer
    g_timeout_add_seconds(1, update_stats_timer, NULL);
}

// Application activation callback
static void on_activate(GtkApplication *app, gpointer user_data) {
    create_main_window(app);
}

int main(int argc, char *argv[]) {
    // Initialize application state
    memset(&g_app_state, 0, sizeof(g_app_state));
    g_app_state.status.state = VPN_STATE_DISCONNECTED;
    
    // Load configuration
    load_config(&g_app_state.config);
    
    // Create GTK application
    g_app_state.app = gtk_application_new("com.securevpn.client", G_APPLICATION_FLAGS_NONE);
    g_signal_connect(g_app_state.app, "activate", G_CALLBACK(on_activate), NULL);
    
    // Run application
    int status = g_application_run(G_APPLICATION(g_app_state.app), argc, argv);
    
    // Cleanup
    if (g_app_state.vpn_thread_running) {
        g_app_state.should_exit = true;
        pthread_join(g_app_state.vpn_thread, NULL);
    }
    
    g_object_unref(g_app_state.app);
    return status;
}
