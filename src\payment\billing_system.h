#ifndef BILLING_SYSTEM_H
#define BILLING_SYSTEM_H

#include "stripe_integration.h"
#include "securevpn.h"
#include <stdint.h>
#include <stdbool.h>
#include <time.h>

// Comprehensive billing and subscription management system
typedef enum {
    BILLING_SUCCESS = 0,
    BILLING_ERROR_INVALID_PARAM = -1,
    BILLING_ERROR_DATABASE = -2,
    BILLING_ERROR_PAYMENT = -3,
    BILLING_ERROR_SUBSCRIPTION = -4,
    BILLING_ERROR_PRORATION = -5,
    BILLING_ERROR_REFUND = -6,
    BILLING_ERROR_INVOICE = -7,
    BILLING_ERROR_WEBHOOK = -8
} billing_error_t;

typedef enum {
    BILLING_CYCLE_MONTHLY = 0,
    BILLING_CYCLE_QUARTERLY = 1,
    BILLING_CYCLE_YEARLY = 2,
    BILLING_CYCLE_LIFETIME = 3
} billing_cycle_t;

typedef enum {
    INVOICE_STATUS_DRAFT = 0,
    INVOICE_STATUS_OPEN = 1,
    INVOICE_STATUS_PAID = 2,
    INVOICE_STATUS_VOID = 3,
    INVOICE_STATUS_UNCOLLECTIBLE = 4
} invoice_status_t;

typedef enum {
    REFUND_REASON_REQUESTED_BY_CUSTOMER = 0,
    REFUND_REASON_DUPLICATE = 1,
    REFUND_REASON_FRAUDULENT = 2,
    REFUND_REASON_SUBSCRIPTION_CANCELED = 3,
    REFUND_REASON_SERVICE_ISSUE = 4
} refund_reason_t;

// Subscription plan details
typedef struct {
    subscription_plan_t plan_type;
    char plan_name[64];
    char plan_description[256];
    double monthly_price;
    double quarterly_price;
    double yearly_price;
    uint32_t max_connections;
    uint64_t bandwidth_limit_gb;
    bool has_premium_servers;
    bool has_priority_support;
    bool has_kill_switch;
    bool has_split_tunneling;
    char features[1024];  // JSON array of features
} plan_details_t;

// Customer billing information
typedef struct {
    uint64_t user_id;
    char stripe_customer_id[64];
    char email[256];
    char full_name[128];
    char billing_address[512];
    char tax_id[64];
    char currency[4];
    char timezone[64];
    bool tax_exempt;
    double account_balance;
    time_t created_at;
    time_t updated_at;
} billing_customer_t;

// Subscription details
typedef struct {
    uint64_t subscription_id;
    uint64_t user_id;
    char stripe_subscription_id[64];
    subscription_plan_t plan_type;
    billing_cycle_t billing_cycle;
    subscription_status_t status;
    double amount;
    char currency[4];
    time_t current_period_start;
    time_t current_period_end;
    time_t trial_start;
    time_t trial_end;
    time_t canceled_at;
    time_t ended_at;
    bool cancel_at_period_end;
    char cancellation_reason[256];
    uint32_t billing_cycle_anchor;
    double proration_amount;
    time_t created_at;
    time_t updated_at;
} billing_subscription_t;

// Invoice details
typedef struct {
    uint64_t invoice_id;
    uint64_t user_id;
    char stripe_invoice_id[64];
    char invoice_number[32];
    invoice_status_t status;
    double subtotal;
    double tax_amount;
    double discount_amount;
    double total_amount;
    char currency[4];
    time_t period_start;
    time_t period_end;
    time_t due_date;
    time_t paid_at;
    char payment_intent_id[64];
    char description[512];
    char pdf_url[512];
    time_t created_at;
} billing_invoice_t;

// Payment method information
typedef struct {
    char payment_method_id[64];
    uint64_t user_id;
    char type[32];  // "card", "bank_account", "paypal", etc.
    char last_four[8];
    char brand[32];
    char country[4];
    uint32_t exp_month;
    uint32_t exp_year;
    bool is_default;
    time_t created_at;
} payment_method_t;

// Refund information
typedef struct {
    uint64_t refund_id;
    uint64_t user_id;
    char stripe_refund_id[64];
    char payment_intent_id[64];
    double amount;
    char currency[4];
    refund_reason_t reason;
    char description[256];
    bool is_partial;
    time_t created_at;
} billing_refund_t;

// Usage tracking for billing
typedef struct {
    uint64_t user_id;
    time_t period_start;
    time_t period_end;
    uint64_t bytes_transferred;
    uint32_t connection_hours;
    uint32_t device_count;
    uint32_t server_switches;
    double overage_charges;
    char usage_details[1024];  // JSON with detailed usage
} usage_record_t;

// Discount/coupon information
typedef struct {
    char coupon_id[64];
    char coupon_code[32];
    char name[128];
    char description[256];
    double percent_off;
    double amount_off;
    char currency[4];
    uint32_t duration_in_months;
    uint32_t max_redemptions;
    uint32_t times_redeemed;
    time_t valid_from;
    time_t valid_until;
    bool is_active;
    time_t created_at;
} billing_coupon_t;

// Billing system context
typedef struct {
    database_ctx_t *db_ctx;
    stripe_config_t stripe_config;
    plan_details_t *plans;
    uint32_t plan_count;
    
    // Configuration
    char company_name[128];
    char company_address[512];
    char tax_id[64];
    char support_email[128];
    char billing_email[128];
    
    // Webhook handling
    char webhook_endpoint_secret[128];
    
    bool is_initialized;
} billing_system_t;

// Initialize billing system
billing_error_t billing_init(billing_system_t *billing, database_ctx_t *db_ctx,
                            const stripe_config_t *stripe_config);
void billing_cleanup(billing_system_t *billing);

// Customer management
billing_error_t billing_create_customer(billing_system_t *billing, uint64_t user_id,
                                       const char *email, const char *full_name,
                                       billing_customer_t *customer);
billing_error_t billing_get_customer(billing_system_t *billing, uint64_t user_id,
                                    billing_customer_t *customer);
billing_error_t billing_update_customer(billing_system_t *billing,
                                       const billing_customer_t *customer);

// Subscription management
billing_error_t billing_create_subscription(billing_system_t *billing, uint64_t user_id,
                                           subscription_plan_t plan, billing_cycle_t cycle,
                                           const char *payment_method_id,
                                           billing_subscription_t *subscription);
billing_error_t billing_get_subscription(billing_system_t *billing, uint64_t user_id,
                                        billing_subscription_t *subscription);
billing_error_t billing_update_subscription(billing_system_t *billing, uint64_t user_id,
                                           subscription_plan_t new_plan, billing_cycle_t new_cycle);
billing_error_t billing_cancel_subscription(billing_system_t *billing, uint64_t user_id,
                                           bool at_period_end, const char *reason);
billing_error_t billing_reactivate_subscription(billing_system_t *billing, uint64_t user_id);

// Payment method management
billing_error_t billing_add_payment_method(billing_system_t *billing, uint64_t user_id,
                                          const char *payment_method_id, bool set_as_default,
                                          payment_method_t *payment_method);
billing_error_t billing_get_payment_methods(billing_system_t *billing, uint64_t user_id,
                                           payment_method_t *methods, uint32_t max_methods,
                                           uint32_t *count);
billing_error_t billing_remove_payment_method(billing_system_t *billing, uint64_t user_id,
                                             const char *payment_method_id);
billing_error_t billing_set_default_payment_method(billing_system_t *billing, uint64_t user_id,
                                                  const char *payment_method_id);

// Invoice management
billing_error_t billing_create_invoice(billing_system_t *billing, uint64_t user_id,
                                      const char *description, double amount,
                                      billing_invoice_t *invoice);
billing_error_t billing_get_invoices(billing_system_t *billing, uint64_t user_id,
                                    billing_invoice_t *invoices, uint32_t max_invoices,
                                    uint32_t *count);
billing_error_t billing_pay_invoice(billing_system_t *billing, uint64_t invoice_id,
                                   const char *payment_method_id);
billing_error_t billing_void_invoice(billing_system_t *billing, uint64_t invoice_id);

// Refund management
billing_error_t billing_create_refund(billing_system_t *billing, uint64_t user_id,
                                     const char *payment_intent_id, double amount,
                                     refund_reason_t reason, const char *description,
                                     billing_refund_t *refund);
billing_error_t billing_get_refunds(billing_system_t *billing, uint64_t user_id,
                                   billing_refund_t *refunds, uint32_t max_refunds,
                                   uint32_t *count);

// Usage tracking and billing
billing_error_t billing_record_usage(billing_system_t *billing, uint64_t user_id,
                                    uint64_t bytes_transferred, uint32_t connection_minutes);
billing_error_t billing_calculate_overage(billing_system_t *billing, uint64_t user_id,
                                         time_t period_start, time_t period_end,
                                         double *overage_amount);
billing_error_t billing_get_usage_report(billing_system_t *billing, uint64_t user_id,
                                        time_t period_start, time_t period_end,
                                        usage_record_t *usage);

// Coupon and discount management
billing_error_t billing_create_coupon(billing_system_t *billing, const char *code,
                                     const char *name, double percent_off, double amount_off,
                                     uint32_t duration_months, billing_coupon_t *coupon);
billing_error_t billing_apply_coupon(billing_system_t *billing, uint64_t user_id,
                                    const char *coupon_code);
billing_error_t billing_remove_coupon(billing_system_t *billing, uint64_t user_id);

// Webhook handling
billing_error_t billing_handle_webhook(billing_system_t *billing, const char *payload,
                                      const char *signature);
billing_error_t billing_process_subscription_updated(billing_system_t *billing,
                                                    const char *subscription_id);
billing_error_t billing_process_payment_succeeded(billing_system_t *billing,
                                                 const char *payment_intent_id);
billing_error_t billing_process_payment_failed(billing_system_t *billing,
                                              const char *payment_intent_id);
billing_error_t billing_process_invoice_paid(billing_system_t *billing,
                                            const char *invoice_id);

// Reporting and analytics
billing_error_t billing_generate_revenue_report(billing_system_t *billing,
                                               time_t start_date, time_t end_date,
                                               char *report_json, size_t report_size);
billing_error_t billing_get_subscription_metrics(billing_system_t *billing,
                                                char *metrics_json, size_t metrics_size);
billing_error_t billing_calculate_mrr(billing_system_t *billing, double *mrr);
billing_error_t billing_calculate_churn_rate(billing_system_t *billing, time_t period_start,
                                            time_t period_end, double *churn_rate);

// Plan management
billing_error_t billing_get_plan_details(subscription_plan_t plan, plan_details_t *details);
double billing_calculate_plan_price(subscription_plan_t plan, billing_cycle_t cycle);
billing_error_t billing_calculate_proration(billing_system_t *billing, uint64_t user_id,
                                           subscription_plan_t new_plan, double *proration_amount);

// Utility functions
const char* billing_error_string(billing_error_t error);
const char* billing_cycle_string(billing_cycle_t cycle);
const char* invoice_status_string(invoice_status_t status);
const char* refund_reason_string(refund_reason_t reason);
bool billing_is_trial_period(const billing_subscription_t *subscription);
bool billing_is_subscription_active(const billing_subscription_t *subscription);
time_t billing_calculate_next_billing_date(const billing_subscription_t *subscription);

#endif // BILLING_SYSTEM_H
