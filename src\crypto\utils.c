#include <stdio.h>
#include "securevpn.h"
#include <sys/random.h>
#include <time.h>
#include <string.h>

void secure_random(uint8_t *buffer, size_t len) {
    if (!buffer || len == 0) {
        return;
    }
    
    // Use getrandom() on Linux for cryptographically secure random bytes
    ssize_t result = getrandom(buffer, len, 0);
    if (result < 0 || (size_t)result != len) {
        // Fallback to reading from /dev/urandom
        FILE *urandom = fopen("/dev/urandom", "rb");
        if (urandom) {
            fread(buffer, 1, len, urandom);
            fclose(urandom);
        }
    }
}

void secure_zero(void *ptr, size_t len) {
    if (!ptr || len == 0) {
        return;
    }
    
    // Use explicit_bzero if available, otherwise volatile to prevent optimization
    volatile uint8_t *p = (volatile uint8_t *)ptr;
    while (len--) {
        *p++ = 0;
    }
}

uint64_t get_timestamp_ms(void) {
    struct timespec ts;
    clock_gettime(CLOCK_REALTIME, &ts);
    return (uint64_t)ts.tv_sec * 1000 + ts.tv_nsec / 1000000;
}
