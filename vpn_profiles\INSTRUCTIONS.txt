🎯 Quick OpenVPN Test Setup
============================

✅ Your .ovpn file is ready: vpn_profiles/simple_test.ovpn

📱 To test:
1. Download OpenVPN Connect: https://openvpn.net/client/
2. Import: simple_test.ovpn
3. Login:
   Username: test
   Password: test
4. Connect!

🌐 Server: 192.168.10.22:1194

⚠️  Note: This is a basic config for testing.
   For production, use proper certificates.

🚀 Next: Start your VPN server to accept connections.
