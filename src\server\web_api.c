#include "securevpn.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <pthread.h>
#include <cjson/cJSON.h>

// Simple HTTP server for API endpoints
typedef struct {
    int socket_fd;
    struct sockaddr_in client_addr;
    socklen_t client_addr_len;
} http_client_t;

typedef struct {
    char method[16];
    char path[256];
    char body[4096];
    size_t body_length;
} http_request_t;

typedef struct {
    int status_code;
    char content_type[64];
    char body[4096];
    size_t body_length;
} http_response_t;

// External license manager functions
extern svpn_error_t license_manager_init(const char *db_path, const char *private_key_file, 
                                        const char *public_key_file);
extern svpn_error_t license_manager_create_user(const char *email, const char *password, 
                                               subscription_plan_t plan, user_account_t *user);
extern svpn_error_t license_manager_authenticate_user(const char *email, const char *password, 
                                                     user_account_t *user);
extern svpn_error_t license_manager_generate_license(uint64_t user_id, const char *device_name,
                                                    time_t duration, char *license_key_out, 
                                                    size_t license_key_size);

static int api_server_socket = -1;
static bool api_server_running = false;

// Parse HTTP request
static int parse_http_request(const char *request_data, http_request_t *request) {
    if (!request_data || !request) {
        return -1;
    }

    memset(request, 0, sizeof(http_request_t));

    // Parse request line
    char *line_end = strstr(request_data, "\r\n");
    if (!line_end) {
        return -1;
    }

    char request_line[512];
    size_t line_length = line_end - request_data;
    if (line_length >= sizeof(request_line)) {
        return -1;
    }

    strncpy(request_line, request_data, line_length);
    request_line[line_length] = '\0';

    // Parse method and path
    char *space1 = strchr(request_line, ' ');
    if (!space1) {
        return -1;
    }

    *space1 = '\0';
    strncpy(request->method, request_line, sizeof(request->method) - 1);

    char *space2 = strchr(space1 + 1, ' ');
    if (!space2) {
        return -1;
    }

    *space2 = '\0';
    strncpy(request->path, space1 + 1, sizeof(request->path) - 1);

    // Find body
    char *body_start = strstr(request_data, "\r\n\r\n");
    if (body_start) {
        body_start += 4;
        size_t body_length = strlen(body_start);
        if (body_length < sizeof(request->body)) {
            strcpy(request->body, body_start);
            request->body_length = body_length;
        }
    }

    return 0;
}

// Send HTTP response
static void send_http_response(int client_socket, http_response_t *response) {
    char header[512];
    snprintf(header, sizeof(header),
             "HTTP/1.1 %d OK\r\n"
             "Content-Type: %s\r\n"
             "Content-Length: %zu\r\n"
             "Access-Control-Allow-Origin: *\r\n"
             "Access-Control-Allow-Methods: GET, POST, OPTIONS\r\n"
             "Access-Control-Allow-Headers: Content-Type\r\n"
             "\r\n",
             response->status_code, response->content_type, response->body_length);

    send(client_socket, header, strlen(header), 0);
    if (response->body_length > 0) {
        send(client_socket, response->body, response->body_length, 0);
    }
}

// API endpoint: Create user account
static void api_create_user(http_request_t *request, http_response_t *response) {
    cJSON *json = cJSON_Parse(request->body);
    if (!json) {
        response->status_code = 400;
        strcpy(response->content_type, "application/json");
        strcpy(response->body, "{\"error\":\"Invalid JSON\"}");
        response->body_length = strlen(response->body);
        return;
    }

    cJSON *email_json = cJSON_GetObjectItem(json, "email");
    cJSON *password_json = cJSON_GetObjectItem(json, "password");
    cJSON *plan_json = cJSON_GetObjectItem(json, "plan");

    if (!email_json || !password_json || !plan_json) {
        cJSON_Delete(json);
        response->status_code = 400;
        strcpy(response->content_type, "application/json");
        strcpy(response->body, "{\"error\":\"Missing required fields\"}");
        response->body_length = strlen(response->body);
        return;
    }

    const char *email = cJSON_GetStringValue(email_json);
    const char *password = cJSON_GetStringValue(password_json);
    const char *plan_str = cJSON_GetStringValue(plan_json);

    subscription_plan_t plan = db_plan_from_string(plan_str);
    user_account_t user;

    svpn_error_t result = license_manager_create_user(email, password, plan, &user);

    cJSON *response_json = cJSON_CreateObject();
    if (result == SVPN_SUCCESS) {
        response->status_code = 201;
        cJSON_AddNumberToObject(response_json, "user_id", user.user_id);
        cJSON_AddStringToObject(response_json, "email", user.email);
        cJSON_AddStringToObject(response_json, "plan", db_plan_to_string(user.plan));
        cJSON_AddNumberToObject(response_json, "max_connections", user.max_connections);
    } else {
        response->status_code = 400;
        cJSON_AddStringToObject(response_json, "error", "Failed to create user");
    }

    char *json_string = cJSON_Print(response_json);
    strcpy(response->content_type, "application/json");
    strncpy(response->body, json_string, sizeof(response->body) - 1);
    response->body_length = strlen(response->body);

    free(json_string);
    cJSON_Delete(response_json);
    cJSON_Delete(json);
}

// API endpoint: Authenticate user
static void api_authenticate_user(http_request_t *request, http_response_t *response) {
    cJSON *json = cJSON_Parse(request->body);
    if (!json) {
        response->status_code = 400;
        strcpy(response->content_type, "application/json");
        strcpy(response->body, "{\"error\":\"Invalid JSON\"}");
        response->body_length = strlen(response->body);
        return;
    }

    cJSON *email_json = cJSON_GetObjectItem(json, "email");
    cJSON *password_json = cJSON_GetObjectItem(json, "password");

    if (!email_json || !password_json) {
        cJSON_Delete(json);
        response->status_code = 400;
        strcpy(response->content_type, "application/json");
        strcpy(response->body, "{\"error\":\"Missing email or password\"}");
        response->body_length = strlen(response->body);
        return;
    }

    const char *email = cJSON_GetStringValue(email_json);
    const char *password = cJSON_GetStringValue(password_json);

    user_account_t user;
    svpn_error_t result = license_manager_authenticate_user(email, password, &user);

    cJSON *response_json = cJSON_CreateObject();
    if (result == SVPN_SUCCESS) {
        response->status_code = 200;
        cJSON_AddNumberToObject(response_json, "user_id", user.user_id);
        cJSON_AddStringToObject(response_json, "email", user.email);
        cJSON_AddStringToObject(response_json, "plan", db_plan_to_string(user.plan));
        cJSON_AddNumberToObject(response_json, "max_connections", user.max_connections);
        cJSON_AddNumberToObject(response_json, "subscription_expires", user.subscription_expires);
    } else {
        response->status_code = 401;
        cJSON_AddStringToObject(response_json, "error", "Authentication failed");
    }

    char *json_string = cJSON_Print(response_json);
    strcpy(response->content_type, "application/json");
    strncpy(response->body, json_string, sizeof(response->body) - 1);
    response->body_length = strlen(response->body);

    free(json_string);
    cJSON_Delete(response_json);
    cJSON_Delete(json);
}

// API endpoint: Generate license
static void api_generate_license(http_request_t *request, http_response_t *response) {
    cJSON *json = cJSON_Parse(request->body);
    if (!json) {
        response->status_code = 400;
        strcpy(response->content_type, "application/json");
        strcpy(response->body, "{\"error\":\"Invalid JSON\"}");
        response->body_length = strlen(response->body);
        return;
    }

    cJSON *user_id_json = cJSON_GetObjectItem(json, "user_id");
    cJSON *device_name_json = cJSON_GetObjectItem(json, "device_name");
    cJSON *duration_json = cJSON_GetObjectItem(json, "duration");

    if (!user_id_json || !device_name_json) {
        cJSON_Delete(json);
        response->status_code = 400;
        strcpy(response->content_type, "application/json");
        strcpy(response->body, "{\"error\":\"Missing required fields\"}");
        response->body_length = strlen(response->body);
        return;
    }

    uint64_t user_id = (uint64_t)cJSON_GetNumberValue(user_id_json);
    const char *device_name = cJSON_GetStringValue(device_name_json);
    time_t duration = duration_json ? (time_t)cJSON_GetNumberValue(duration_json) : 30 * 24 * 3600; // 30 days default

    char license_key[1024];
    svpn_error_t result = license_manager_generate_license(user_id, device_name, duration, 
                                                          license_key, sizeof(license_key));

    cJSON *response_json = cJSON_CreateObject();
    if (result == SVPN_SUCCESS) {
        response->status_code = 200;
        cJSON_AddStringToObject(response_json, "license_key", license_key);
        cJSON_AddNumberToObject(response_json, "expires_in", duration);
    } else {
        response->status_code = 400;
        cJSON_AddStringToObject(response_json, "error", "Failed to generate license");
    }

    char *json_string = cJSON_Print(response_json);
    strcpy(response->content_type, "application/json");
    strncpy(response->body, json_string, sizeof(response->body) - 1);
    response->body_length = strlen(response->body);

    free(json_string);
    cJSON_Delete(response_json);
    cJSON_Delete(json);
}
