@echo off
REM Quick OpenVPN Setup for Local PC
REM Run as Administrator

echo 🚀 Setting up OpenVPN on local PC...

REM Check if running as admin
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ Please run as Administrator
    pause
    exit /b 1
)

REM Install OpenVPN if not present
where openvpn >nul 2>&1
if %errorLevel% neq 0 (
    echo 📦 Installing OpenVPN...
    if exist "C:\Program Files\OpenVPN\bin\openvpn.exe" (
        echo ✅ OpenVPN already installed
    ) else (
        echo ❌ Please install OpenVPN from: https://openvpn.net/community-downloads/
        pause
        exit /b 1
    )
)

REM Setup OpenVPN certificates
echo 🔧 Setting up certificates...
python openvpn_integration.py

REM Copy server config
echo 📋 Configuring OpenVPN...
if not exist "C:\Program Files\OpenVPN\config" mkdir "C:\Program Files\OpenVPN\config"
copy "openvpn\server\server.conf" "C:\Program Files\OpenVPN\config\"
xcopy "openvpn\ca" "C:\Program Files\OpenVPN\config\ca\" /E /I /Y
xcopy "openvpn\server\*.crt" "C:\Program Files\OpenVPN\config\" /Y
xcopy "openvpn\server\*.key" "C:\Program Files\OpenVPN\config\" /Y
xcopy "openvpn\server\*.pem" "C:\Program Files\OpenVPN\config\" /Y

REM Enable IP forwarding (Windows)
echo 🌐 Enabling IP forwarding...
reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters" /v IPEnableRouter /t REG_DWORD /d 1 /f

REM Configure Windows Firewall
echo 🔥 Configuring Windows Firewall...
netsh advfirewall firewall add rule name="OpenVPN" dir=in action=allow protocol=UDP localport=1194
netsh advfirewall firewall add rule name="VPN API" dir=in action=allow protocol=TCP localport=8080

REM Start OpenVPN service
echo 🎯 Starting OpenVPN service...
net start OpenVPNService

REM Create test user
echo 👤 Creating test user...
python vpn_manager_tool.py --create-user <EMAIL> --plan premium
python vpn_manager_tool.py --generate-profiles <EMAIL> --server localhost

echo.
echo ✅ Local OpenVPN setup complete!
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo 📱 Your .ovpn file: vpn_profiles\test_openvpn.ovpn
echo 🔑 Login: <EMAIL> / password
echo 🌐 Server: localhost:1194
echo.
echo 🔌 To test connection:
echo 1. Open another OpenVPN client on same PC
echo 2. Import test_openvpn.ovpn
echo 3. Connect with credentials above
echo.
pause
