#!/usr/bin/env python3
"""
Complete Windows OpenVPN Setup
This will get OpenVPN fully working on your Windows PC
"""

import os
import sys
import subprocess
import urllib.request
import zipfile
import shutil
from pathlib import Path
import time

def is_admin():
    """Check if running as administrator"""
    try:
        import ctypes
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """Restart script as administrator"""
    if is_admin():
        return True
    else:
        print("🔐 Requesting administrator privileges...")
        try:
            import ctypes
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            return False
        except:
            print("❌ Failed to get admin privileges")
            return False

def download_openvpn():
    """Download and install OpenVPN"""
    print("📥 Downloading OpenVPN...")
    
    # OpenVPN download URL (latest community version)
    url = "https://swupdate.openvpn.org/community/releases/OpenVPN-2.6.8-I001-amd64.msi"
    installer_path = "OpenVPN-installer.msi"
    
    try:
        urllib.request.urlretrieve(url, installer_path)
        print("✅ OpenVPN downloaded")
        
        # Install silently
        print("🔧 Installing OpenVPN...")
        subprocess.run([
            "msiexec", "/i", installer_path, "/quiet", "/norestart"
        ], check=True)
        
        # Clean up
        os.remove(installer_path)
        print("✅ OpenVPN installed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Failed to download/install OpenVPN: {e}")
        return False

def setup_openvpn_service():
    """Setup OpenVPN as Windows service"""
    print("🔧 Setting up OpenVPN service...")
    
    openvpn_path = r"C:\Program Files\OpenVPN\bin\openvpn.exe"
    config_dir = r"C:\Program Files\OpenVPN\config"
    
    if not os.path.exists(openvpn_path):
        print("❌ OpenVPN not found after installation")
        return False
    
    # Create config directory if it doesn't exist
    os.makedirs(config_dir, exist_ok=True)
    
    # Add OpenVPN to PATH
    current_path = os.environ.get("PATH", "")
    openvpn_bin = r"C:\Program Files\OpenVPN\bin"
    if openvpn_bin not in current_path:
        os.environ["PATH"] = openvpn_bin + ";" + current_path
    
    print("✅ OpenVPN service configured")
    return True

def create_openvpn_config():
    """Create OpenVPN server configuration"""
    print("🔧 Creating OpenVPN configuration...")
    
    # Setup certificates first
    try:
        from openvpn_integration import OpenVPNManager
        manager = OpenVPNManager()
        
        # Get local IP
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        
        # Setup complete server
        manager.setup_complete_server(local_ip, 1194)
        print("✅ OpenVPN certificates and config created")
        
        # Create test user
        manager.create_user_profile("<EMAIL>", local_ip)
        print("✅ Test user profile created")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create OpenVPN config: {e}")
        return False

def configure_windows_firewall():
    """Configure Windows Firewall for OpenVPN"""
    print("🔥 Configuring Windows Firewall...")
    
    try:
        # Allow OpenVPN through firewall
        subprocess.run([
            "netsh", "advfirewall", "firewall", "add", "rule",
            "name=OpenVPN Server", "dir=in", "action=allow",
            "protocol=UDP", "localport=1194"
        ], check=True, capture_output=True)
        
        subprocess.run([
            "netsh", "advfirewall", "firewall", "add", "rule",
            "name=VPN API", "dir=in", "action=allow",
            "protocol=TCP", "localport=8080"
        ], check=True, capture_output=True)
        
        print("✅ Windows Firewall configured")
        return True
        
    except Exception as e:
        print(f"❌ Failed to configure firewall: {e}")
        return False

def enable_ip_forwarding():
    """Enable IP forwarding on Windows"""
    print("🌐 Enabling IP forwarding...")
    
    try:
        # Enable IP forwarding in registry
        subprocess.run([
            "reg", "add",
            "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters",
            "/v", "IPEnableRouter", "/t", "REG_DWORD", "/d", "1", "/f"
        ], check=True, capture_output=True)
        
        print("✅ IP forwarding enabled")
        return True
        
    except Exception as e:
        print(f"❌ Failed to enable IP forwarding: {e}")
        return False

def start_openvpn_server():
    """Start OpenVPN server"""
    print("🎯 Starting OpenVPN server...")
    
    config_file = "openvpn/server/server.conf"
    if not os.path.exists(config_file):
        print("❌ Server config not found")
        return False
    
    try:
        # Change to server directory
        server_dir = Path("openvpn/server")
        os.chdir(server_dir)
        
        # Start OpenVPN server
        openvpn_path = r"C:\Program Files\OpenVPN\bin\openvpn.exe"
        
        print("🚀 OpenVPN server starting...")
        print("📋 Config: server.conf")
        print("🔌 Port: 1194/UDP")
        print("🛑 Press Ctrl+C to stop")
        
        subprocess.run([openvpn_path, "--config", "server.conf"])
        
    except KeyboardInterrupt:
        print("\n🛑 OpenVPN server stopped")
    except Exception as e:
        print(f"❌ Failed to start OpenVPN server: {e}")
        return False
    
    return True

def main():
    """Main setup function"""
    print("🚀 Complete Windows OpenVPN Setup")
    print("=" * 50)
    
    # Check if we need admin privileges
    if not is_admin():
        print("🔐 Administrator privileges required for installation")
        if not run_as_admin():
            return
        sys.exit(0)
    
    print("✅ Running as administrator")
    
    # Step 1: Download and install OpenVPN
    openvpn_path = r"C:\Program Files\OpenVPN\bin\openvpn.exe"
    if not os.path.exists(openvpn_path):
        if not download_openvpn():
            print("❌ OpenVPN installation failed")
            return
    else:
        print("✅ OpenVPN already installed")
    
    # Step 2: Setup OpenVPN service
    if not setup_openvpn_service():
        return
    
    # Step 3: Configure firewall
    if not configure_windows_firewall():
        return
    
    # Step 4: Enable IP forwarding
    if not enable_ip_forwarding():
        return
    
    # Step 5: Create OpenVPN configuration
    if not create_openvpn_config():
        return
    
    print("\n🎉 OpenVPN setup complete!")
    print("=" * 50)
    print("📱 Client file: vpn_profiles/test_openvpn.ovpn")
    print("🔑 Username: <EMAIL>")
    print("🔑 Password: password")
    print("\n🚀 To start OpenVPN server:")
    print("   python windows_openvpn_setup.py --start")
    print("\n📱 To test connection:")
    print("   1. Download OpenVPN Connect app")
    print("   2. Import vpn_profiles/test_openvpn.ovpn")
    print("   3. Enter credentials above")
    print("   4. Connect!")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--start":
        start_openvpn_server()
    else:
        main()
