#!/usr/bin/env python3
"""
Freemium Manager - Handle tier limitations and upgrades
"""

import sqlite3
import datetime
import json
from pathlib import Path

class FreemiumManager:
    def __init__(self, db_path="vpn_production.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize freemium tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Add tier and usage columns to users table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_tiers (
            user_id INTEGER PRIMARY KEY,
            tier TEXT DEFAULT 'free',
            tier_start_date TEXT,
            tier_end_date TEXT,
            monthly_bandwidth_used INTEGER DEFAULT 0,
            monthly_bandwidth_limit INTEGER DEFAULT 10737418240,
            device_count INTEGER DEFAULT 0,
            device_limit INTEGER DEFAULT 1,
            last_reset_date TEXT,
            FOREIGN KEY (user_id) REFERENCES users (user_id)
        )
        ''')
        
        # Usage tracking
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS usage_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            date TEXT,
            bandwidth_used INTEGER,
            connection_time INTEGER,
            device_info TEXT,
            FOREIGN KEY (user_id) REFERENCES users (user_id)
        )
        ''')
        
        conn.commit()
        conn.close()
    
    def get_tier_limits(self, tier):
        """Get limits for each tier"""
        limits = {
            'free': {
                'bandwidth_limit': 10 * 1024 * 1024 * 1024,  # 10GB
                'device_limit': 1,
                'features': ['basic_vpn'],
                'support': 'community'
            },
            'bronze': {
                'bandwidth_limit': 100 * 1024 * 1024 * 1024,  # 100GB
                'device_limit': 3,
                'features': ['basic_vpn', 'professional_client', 'kill_switch'],
                'support': 'email'
            },
            'silver': {
                'bandwidth_limit': -1,  # Unlimited
                'device_limit': 10,
                'features': ['basic_vpn', 'professional_client', 'kill_switch', 'mobile_apps', 'cloud_deployment'],
                'support': 'priority_email'
            },
            'gold': {
                'bandwidth_limit': -1,  # Unlimited
                'device_limit': 50,
                'features': ['basic_vpn', 'professional_client', 'kill_switch', 'mobile_apps', 'cloud_deployment', 'team_management', 'analytics'],
                'support': 'phone'
            },
            'platinum': {
                'bandwidth_limit': -1,  # Unlimited
                'device_limit': -1,  # Unlimited
                'features': ['all'],
                'support': '24_7_dedicated'
            }
        }
        return limits.get(tier, limits['free'])
    
    def check_user_limits(self, user_id):
        """Check if user is within their tier limits"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get user tier info
        cursor.execute('''
        SELECT tier, monthly_bandwidth_used, monthly_bandwidth_limit, 
               device_count, device_limit, last_reset_date
        FROM user_tiers WHERE user_id = ?
        ''', (user_id,))
        
        result = cursor.fetchone()
        conn.close()
        
        if not result:
            # Create default free tier for new user
            self.set_user_tier(user_id, 'free')
            return self.check_user_limits(user_id)
        
        tier, bandwidth_used, bandwidth_limit, device_count, device_limit, last_reset = result
        
        # Check if we need to reset monthly usage
        today = datetime.date.today()
        if last_reset:
            last_reset_date = datetime.datetime.strptime(last_reset, '%Y-%m-%d').date()
            if today.month != last_reset_date.month:
                self.reset_monthly_usage(user_id)
                bandwidth_used = 0
        
        # Check limits
        limits_exceeded = []
        
        if bandwidth_limit > 0 and bandwidth_used >= bandwidth_limit:
            limits_exceeded.append('bandwidth')
        
        if device_limit > 0 and device_count >= device_limit:
            limits_exceeded.append('devices')
        
        return {
            'tier': tier,
            'limits_exceeded': limits_exceeded,
            'bandwidth_used': bandwidth_used,
            'bandwidth_limit': bandwidth_limit,
            'bandwidth_remaining': max(0, bandwidth_limit - bandwidth_used) if bandwidth_limit > 0 else -1,
            'device_count': device_count,
            'device_limit': device_limit,
            'can_connect': len(limits_exceeded) == 0
        }
    
    def set_user_tier(self, user_id, tier, duration_months=1):
        """Set user's tier"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        limits = self.get_tier_limits(tier)
        start_date = datetime.date.today()
        end_date = start_date + datetime.timedelta(days=30 * duration_months) if tier != 'free' else None
        
        cursor.execute('''
        INSERT OR REPLACE INTO user_tiers 
        (user_id, tier, tier_start_date, tier_end_date, monthly_bandwidth_limit, device_limit, last_reset_date)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (user_id, tier, start_date.isoformat(), 
              end_date.isoformat() if end_date else None,
              limits['bandwidth_limit'], limits['device_limit'], start_date.isoformat()))
        
        conn.commit()
        conn.close()
    
    def log_usage(self, user_id, bandwidth_used, connection_time, device_info):
        """Log user usage"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        today = datetime.date.today().isoformat()
        
        # Log usage
        cursor.execute('''
        INSERT INTO usage_logs (user_id, date, bandwidth_used, connection_time, device_info)
        VALUES (?, ?, ?, ?, ?)
        ''', (user_id, today, bandwidth_used, connection_time, json.dumps(device_info)))
        
        # Update monthly usage
        cursor.execute('''
        UPDATE user_tiers 
        SET monthly_bandwidth_used = monthly_bandwidth_used + ?
        WHERE user_id = ?
        ''', (bandwidth_used, user_id))
        
        conn.commit()
        conn.close()
    
    def reset_monthly_usage(self, user_id):
        """Reset monthly usage counters"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        today = datetime.date.today().isoformat()
        
        cursor.execute('''
        UPDATE user_tiers 
        SET monthly_bandwidth_used = 0, last_reset_date = ?
        WHERE user_id = ?
        ''', (today, user_id))
        
        conn.commit()
        conn.close()
    
    def get_upgrade_suggestions(self, user_id):
        """Get upgrade suggestions based on usage"""
        limits = self.check_user_limits(user_id)
        suggestions = []
        
        if 'bandwidth' in limits['limits_exceeded']:
            if limits['tier'] == 'free':
                suggestions.append({
                    'reason': 'bandwidth_limit',
                    'message': "You've used your 10GB monthly limit. Upgrade to Bronze for 100GB!",
                    'suggested_tier': 'bronze',
                    'price': '$9.99/month'
                })
            elif limits['tier'] == 'bronze':
                suggestions.append({
                    'reason': 'bandwidth_limit',
                    'message': "You've used your 100GB limit. Upgrade to Silver for unlimited bandwidth!",
                    'suggested_tier': 'silver',
                    'price': '$19.99/month'
                })
        
        if 'devices' in limits['limits_exceeded']:
            if limits['tier'] == 'free':
                suggestions.append({
                    'reason': 'device_limit',
                    'message': "You can only connect 1 device on Free. Upgrade to Bronze for 3 devices!",
                    'suggested_tier': 'bronze',
                    'price': '$9.99/month'
                })
            elif limits['tier'] == 'bronze':
                suggestions.append({
                    'reason': 'device_limit',
                    'message': "You've reached your 3 device limit. Upgrade to Silver for 10 devices!",
                    'suggested_tier': 'silver',
                    'price': '$19.99/month'
                })
        
        return suggestions
    
    def can_use_feature(self, user_id, feature):
        """Check if user can use a specific feature"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT tier FROM user_tiers WHERE user_id = ?', (user_id,))
        result = cursor.fetchone()
        conn.close()
        
        if not result:
            return False
        
        tier = result[0]
        limits = self.get_tier_limits(tier)
        
        return feature in limits['features'] or 'all' in limits['features']

# Integration with VPN client
def create_freemium_vpn_client():
    """Create VPN client with freemium restrictions"""
    
    freemium_client_code = '''
import tkinter as tk
from tkinter import messagebox
from freemium_manager import FreemiumManager

class FreemiumVPNClient:
    def __init__(self):
        self.freemium = FreemiumManager()
        self.user_id = 1  # Get from login
        
        # Check limits before connecting
        limits = self.freemium.check_user_limits(self.user_id)
        
        if not limits['can_connect']:
            self.show_upgrade_dialog(limits)
        else:
            self.show_client_ui(limits)
    
    def show_upgrade_dialog(self, limits):
        """Show upgrade dialog when limits exceeded"""
        suggestions = self.freemium.get_upgrade_suggestions(self.user_id)
        
        if suggestions:
            suggestion = suggestions[0]
            
            result = messagebox.askyesno(
                "Upgrade Required",
                f"{suggestion['message']}\\n\\n"
                f"Upgrade to {suggestion['suggested_tier'].title()} for {suggestion['price']}?",
                icon='question'
            )
            
            if result:
                self.open_upgrade_page(suggestion['suggested_tier'])
    
    def show_client_ui(self, limits):
        """Show client UI with tier-appropriate features"""
        root = tk.Tk()
        root.title(f"VPN Client - {limits['tier'].title()} Tier")
        
        # Show usage info
        if limits['bandwidth_limit'] > 0:
            usage_pct = (limits['bandwidth_used'] / limits['bandwidth_limit']) * 100
            tk.Label(root, text=f"Bandwidth: {usage_pct:.1f}% used").pack()
        
        # Show features based on tier
        if self.freemium.can_use_feature(self.user_id, 'professional_client'):
            tk.Button(root, text="Professional Features Available").pack()
        else:
            tk.Button(root, text="Upgrade for Professional Features", 
                     command=lambda: self.open_upgrade_page('bronze')).pack()
        
        root.mainloop()
    
    def open_upgrade_page(self, tier):
        """Open upgrade page"""
        import webbrowser
        webbrowser.open(f"https://vpn-in-a-box.com/upgrade?tier={tier}")

if __name__ == "__main__":
    client = FreemiumVPNClient()
    '''
    
    with open("freemium_vpn_client.py", "w") as f:
        f.write(freemium_client_code)
    
    print("✅ Freemium VPN client created!")

if __name__ == "__main__":
    # Demo the freemium system
    fm = FreemiumManager()
    
    # Create a test user
    user_id = 1
    fm.set_user_tier(user_id, 'free')
    
    # Check limits
    limits = fm.check_user_limits(user_id)
    print(f"User limits: {limits}")
    
    # Simulate usage that exceeds free tier
    fm.log_usage(user_id, 11 * 1024 * 1024 * 1024, 3600, {"device": "laptop"})  # 11GB
    
    # Check limits again
    limits = fm.check_user_limits(user_id)
    print(f"After heavy usage: {limits}")
    
    # Get upgrade suggestions
    suggestions = fm.get_upgrade_suggestions(user_id)
    print(f"Upgrade suggestions: {suggestions}")
    
    create_freemium_vpn_client()
