#!/bin/bash

# Secure VPN Linux Installation Script
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
INSTALL_DIR="/opt/securevpn"
SERVICE_NAME="securevpn"
DATA_DIR="/var/lib/securevpn"
CONFIG_DIR="/etc/securevpn"
LOG_DIR="/var/log/securevpn"
USER="securevpn"
GROUP="securevpn"

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

# Error handling
error_exit() {
    log_error "ERROR: $1"
    exit 1
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error_exit "This script must be run as root (use sudo)"
    fi
}

# Detect Linux distribution
detect_distro() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        DISTRO=$ID
        VERSION=$VERSION_ID
    else
        error_exit "Cannot detect Linux distribution"
    fi
    
    log "Detected distribution: $DISTRO $VERSION"
}

# Install dependencies
install_dependencies() {
    log "Installing dependencies..."
    
    case $DISTRO in
        ubuntu|debian)
            apt-get update
            apt-get install -y \
                libsqlite3-0 \
                libcjson1 \
                libcurl4 \
                libssl3 \
                iptables \
                iproute2 \
                systemd \
                curl \
                wget \
                ca-certificates
            ;;
        centos|rhel|fedora)
            if command -v dnf >/dev/null 2>&1; then
                dnf install -y \
                    sqlite \
                    libcjson \
                    libcurl \
                    openssl \
                    iptables \
                    iproute \
                    systemd \
                    curl \
                    wget \
                    ca-certificates
            else
                yum install -y \
                    sqlite \
                    libcjson \
                    libcurl \
                    openssl \
                    iptables \
                    iproute \
                    systemd \
                    curl \
                    wget \
                    ca-certificates
            fi
            ;;
        arch)
            pacman -Sy --noconfirm \
                sqlite \
                cjson \
                curl \
                openssl \
                iptables \
                iproute2 \
                systemd \
                wget \
                ca-certificates
            ;;
        *)
            log_warning "Unknown distribution, attempting to continue..."
            ;;
    esac
    
    log_success "Dependencies installed successfully"
}

# Create user and group
create_user() {
    log "Creating user and group..."
    
    if ! getent group $GROUP >/dev/null 2>&1; then
        groupadd --system $GROUP
        log_success "Created group: $GROUP"
    else
        log "Group $GROUP already exists"
    fi
    
    if ! getent passwd $USER >/dev/null 2>&1; then
        useradd --system --gid $GROUP --home-dir $DATA_DIR \
                --shell /bin/false --comment "Secure VPN Service" $USER
        log_success "Created user: $USER"
    else
        log "User $USER already exists"
    fi
}

# Create directories
create_directories() {
    log "Creating directories..."
    
    mkdir -p "$INSTALL_DIR"/{bin,lib,share}
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$DATA_DIR"/{database,ssl,licenses}
    mkdir -p "$LOG_DIR"
    
    # Set ownership and permissions
    chown -R $USER:$GROUP "$DATA_DIR"
    chown -R $USER:$GROUP "$LOG_DIR"
    chown -R root:$GROUP "$CONFIG_DIR"
    
    chmod 755 "$INSTALL_DIR"
    chmod 750 "$CONFIG_DIR"
    chmod 750 "$DATA_DIR"
    chmod 750 "$LOG_DIR"
    
    log_success "Directories created successfully"
}

# Install binaries
install_binaries() {
    log "Installing binaries..."
    
    # Copy binaries
    cp svpn_enhanced_server "$INSTALL_DIR/bin/"
    cp svpn_web_api "$INSTALL_DIR/bin/"
    cp vpn_manager "$INSTALL_DIR/bin/"
    cp keygen "$INSTALL_DIR/bin/"
    
    if [[ -f svpn_gui_client ]]; then
        cp svpn_gui_client "$INSTALL_DIR/bin/"
    fi
    
    if [[ -f metrics_exporter ]]; then
        cp metrics_exporter "$INSTALL_DIR/bin/"
    fi
    
    # Set permissions
    chmod 755 "$INSTALL_DIR/bin/"*
    chown root:root "$INSTALL_DIR/bin/"*
    
    # Create symlinks in /usr/local/bin
    ln -sf "$INSTALL_DIR/bin/vpn_manager" /usr/local/bin/vpn_manager
    ln -sf "$INSTALL_DIR/bin/keygen" /usr/local/bin/svpn_keygen
    
    log_success "Binaries installed successfully"
}

# Install configuration files
install_config() {
    log "Installing configuration files..."
    
    # Create default configuration
    cat > "$CONFIG_DIR/securevpn.conf" << EOF
# Secure VPN Configuration
vpn_port=8443
api_port=8080
metrics_port=9090
max_clients=1000
log_level=INFO
database_path=$DATA_DIR/database/vpn.db
private_key_file=$DATA_DIR/ssl/server.key
public_key_file=$DATA_DIR/ssl/server.pub
log_file=$LOG_DIR/securevpn.log
pid_file=$DATA_DIR/securevpn.pid
EOF
    
    chown root:$GROUP "$CONFIG_DIR/securevpn.conf"
    chmod 640 "$CONFIG_DIR/securevpn.conf"
    
    log_success "Configuration files installed successfully"
}

# Generate SSL certificates
generate_certificates() {
    log "Generating SSL certificates..."
    
    if [[ ! -f "$DATA_DIR/ssl/server.key" ]]; then
        "$INSTALL_DIR/bin/keygen" -g \
            -k "$DATA_DIR/ssl/server.key" \
            -p "$DATA_DIR/ssl/server.pub"
        
        chown $USER:$GROUP "$DATA_DIR/ssl/"*
        chmod 600 "$DATA_DIR/ssl/server.key"
        chmod 644 "$DATA_DIR/ssl/server.pub"
        
        log_success "SSL certificates generated successfully"
    else
        log "SSL certificates already exist"
    fi
}

# Initialize database
initialize_database() {
    log "Initializing database..."
    
    if [[ ! -f "$DATA_DIR/database/vpn.db" ]]; then
        sudo -u $USER "$INSTALL_DIR/bin/vpn_manager" \
            -d "$DATA_DIR/database/vpn.db" \
            -k "$DATA_DIR/ssl/server.key" \
            -p "$DATA_DIR/ssl/server.pub" \
            create-user "<EMAIL>" "admin123" "enterprise"
        
        log_success "Database initialized successfully"
        log_success "Default admin user created: <EMAIL> / admin123"
    else
        log "Database already exists"
    fi
}

# Create systemd service
create_service() {
    log "Creating systemd service..."
    
    cat > "/etc/systemd/system/$SERVICE_NAME.service" << EOF
[Unit]
Description=Secure VPN Service
After=network.target
Wants=network.target

[Service]
Type=forking
User=$USER
Group=$GROUP
ExecStart=$INSTALL_DIR/bin/svpn_enhanced_server \\
    --config $CONFIG_DIR/securevpn.conf \\
    --daemon \\
    --pid-file $DATA_DIR/securevpn.pid
ExecReload=/bin/kill -HUP \$MAINPID
PIDFile=$DATA_DIR/securevpn.pid
Restart=always
RestartSec=10

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$DATA_DIR $LOG_DIR
CapabilityBoundingSet=CAP_NET_ADMIN CAP_NET_RAW
AmbientCapabilities=CAP_NET_ADMIN CAP_NET_RAW

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    systemctl enable $SERVICE_NAME
    
    log_success "Systemd service created successfully"
}

# Configure firewall
configure_firewall() {
    log "Configuring firewall..."
    
    # Try to configure iptables
    if command -v iptables >/dev/null 2>&1; then
        iptables -A INPUT -p udp --dport 8443 -j ACCEPT 2>/dev/null || true
        iptables -A INPUT -p tcp --dport 8080 -j ACCEPT 2>/dev/null || true
        iptables -A INPUT -p tcp --dport 9090 -j ACCEPT 2>/dev/null || true
        
        # Try to save iptables rules
        if command -v iptables-save >/dev/null 2>&1; then
            case $DISTRO in
                ubuntu|debian)
                    if command -v netfilter-persistent >/dev/null 2>&1; then
                        netfilter-persistent save
                    fi
                    ;;
                centos|rhel|fedora)
                    if command -v iptables-save >/dev/null 2>&1; then
                        iptables-save > /etc/sysconfig/iptables 2>/dev/null || true
                    fi
                    ;;
            esac
        fi
        
        log_success "Firewall configured successfully"
    else
        log_warning "iptables not found, skipping firewall configuration"
    fi
    
    # Try to configure ufw (Ubuntu/Debian)
    if command -v ufw >/dev/null 2>&1; then
        ufw allow 8443/udp comment "Secure VPN Server" 2>/dev/null || true
        ufw allow 8080/tcp comment "Secure VPN API" 2>/dev/null || true
        ufw allow 9090/tcp comment "Secure VPN Metrics" 2>/dev/null || true
        log "UFW rules added"
    fi
    
    # Try to configure firewalld (CentOS/RHEL/Fedora)
    if command -v firewall-cmd >/dev/null 2>&1; then
        firewall-cmd --permanent --add-port=8443/udp 2>/dev/null || true
        firewall-cmd --permanent --add-port=8080/tcp 2>/dev/null || true
        firewall-cmd --permanent --add-port=9090/tcp 2>/dev/null || true
        firewall-cmd --reload 2>/dev/null || true
        log "Firewalld rules added"
    fi
}

# Start service
start_service() {
    log "Starting Secure VPN service..."
    
    if systemctl start $SERVICE_NAME; then
        log_success "Service started successfully"
        
        # Wait a moment and check status
        sleep 2
        if systemctl is-active --quiet $SERVICE_NAME; then
            log_success "Service is running"
        else
            log_warning "Service may not be running properly"
            systemctl status $SERVICE_NAME --no-pager || true
        fi
    else
        log_error "Failed to start service"
        systemctl status $SERVICE_NAME --no-pager || true
    fi
}

# Create desktop entry (if GUI is available)
create_desktop_entry() {
    if [[ -f "$INSTALL_DIR/bin/svpn_gui_client" ]] && [[ -n "${DISPLAY:-}" ]]; then
        log "Creating desktop entry..."
        
        cat > "/usr/share/applications/securevpn.desktop" << EOF
[Desktop Entry]
Name=Secure VPN
Comment=Secure VPN Client
Exec=$INSTALL_DIR/bin/svpn_gui_client
Icon=$INSTALL_DIR/share/securevpn.png
Terminal=false
Type=Application
Categories=Network;Security;
EOF
        
        log_success "Desktop entry created"
    fi
}

# Main installation function
main() {
    echo "========================================"
    echo "Secure VPN Linux Installation"
    echo "========================================"
    echo
    
    check_root
    detect_distro
    install_dependencies
    create_user
    create_directories
    install_binaries
    install_config
    generate_certificates
    initialize_database
    create_service
    configure_firewall
    create_desktop_entry
    start_service
    
    echo
    echo "========================================"
    echo "Installation completed successfully!"
    echo "========================================"
    echo
    echo "Installation directory: $INSTALL_DIR"
    echo "Configuration: $CONFIG_DIR/securevpn.conf"
    echo "Data directory: $DATA_DIR"
    echo "Log directory: $LOG_DIR"
    echo "Service name: $SERVICE_NAME"
    echo
    echo "Default admin credentials:"
    echo "  Email: <EMAIL>"
    echo "  Password: admin123"
    echo
    echo "VPN Server: localhost:8443"
    echo "Web API: http://localhost:8080"
    echo "Metrics: http://localhost:9090/metrics"
    echo
    echo "Service management:"
    echo "  Start:   systemctl start $SERVICE_NAME"
    echo "  Stop:    systemctl stop $SERVICE_NAME"
    echo "  Status:  systemctl status $SERVICE_NAME"
    echo "  Logs:    journalctl -u $SERVICE_NAME -f"
    echo
    echo "User management:"
    echo "  vpn_manager -d $DATA_DIR/database/vpn.db [command]"
    echo
    echo "For support, visit: https://securevpn.com/support"
    echo
}

# Run main function
main "$@"
