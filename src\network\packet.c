#include <stdio.h>
#include "securevpn.h"
#include <string.h>
#include <arpa/inet.h>

#define VPN_MAGIC 0x53565044  // "SVPD" in hex

svpn_error_t packet_create(vpn_packet_t *packet, uint16_t type, const uint8_t *payload, 
                          uint16_t payload_len) {
    if (!packet || (payload_len > 0 && !payload) || payload_len > MAX_PAYLOAD_SIZE) {
        return SVPN_ERROR_INVALID_PARAM;
    }
    
    // Clear packet
    memset(packet, 0, sizeof(vpn_packet_t));
    
    // Set header fields
    packet->magic = htonl(VPN_MAGIC);
    packet->version = htons(SVPN_VERSION_MAJOR);
    packet->type = htons(type);
    packet->sequence = 0;  // Will be set by caller
    packet->timestamp = htonl((uint32_t)(get_timestamp_ms() / 1000));
    packet->payload_len = htons(payload_len);
    
    // Copy payload if provided
    if (payload_len > 0 && payload) {
        memcpy(packet->payload, payload, payload_len);
    }
    
    return SVPN_SUCCESS;
}

svpn_error_t packet_encrypt(vpn_packet_t *packet, const uint8_t *key) {
    if (!packet || !key) {
        return SVPN_ERROR_INVALID_PARAM;
    }
    
    chacha20_ctx_t ctx;
    uint8_t nonce[CHACHA20_NONCE_SIZE];
    uint8_t auth_key[CHACHA20_KEY_SIZE];
    uint8_t tag[POLY1305_TAG_SIZE];
    uint16_t payload_len = ntohs(packet->payload_len);
    svpn_error_t result;
    
    if (payload_len == 0) {
        return SVPN_SUCCESS;  // Nothing to encrypt
    }
    
    // Generate random nonce
    secure_random(nonce, sizeof(nonce));
    
    // Derive encryption and authentication keys from master key
    // For simplicity, we'll use the key directly (in production, use HKDF)
    memcpy(auth_key, key, CHACHA20_KEY_SIZE);
    
    // Initialize ChaCha20
    result = chacha20_init(&ctx, key, nonce);
    if (result != SVPN_SUCCESS) {
        return result;
    }
    
    // Encrypt payload in-place
    result = chacha20_encrypt(&ctx, packet->payload, packet->payload, payload_len);
    if (result != SVPN_SUCCESS) {
        return result;
    }
    
    // Store nonce in reserved area (first 12 bytes)
    memcpy(packet->reserved, nonce, CHACHA20_NONCE_SIZE);
    
    // Compute authentication tag over entire packet
    result = poly1305_auth(auth_key, (uint8_t*)packet, 
                          VPN_HEADER_SIZE + payload_len, tag);
    if (result != SVPN_SUCCESS) {
        return result;
    }
    
    // Store tag in remaining reserved area (last 2 bytes unused)
    memcpy(packet->reserved + CHACHA20_NONCE_SIZE, tag, 
           sizeof(packet->reserved) - CHACHA20_NONCE_SIZE);
    
    return SVPN_SUCCESS;
}

svpn_error_t packet_decrypt(vpn_packet_t *packet, const uint8_t *key) {
    if (!packet || !key) {
        return SVPN_ERROR_INVALID_PARAM;
    }
    
    chacha20_ctx_t ctx;
    uint8_t nonce[CHACHA20_NONCE_SIZE];
    uint8_t auth_key[CHACHA20_KEY_SIZE];
    uint8_t stored_tag[POLY1305_TAG_SIZE];
    uint16_t payload_len = ntohs(packet->payload_len);
    svpn_error_t result;
    
    if (payload_len == 0) {
        return SVPN_SUCCESS;  // Nothing to decrypt
    }
    
    // Verify magic number
    if (ntohl(packet->magic) != VPN_MAGIC) {
        return SVPN_ERROR_CRYPTO_FAIL;
    }
    
    // Extract nonce and tag from reserved area
    memcpy(nonce, packet->reserved, CHACHA20_NONCE_SIZE);
    memcpy(stored_tag, packet->reserved + CHACHA20_NONCE_SIZE, 
           sizeof(packet->reserved) - CHACHA20_NONCE_SIZE);
    
    // Derive authentication key
    memcpy(auth_key, key, CHACHA20_KEY_SIZE);
    
    // Verify authentication tag
    result = poly1305_verify(auth_key, (uint8_t*)packet, 
                            VPN_HEADER_SIZE + payload_len, stored_tag);
    if (result != SVPN_SUCCESS) {
        return SVPN_ERROR_CRYPTO_FAIL;
    }
    
    // Initialize ChaCha20
    result = chacha20_init(&ctx, key, nonce);
    if (result != SVPN_SUCCESS) {
        return result;
    }
    
    // Decrypt payload in-place
    result = chacha20_decrypt(&ctx, packet->payload, packet->payload, payload_len);
    if (result != SVPN_SUCCESS) {
        return result;
    }
    
    // Clear nonce and tag from reserved area
    secure_zero(packet->reserved, sizeof(packet->reserved));
    
    return SVPN_SUCCESS;
}
