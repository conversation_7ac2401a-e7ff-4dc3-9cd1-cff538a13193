# Secure VPN - Commercial VPN Service

A highly secure, commercial-grade VPN implementation with advanced encryption, database-backed user management, and subscription billing support.

## Features

### Core Security
- **Advanced Encryption**: ChaCha20-Poly1305 (superior to AES-256)
- **Key Exchange**: X25519 Elliptic Curve Di<PERSON>ie-<PERSON>man
- **Authentication**: RSA-4096 license key system
- **Protocol**: Custom UDP-based protocol with obfuscation
- **Security**: Built to avoid antivirus false positives

### Commercial Features
- **User Management**: SQLite database with user accounts and subscriptions
- **Subscription Plans**: Free, Basic, Premium, and Enterprise tiers
- **License Management**: Database-backed license generation and validation
- **Session Tracking**: Real-time monitoring of user connections and usage
- **Usage Analytics**: Bandwidth tracking and connection statistics
- **Multi-device Support**: Per-user connection limits based on subscription
- **Web API**: RESTful API for user management and billing integration
- **Management Tools**: Command-line tools for administration

## Architecture

### Cryptographic Components
- **ChaCha20**: Stream cipher for data encryption
- **Poly1305**: Message authentication code
- **X25519**: Key exchange for perfect forward secrecy
- **RSA-4096**: License signing and verification

### Network Protocol
- Custom packet format with magic numbers
- Encrypted payload with authenticated headers
- Sequence numbers and timestamps for replay protection
- Keepalive mechanism for connection management

## Subscription Plans

| Plan | Connections | Daily Bandwidth | Price | Features |
|------|-------------|-----------------|-------|----------|
| **Free** | 1 device | 1 GB | $0/month | Basic VPN access |
| **Basic** | 3 devices | 10 GB | $5/month | Multiple devices |
| **Premium** | 5 devices | 100 GB | $10/month | High bandwidth |
| **Enterprise** | 10 devices | Unlimited | $25/month | Business features |

## Building

### Windows Prerequisites
```powershell
# Install Visual Studio 2019/2022 with C++ tools
# Install vcpkg package manager
git clone https://github.com/Microsoft/vcpkg.git
cd vcpkg
.\bootstrap-vcpkg.bat
.\vcpkg integrate install

# Install dependencies
.\vcpkg install sqlite3:x64-windows
.\vcpkg install cjson:x64-windows
.\vcpkg install curl:x64-windows
.\vcpkg install openssl:x64-windows
```

### Linux Prerequisites
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install build-essential cmake libsqlite3-dev libcjson-dev libcurl4-openssl-dev libssl-dev libgtk-3-dev

# CentOS/RHEL/Fedora
sudo dnf install gcc gcc-c++ cmake sqlite-devel libcjson-devel libcurl-devel openssl-devel gtk3-devel

# Arch Linux
sudo pacman -S base-devel cmake sqlite cjson curl openssl gtk3
```

### Compilation

#### Windows (Visual Studio)
```powershell
mkdir build && cd build
cmake .. -DCMAKE_TOOLCHAIN_FILE=C:/vcpkg/scripts/buildsystems/vcpkg.cmake
cmake --build . --config Release
```

#### Linux
```bash
mkdir build && cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
make -j$(nproc)
```

### Build Targets
- `svpn_server` - Original VPN server
- `svpn_enhanced_server` - Enhanced server with database integration
- `svpn_client` - Command-line VPN client
- `svpn_gui_client` - Platform-specific GUI client (Windows native/Linux GTK)
- `vpn_manager` - Management tool for commercial operations
- `svpn_web_api` - Web API server (requires cJSON)
- `metrics_exporter` - Prometheus metrics exporter
- `billing_test` - Billing system testing tool
- `keygen` - License key generator

## Quick Installation

### Windows
```powershell
# Download and run the installer
.\install.bat

# Or use the GUI installer
SecureVPN-Setup.exe
```

### Linux
```bash
# Download and run the installer
chmod +x install.sh
sudo ./install.sh

# Or install from package
sudo dpkg -i securevpn_1.0.0_amd64.deb  # Debian/Ubuntu
sudo rpm -i securevpn-1.0.0.x86_64.rpm  # CentOS/RHEL/Fedora
```

## Commercial VPN Setup

### 1. Generate RSA Keypair
```bash
./keygen -g -k server.key -p server.pub
```

### 2. Initialize Database and Create Users
```bash
# Create a user account
./vpn_manager -d vpn.db -k server.key -p server.pub create-user <EMAIL> password123 premium

# Generate license for user
./vpn_manager -d vpn.db -k server.key -p server.pub generate-license 1 "John's Phone" 30
```

### 3. Start Enhanced Server
```bash
# Start the enhanced server with database integration
./svpn_enhanced_server -d vpn.db -k server.key -p server.pub --vpn-port 8443 --api-port 8080
```

### 4. Connect Client with License
```bash
./svpn_client -s 127.0.0.1 -p 8443 -l license_1_Johns_Phone.lic
```

## Management Operations

### User Management
```bash
# Create users with different plans
./vpn_manager -d vpn.db -k server.key -p server.pub create-user <EMAIL> pass456 basic
./vpn_manager -d vpn.db -k server.key -p server.pub create-user <EMAIL> pass789 enterprise

# List all users
./vpn_manager -d vpn.db list-users

# Generate licenses for devices
./vpn_manager -d vpn.db generate-license 2 "Alice's Laptop" 30
./vpn_manager -d vpn.db generate-license 3 "Bob's Work Phone" 90
```

### Analytics and Monitoring
```bash
# View user analytics
./vpn_manager -d vpn.db view-analytics 1

# View server statistics
./vpn_manager -d vpn.db view-analytics

# Cleanup inactive sessions
./vpn_manager -d vpn.db cleanup-sessions
```

## Security Features

1. **Post-Quantum Resistant**: Uses ChaCha20 which is quantum-resistant
2. **Perfect Forward Secrecy**: X25519 key exchange ensures session keys are ephemeral
3. **License Control**: RSA-signed licenses control access and duration
4. **Anti-Detection**: Custom protocol designed to avoid DPI detection
5. **Memory Safety**: Secure memory clearing and bounds checking

## File Structure

```
secure-vpn/
├── src/
│   ├── crypto/          # Cryptographic implementations
│   ├── network/         # Network packet handling
│   ├── auth/           # License authentication
│   ├── client/         # Client application
│   └── server/         # Server application
├── include/            # Header files
├── tools/             # License generation tools
└── build/             # Build output
```

## Protocol Specification

### Packet Format
```
+--------+--------+--------+--------+
| Magic  | Version| Type   | Seq    |
+--------+--------+--------+--------+
| Timestamp       | PayloadLen      |
+--------+--------+--------+--------+
| Reserved (Nonce + Auth Tag)      |
+--------+--------+--------+--------+
| Payload...                       |
+----------------------------------+
```

### Handshake Process
1. Client sends X25519 public key
2. Server responds with its X25519 public key
3. Both compute shared secret
4. Client sends encrypted license
5. Server validates license and responds

## License Format

The license contains:
- User ID (64 bytes)
- Issue timestamp
- Expiration timestamp
- Maximum connections
- RSA-4096 signature

## Security Considerations

- All sensitive data is cleared from memory after use
- Random nonces prevent replay attacks
- Constant-time comparisons prevent timing attacks
- License validation includes timestamp checks
- Connection timeouts prevent resource exhaustion

## Development Notes

This VPN is designed to be built in WSL to avoid Windows antivirus false positives during development. The implementation uses standard cryptographic primitives and avoids suspicious system calls that might trigger security software.
