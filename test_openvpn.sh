#!/bin/bash

# Test OpenVPN Connection
# Run this to verify your OpenVPN server is working

echo "🧪 Testing OpenVPN Server..."

# Check if OpenVPN is running
if sudo systemctl is-active --quiet openvpn@server; then
    echo "✅ OpenVPN service is running"
else
    echo "❌ OpenVPN service is not running"
    echo "Starting OpenVPN..."
    sudo systemctl start openvpn@server
    sleep 3
fi

# Check if port 1194 is listening
if sudo netstat -ulnp | grep -q ":1194"; then
    echo "✅ OpenVPN port 1194/UDP is listening"
else
    echo "❌ OpenVPN port 1194/UDP is not listening"
fi

# Check if TUN interface exists
if ip link show | grep -q "tun"; then
    echo "✅ TUN interface is active"
else
    echo "❌ No TUN interface found"
fi

# Check IP forwarding
if sysctl net.ipv4.ip_forward | grep -q "= 1"; then
    echo "✅ IP forwarding is enabled"
else
    echo "❌ IP forwarding is disabled"
fi

# Check firewall rules
if sudo ufw status | grep -q "1194/udp"; then
    echo "✅ Firewall allows OpenVPN traffic"
else
    echo "❌ Firewall may be blocking OpenVPN"
fi

# Test profile generation
echo ""
echo "🔧 Testing profile generation..."
if [ -f "vpn_profiles/test_openvpn.ovpn" ]; then
    echo "✅ Test profile exists"
    echo "📄 Profile location: vpn_profiles/test_openvpn.ovpn"
else
    echo "⚠️  Generating test profile..."
    python3 vpn_manager_tool.py --create-user <EMAIL> --plan premium
    python3 vpn_manager_tool.py --generate-profiles <EMAIL> --server $(curl -s ifconfig.me)
fi

echo ""
echo "📊 OpenVPN Status Summary:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
sudo systemctl status openvpn@server --no-pager -l | head -10

echo ""
echo "🎯 Ready to connect!"
echo "1. Download: vpn_profiles/test_openvpn.ovpn"
echo "2. Import into OpenVPN client"
echo "3. Login: <EMAIL> / password"
echo "4. Connect!"
