cmake_minimum_required(VERSION 3.16)
project(SecureVPN VERSION 1.0.0 LANGUAGES C)

set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Compiler flags for security and performance
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra -O2 -fstack-protector-strong")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -D_FORTIFY_SOURCE=2 -fPIE -pie")

# Include directories
include_directories(include)

# Find required packages
find_package(PkgConfig REQUIRED)

# Find SQLite3
find_package(PkgConfig REQUIRED)
pkg_check_modules(SQLITE3 REQUIRED sqlite3)

# Find cJSON for web API
pkg_check_modules(CJSO<PERSON> libcjson)

# Find libcurl for payment integration
pkg_check_modules(CURL libcurl)

# Find OpenSSL for enhanced security
find_package(OpenSSL REQUIRED)

# Find threads
find_package(Threads REQUIRED)

# Source files
file(GLOB_RECURSE CRYPTO_SOURCES "src/crypto/*.c")
file(GLOB_RECURSE NETWORK_SOURCES "src/network/*.c")
file(GLOB_RECURSE AUTH_SOURCES "src/auth/*.c")
file(GLOB_RECURSE TUNNEL_SOURCES "src/tunnel/*.c")
file(GLOB_RECURSE DATABASE_SOURCES "src/database/*.c")
file(GLOB_RECURSE PAYMENT_SOURCES "src/payment/*.c")
file(GLOB_RECURSE MONITORING_SOURCES "src/monitoring/*.c")
file(GLOB_RECURSE SECURITY_SOURCES "src/security/*.c")

# Create static library for core components
add_library(securevpn_core STATIC
    ${CRYPTO_SOURCES}
    ${NETWORK_SOURCES}
    ${AUTH_SOURCES}
    ${TUNNEL_SOURCES}
    ${DATABASE_SOURCES}
    ${PAYMENT_SOURCES}
    ${MONITORING_SOURCES}
    ${SECURITY_SOURCES}
)

# Link libraries to the core library
target_link_libraries(securevpn_core ${SQLITE3_LIBRARIES} OpenSSL::SSL OpenSSL::Crypto)
target_include_directories(securevpn_core PRIVATE ${SQLITE3_INCLUDE_DIRS})
target_compile_options(securevpn_core PRIVATE ${SQLITE3_CFLAGS_OTHER})

# Add curl support if available
if(CURL_FOUND)
    target_link_libraries(securevpn_core ${CURL_LIBRARIES})
    target_include_directories(securevpn_core PRIVATE ${CURL_INCLUDE_DIRS})
    target_compile_options(securevpn_core PRIVATE ${CURL_CFLAGS_OTHER})
    target_compile_definitions(securevpn_core PRIVATE HAVE_CURL)
endif()

# Client executable
add_executable(svpn_client src/client/main.c)
target_link_libraries(svpn_client securevpn_core)

# Server executable
add_executable(svpn_server src/server/main.c)
target_link_libraries(svpn_server securevpn_core Threads::Threads)

# License key generator tool
add_executable(keygen tools/keygen.c)
target_link_libraries(keygen securevpn_core)

# VPN management tool
add_executable(vpn_manager tools/vpn_manager.c)
target_link_libraries(vpn_manager securevpn_core Threads::Threads)

# Enhanced server with database integration
add_executable(svpn_enhanced_server src/server/enhanced_server.c)
target_link_libraries(svpn_enhanced_server securevpn_core Threads::Threads)

# Web API server (optional, requires cJSON)
if(CJSON_FOUND)
    add_executable(svpn_web_api src/server/web_api.c)
    target_link_libraries(svpn_web_api securevpn_core ${CJSON_LIBRARIES} Threads::Threads)
    target_include_directories(svpn_web_api PRIVATE ${CJSON_INCLUDE_DIRS})
    target_compile_options(svpn_web_api PRIVATE ${CJSON_CFLAGS_OTHER})
endif()

# Metrics exporter
add_executable(metrics_exporter src/monitoring/metrics_main.c)
target_link_libraries(metrics_exporter securevpn_core Threads::Threads)

# Platform-specific GUI clients
if(WIN32)
    # Windows native GUI client
    add_executable(svpn_gui_client clients/windows/SecureVPN.cpp)
    target_link_libraries(svpn_gui_client securevpn_core user32 gdi32 comctl32 shell32 wininet)
    set_target_properties(svpn_gui_client PROPERTIES WIN32_EXECUTABLE TRUE)
elseif(UNIX)
    # Linux GTK GUI client (optional, requires GTK)
    find_package(PkgConfig QUIET)
    if(PkgConfig_FOUND)
        pkg_check_modules(GTK3 gtk+-3.0)
        if(GTK3_FOUND)
            add_executable(svpn_gui_client clients/desktop/main.c)
            target_link_libraries(svpn_gui_client securevpn_core ${GTK3_LIBRARIES} Threads::Threads)
            target_include_directories(svpn_gui_client PRIVATE ${GTK3_INCLUDE_DIRS})
            target_compile_options(svpn_gui_client PRIVATE ${GTK3_CFLAGS_OTHER})
        endif()
    endif()
endif()

# Billing system test tool
add_executable(billing_test tools/billing_test.c)
target_link_libraries(billing_test securevpn_core Threads::Threads)

# Install targets
set(INSTALL_TARGETS svpn_client svpn_server keygen vpn_manager svpn_enhanced_server metrics_exporter billing_test)

if(CJSON_FOUND)
    list(APPEND INSTALL_TARGETS svpn_web_api)
endif()

if(GTK3_FOUND)
    list(APPEND INSTALL_TARGETS svpn_gui_client)
endif()

install(TARGETS ${INSTALL_TARGETS}
    RUNTIME DESTINATION bin
)

# Install configuration files
install(DIRECTORY deployment/config/
    DESTINATION etc/securevpn
    FILES_MATCHING PATTERN "*.conf"
)

# Install documentation
install(FILES README.md
    DESTINATION share/doc/securevpn
)

install(DIRECTORY docs/
    DESTINATION share/doc/securevpn
    FILES_MATCHING PATTERN "*.md" PATTERN "*.html"
)

# Platform-specific installation files
if(WIN32)
    # Install Windows-specific files
    install(FILES deployment/windows/install.bat
        DESTINATION .
        RENAME install.bat
    )
    install(DIRECTORY deployment/windows/
        DESTINATION windows
        FILES_MATCHING PATTERN "*.exe" PATTERN "*.msi" PATTERN "*.dll"
    )
elseif(UNIX)
    # Install Linux-specific files
    install(FILES deployment/linux/install.sh
        DESTINATION .
        PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE
    )
    install(FILES deployment/systemd/securevpn.service
        DESTINATION lib/systemd/system
        OPTIONAL
    )
    install(DIRECTORY deployment/linux/
        DESTINATION linux
        FILES_MATCHING PATTERN "*.sh" PATTERN "*.conf"
        PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE GROUP_READ GROUP_EXECUTE WORLD_READ WORLD_EXECUTE
    )
endif()

# Create package
set(CPACK_PACKAGE_NAME "SecureVPN")
set(CPACK_PACKAGE_VERSION "${PROJECT_VERSION}")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "Commercial VPN Service with Advanced Security")
set(CPACK_PACKAGE_VENDOR "SecureVPN Inc.")
set(CPACK_PACKAGE_CONTACT "<EMAIL>")
set(CPACK_RESOURCE_FILE_LICENSE "${CMAKE_CURRENT_SOURCE_DIR}/LICENSE")
set(CPACK_RESOURCE_FILE_README "${CMAKE_CURRENT_SOURCE_DIR}/README.md")

# Platform-specific packaging
if(WIN32)
    set(CPACK_GENERATOR "NSIS;ZIP")
    set(CPACK_NSIS_DISPLAY_NAME "Secure VPN")
    set(CPACK_NSIS_PACKAGE_NAME "SecureVPN")
    set(CPACK_NSIS_URL_INFO_ABOUT "https://securevpn.com")
    set(CPACK_NSIS_HELP_LINK "https://securevpn.com/support")
    set(CPACK_NSIS_CONTACT "<EMAIL>")
    set(CPACK_NSIS_MODIFY_PATH ON)

    # Windows-specific dependencies
    set(CPACK_NSIS_EXTRA_INSTALL_COMMANDS "
        ExecWait '$INSTDIR\\\\bin\\\\vcredist_x64.exe /quiet'
        ExecWait '$INSTDIR\\\\bin\\\\install_tap_driver.exe'
    ")
elseif(UNIX)
    set(CPACK_GENERATOR "DEB;RPM;TGZ")
    set(CPACK_DEBIAN_PACKAGE_MAINTAINER "SecureVPN Team <<EMAIL>>")
    set(CPACK_DEBIAN_PACKAGE_SECTION "net")
    set(CPACK_DEBIAN_PACKAGE_DEPENDS "libsqlite3-0, libcjson1, libcurl4, libssl3, libc6")
    set(CPACK_DEBIAN_PACKAGE_RECOMMENDS "iptables, iproute2")
    set(CPACK_RPM_PACKAGE_GROUP "Applications/Internet")
    set(CPACK_RPM_PACKAGE_REQUIRES "sqlite >= 3.0, libcjson >= 1.0, libcurl >= 7.0, openssl >= 1.1, glibc")
    set(CPACK_RPM_PACKAGE_SUGGESTS "iptables, iproute")

    # Linux post-install scripts
    set(CPACK_DEBIAN_PACKAGE_CONTROL_EXTRA "${CMAKE_CURRENT_SOURCE_DIR}/deployment/linux/postinst")
    set(CPACK_RPM_POST_INSTALL_SCRIPT_FILE "${CMAKE_CURRENT_SOURCE_DIR}/deployment/linux/postinst.sh")
endif()

include(CPack)
