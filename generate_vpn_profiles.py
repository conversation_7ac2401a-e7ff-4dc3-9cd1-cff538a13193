#!/usr/bin/env python3
"""
VPN Profile Generator - Windows Compatible
Generates OpenVPN profiles for users without requiring complex setup
"""

import os
import sqlite3
import secrets
import time
from pathlib import Path

class SimpleVPNProfileGenerator:
    def __init__(self):
        self.profiles_dir = Path("vpn_profiles")
        self.profiles_dir.mkdir(exist_ok=True)
        self.db_file = "vpn_production.db"
        
        # OpenSSL path for Windows
        self.openssl_path = r"C:\Program Files\OpenSSL-Win64\bin\openssl.exe"
    
    def check_user_exists(self, email):
        """Check if user exists in database"""
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('SELECT user_id, plan FROM users WHERE email = ? AND is_active = 1', (email,))
            result = cursor.fetchone()
            conn.close()
            
            return result
        except Exception as e:
            print(f"Database error: {e}")
            return None
    
    def generate_simple_openvpn_profile(self, email, server_host="your-vpn-server.com"):
        """Generate a simple OpenVPN profile without certificates"""
        user_info = self.check_user_exists(email)
        if not user_info:
            print(f"❌ User {email} not found or inactive")
            return None
        
        user_id, plan = user_info
        username = email.split('@')[0]
        
        # Create simple OpenVPN config (username/password auth)
        config = f"""# Secure VPN - OpenVPN Profile
# User: {email}
# Plan: {plan}
# Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}

client
dev tun
proto udp
remote {server_host} 1194
resolv-retry infinite
nobind
persist-key
persist-tun

# Authentication
auth-user-pass
auth-nocache

# Security
cipher AES-256-GCM
auth SHA256
compress lz4-v2

# Logging
verb 3
mute 20

# Certificate verification (disable for demo)
verify-x509-name {server_host} name
remote-cert-tls server

# Windows compatibility
route-method exe
route-delay 2

# DNS
dhcp-option DNS *******
dhcp-option DNS *******
"""
        
        # Save profile
        profile_file = self.profiles_dir / f"{username}_openvpn.ovpn"
        with open(profile_file, 'w') as f:
            f.write(config)
        
        # Create instructions
        instructions_file = self.profiles_dir / f"{username}_openvpn_instructions.txt"
        instructions = f"""
OpenVPN Setup Instructions for {email}
=====================================

STEP 1: Download OpenVPN Client
- Windows: https://openvpn.net/community-downloads/
- Download "OpenVPN Connect" or "OpenVPN GUI"

STEP 2: Install and Import Profile
1. Install OpenVPN client
2. Import the profile file: {username}_openvpn.ovpn
3. When prompted for credentials, enter:
   Username: {email}
   Password: [your account password]

STEP 3: Connect
- Select the imported profile
- Click Connect
- Enter your credentials when prompted

Your Plan: {plan}
Profile File: {profile_file}
Server: {server_host}:1194

Support: <EMAIL>
"""
        
        with open(instructions_file, 'w') as f:
            f.write(instructions)
        
        print(f"✅ OpenVPN profile created for {email}")
        print(f"   Profile: {profile_file}")
        print(f"   Instructions: {instructions_file}")
        
        return profile_file, instructions_file
    
    def generate_wireguard_profile(self, email, server_host="your-vpn-server.com"):
        """Generate WireGuard profile with pre-generated keys"""
        user_info = self.check_user_exists(email)
        if not user_info:
            print(f"❌ User {email} not found or inactive")
            return None
        
        user_id, plan = user_info
        username = email.split('@')[0]
        
        # Generate simple keys (for demo - in production use proper WireGuard key generation)
        client_private_key = secrets.token_urlsafe(32)
        client_public_key = secrets.token_urlsafe(32)
        server_public_key = "DEMO_SERVER_PUBLIC_KEY_" + secrets.token_urlsafe(16)
        
        # Assign IP (simple increment based on user_id)
        client_ip = f"10.66.66.{user_id + 1}"
        
        # Create WireGuard config
        config = f"""# Secure VPN - WireGuard Profile
# User: {email}
# Plan: {plan}
# Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}

[Interface]
PrivateKey = {client_private_key}
Address = {client_ip}/24
DNS = *******, *******

[Peer]
PublicKey = {server_public_key}
Endpoint = {server_host}:51820
AllowedIPs = 0.0.0.0/0
PersistentKeepalive = 25
"""
        
        # Save profile
        profile_file = self.profiles_dir / f"{username}_wireguard.conf"
        with open(profile_file, 'w') as f:
            f.write(config)
        
        # Create instructions
        instructions_file = self.profiles_dir / f"{username}_wireguard_instructions.txt"
        instructions = f"""
WireGuard Setup Instructions for {email}
=======================================

STEP 1: Download WireGuard Client
- Windows: https://www.wireguard.com/install/
- Download "WireGuard for Windows"

STEP 2: Import Configuration
1. Install WireGuard client
2. Open WireGuard application
3. Click "Import tunnel(s) from file"
4. Select: {username}_wireguard.conf
5. Click "Activate"

STEP 3: Connect
- Toggle the tunnel ON in WireGuard app
- You should see data transfer statistics

Your Plan: {plan}
Profile File: {profile_file}
Client IP: {client_ip}
Server: {server_host}:51820

ADVANTAGES:
- No username/password needed
- Better performance than OpenVPN
- Modern cryptography
- Lower battery usage on mobile

Support: <EMAIL>
"""
        
        with open(instructions_file, 'w') as f:
            f.write(instructions)
        
        print(f"✅ WireGuard profile created for {email}")
        print(f"   Profile: {profile_file}")
        print(f"   Instructions: {instructions_file}")
        
        return profile_file, instructions_file
    
    def generate_all_profiles(self, email, server_host="your-vpn-server.com"):
        """Generate both OpenVPN and WireGuard profiles"""
        print(f"📱 Generating VPN profiles for {email}...")
        
        profiles = []
        
        # Generate OpenVPN profile
        try:
            ovpn_result = self.generate_simple_openvpn_profile(email, server_host)
            if ovpn_result:
                profiles.append(('OpenVPN', ovpn_result))
        except Exception as e:
            print(f"❌ OpenVPN profile generation failed: {e}")
        
        # Generate WireGuard profile
        try:
            wg_result = self.generate_wireguard_profile(email, server_host)
            if wg_result:
                profiles.append(('WireGuard', wg_result))
        except Exception as e:
            print(f"❌ WireGuard profile generation failed: {e}")
        
        if profiles:
            print(f"\n✅ Generated {len(profiles)} profile(s) for {email}")
            print(f"📁 All files saved in: {self.profiles_dir}")
            
            print(f"\n📧 Send these files to {email}:")
            for profile_type, (config_file, instructions_file) in profiles:
                print(f"   {profile_type}:")
                print(f"     Config: {config_file}")
                print(f"     Instructions: {instructions_file}")
        else:
            print(f"❌ No profiles could be generated for {email}")
        
        return profiles

def main():
    """Main function"""
    print("📱 VPN Profile Generator")
    print("=" * 30)
    
    generator = SimpleVPNProfileGenerator()
    
    # Check if database exists
    if not os.path.exists(generator.db_file):
        print(f"❌ Database not found: {generator.db_file}")
        print("Please run the VPN server first to create the database")
        return 1
    
    # Get user input
    try:
        email = input("Enter user email: ").strip()
        if not email:
            print("❌ Email is required")
            return 1
        
        server_host = input("Enter server hostname (default: your-vpn-server.com): ").strip()
        if not server_host:
            server_host = "your-vpn-server.com"
        
        # Generate profiles
        profiles = generator.generate_all_profiles(email, server_host)
        
        if profiles:
            print(f"\n🎉 Success! VPN profiles ready for {email}")
            print(f"📂 Check the '{generator.profiles_dir}' folder for all files")
            
            print(f"\n📋 Next Steps:")
            print(f"1. Send the profile files to {email}")
            print(f"2. User follows the instructions to set up VPN")
            print(f"3. User connects using their account credentials")
            print(f"4. Monitor connections via dashboard: http://localhost:8090/docs/dashboard.html")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n🛑 Cancelled")
        return 1
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
