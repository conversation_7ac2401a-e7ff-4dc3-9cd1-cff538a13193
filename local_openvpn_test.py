#!/usr/bin/env python3
"""
Local OpenVPN Test Setup
Quick setup for testing OpenVPN on your local PC
"""

import os
import sys
import subprocess
import socket
from pathlib import Path

def get_local_ip():
    """Get local IP address"""
    try:
        # Connect to a remote address to get local IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "127.0.0.1"

def check_openvpn_installed():
    """Check if OpenVPN is installed"""
    try:
        subprocess.run(["openvpn", "--version"], capture_output=True, check=True)
        return True
    except:
        return False

def setup_local_openvpn():
    """Setup OpenVPN for local testing"""
    print("🚀 Setting up OpenVPN for local testing...")
    
    local_ip = get_local_ip()
    print(f"🌐 Local IP: {local_ip}")
    
    # Check if OpenVPN is installed
    if not check_openvpn_installed():
        print("❌ OpenVPN not found!")
        print("📦 Please install OpenVPN:")
        print("   Windows: https://openvpn.net/community-downloads/")
        print("   Linux: sudo apt install openvpn")
        print("   Mac: brew install openvpn")
        return False
    
    print("✅ OpenVPN is installed")
    
    # Setup certificates and config
    print("🔧 Setting up certificates...")
    try:
        from openvpn_integration import OpenVPNManager
        manager = OpenVPNManager()
        manager.setup_complete_server(local_ip, 1194)
        print("✅ Certificates created")
    except Exception as e:
        print(f"❌ Certificate setup failed: {e}")
        return False
    
    # Create test user
    print("👤 Creating test user...")
    try:
        subprocess.run([
            sys.executable, "vpn_manager_tool.py", 
            "--create-user", "<EMAIL>", 
            "--plan", "premium"
        ], check=True)
        
        subprocess.run([
            sys.executable, "vpn_manager_tool.py",
            "--generate-profiles", "<EMAIL>",
            "--server", local_ip
        ], check=True)
        
        print("✅ Test user created")
    except Exception as e:
        print(f"❌ User creation failed: {e}")
        return False
    
    # Create simple server config for local testing
    create_simple_server_config(local_ip)
    
    print("\n🎉 Local OpenVPN setup complete!")
    print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    print(f"📱 Client file: vpn_profiles/test_openvpn.ovpn")
    print(f"🔑 Username: <EMAIL>")
    print(f"🔑 Password: password")
    print(f"🌐 Server: {local_ip}:1194")
    print("\n🚀 To start OpenVPN server:")
    print("   sudo openvpn --config openvpn/server/server.conf")
    print("\n📱 To test connection:")
    print("   1. Import vpn_profiles/test_openvpn.ovpn into OpenVPN client")
    print("   2. Enter credentials above")
    print("   3. Connect!")
    
    return True

def create_simple_server_config(server_ip):
    """Create a simple OpenVPN server config for local testing"""
    config_dir = Path("openvpn/server")
    config_file = config_dir / "simple_server.conf"
    
    config = f"""# Simple OpenVPN Server Config for Local Testing
port 1194
proto udp
dev tun

# Certificates
ca ca/ca.crt
cert server.crt
key server.key
dh dh2048.pem

# Network
server ******** *************
ifconfig-pool-persist ipp.txt

# Routes
push "redirect-gateway def1 bypass-dhcp"
push "dhcp-option DNS *******"
push "dhcp-option DNS *******"

# Security
cipher AES-256-GCM
auth SHA256
keepalive 10 120
compress lz4-v2
push "compress lz4-v2"

# Logging
status openvpn-status.log
log openvpn.log
verb 3

# Client auth
auth-user-pass-verify auth_script.py via-env
script-security 3
username-as-common-name

# Management
management localhost 7505
"""
    
    with open(config_file, 'w') as f:
        f.write(config)
    
    print(f"✅ Simple server config created: {config_file}")

def start_openvpn_server():
    """Start OpenVPN server"""
    config_file = "openvpn/server/simple_server.conf"
    
    if not os.path.exists(config_file):
        print("❌ Server config not found. Run setup first.")
        return False
    
    print("🎯 Starting OpenVPN server...")
    try:
        # Change to openvpn/server directory
        os.chdir("openvpn/server")
        
        # Start OpenVPN
        subprocess.run([
            "openvpn", "--config", "simple_server.conf"
        ], check=True)
        
    except KeyboardInterrupt:
        print("\n🛑 OpenVPN server stopped")
    except Exception as e:
        print(f"❌ Failed to start OpenVPN: {e}")
        return False
    
    return True

def main():
    """Main function"""
    if len(sys.argv) > 1 and sys.argv[1] == "--start":
        start_openvpn_server()
    else:
        setup_local_openvpn()
        
        print("\n🎯 Next steps:")
        print("1. Start server: python local_openvpn_test.py --start")
        print("2. In another terminal/PC: Import and connect with .ovpn file")

if __name__ == "__main__":
    main()
