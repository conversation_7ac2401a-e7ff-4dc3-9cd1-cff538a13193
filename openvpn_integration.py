#!/usr/bin/env python3
"""
OpenVPN Integration for Secure VPN
Generates OpenVPN server configs and client profiles
"""

import os
import subprocess
import sqlite3
import secrets
import time
from pathlib import Path

class OpenVPNManager:
    def __init__(self, base_dir="openvpn"):
        self.base_dir = Path(base_dir)
        self.ca_dir = self.base_dir / "ca"
        self.server_dir = self.base_dir / "server"
        self.client_dir = self.base_dir / "clients"
        self.db_file = "vpn_production.db"
        
        # Create directories
        for dir_path in [self.ca_dir, self.server_dir, self.client_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def setup_ca(self):
        """Set up Certificate Authority"""
        print("🔐 Setting up Certificate Authority...")
        
        # Create CA key
        ca_key = self.ca_dir / "ca.key"
        ca_crt = self.ca_dir / "ca.crt"
        
        if not ca_key.exists():
            # Generate CA private key
            subprocess.run([
                "openssl", "genrsa", "-out", str(ca_key), "4096"
            ], check=True)
            
            # Generate CA certificate
            subprocess.run([
                "openssl", "req", "-new", "-x509", "-days", "3650",
                "-key", str(ca_key), "-out", str(ca_crt),
                "-subj", "/C=US/ST=CA/L=San Francisco/O=SecureVPN/CN=SecureVPN-CA"
            ], check=True)
            
            print("✅ CA certificate created")
        else:
            print("✅ CA already exists")
    
    def setup_server_certs(self):
        """Set up server certificates"""
        print("🔐 Setting up server certificates...")
        
        server_key = self.server_dir / "server.key"
        server_crt = self.server_dir / "server.crt"
        server_csr = self.server_dir / "server.csr"
        dh_pem = self.server_dir / "dh2048.pem"
        ta_key = self.server_dir / "ta.key"
        
        if not server_key.exists():
            # Generate server private key
            subprocess.run([
                "openssl", "genrsa", "-out", str(server_key), "4096"
            ], check=True)
            
            # Generate server certificate request
            subprocess.run([
                "openssl", "req", "-new", "-key", str(server_key),
                "-out", str(server_csr),
                "-subj", "/C=US/ST=CA/L=San Francisco/O=SecureVPN/CN=vpn.securevpn.com"
            ], check=True)
            
            # Sign server certificate
            subprocess.run([
                "openssl", "x509", "-req", "-days", "3650",
                "-in", str(server_csr), "-CA", str(self.ca_dir / "ca.crt"),
                "-CAkey", str(self.ca_dir / "ca.key"), "-CAcreateserial",
                "-out", str(server_crt)
            ], check=True)
            
            print("✅ Server certificate created")
        
        if not dh_pem.exists():
            print("🔐 Generating Diffie-Hellman parameters (this may take a while)...")
            subprocess.run([
                "openssl", "dhparam", "-out", str(dh_pem), "2048"
            ], check=True)
            print("✅ DH parameters generated")
        
        if not ta_key.exists():
            # Generate TLS auth key
            subprocess.run([
                "openvpn", "--genkey", "--secret", str(ta_key)
            ], check=True)
            print("✅ TLS auth key generated")
    
    def create_server_config(self, server_ip="0.0.0.0", port=1194):
        """Create OpenVPN server configuration"""
        config_file = self.server_dir / "server.conf"
        
        config = f"""# Secure VPN OpenVPN Server Configuration
port {port}
proto udp
dev tun

# Certificates and keys
ca {self.ca_dir / "ca.crt"}
cert {self.server_dir / "server.crt"}
key {self.server_dir / "server.key"}
dh {self.server_dir / "dh2048.pem"}
tls-auth {self.server_dir / "ta.key"} 0

# Network configuration
server ******** *************
ifconfig-pool-persist {self.server_dir / "ipp.txt"}

# Push routes to clients
push "redirect-gateway def1 bypass-dhcp"
push "dhcp-option DNS *******"
push "dhcp-option DNS *******"

# Client management
client-to-client
duplicate-cn
keepalive 10 120
tls-version-min 1.2
cipher AES-256-GCM
auth SHA256
compress lz4-v2
push "compress lz4-v2"

# Logging
status {self.server_dir / "openvpn-status.log"}
log-append {self.server_dir / "openvpn.log"}
verb 3
mute 20

# Security
user nobody
group nogroup
persist-key
persist-tun

# Client authentication script
auth-user-pass-verify {self.server_dir / "auth_script.py"} via-env
script-security 3
username-as-common-name

# Management interface
management localhost 7505
"""
        
        with open(config_file, 'w') as f:
            f.write(config)
        
        print(f"✅ Server config created: {config_file}")
        return config_file
    
    def create_auth_script(self):
        """Create authentication script for OpenVPN"""
        auth_script = self.server_dir / "auth_script.py"
        
        script_content = f'''#!/usr/bin/env python3
"""
OpenVPN Authentication Script
Authenticates users against Secure VPN database
"""

import os
import sys
import sqlite3
import hashlib

def authenticate_user():
    """Authenticate user against database"""
    username = os.environ.get('username', '')
    password = os.environ.get('password', '')
    
    if not username or not password:
        return False
    
    try:
        conn = sqlite3.connect('{os.path.abspath(self.db_file)}')
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT user_id, password_hash, salt, plan, is_active
        FROM users WHERE email = ?
        ''', (username,))
        
        result = cursor.fetchone()
        conn.close()
        
        if not result:
            return False
        
        user_id, stored_hash, salt, plan, is_active = result
        
        if not is_active:
            return False
        
        # Verify password
        computed_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000).hex()
        
        if computed_hash == stored_hash:
            # Log successful authentication
            with open('{self.server_dir / "auth.log"}', 'a') as f:
                f.write(f"{{username}} authenticated successfully ({{plan}})\\n")
            return True
        
        return False
        
    except Exception as e:
        with open('{self.server_dir / "auth.log"}', 'a') as f:
            f.write(f"Auth error for {{username}}: {{e}}\\n")
        return False

if __name__ == "__main__":
    if authenticate_user():
        sys.exit(0)  # Success
    else:
        sys.exit(1)  # Failure
'''
        
        with open(auth_script, 'w') as f:
            f.write(script_content)
        
        # Make executable
        os.chmod(auth_script, 0o755)
        
        print(f"✅ Auth script created: {auth_script}")
        return auth_script
    
    def generate_client_cert(self, username):
        """Generate client certificate"""
        client_key = self.client_dir / f"{username}.key"
        client_crt = self.client_dir / f"{username}.crt"
        client_csr = self.client_dir / f"{username}.csr"
        
        if client_key.exists():
            print(f"✅ Client cert for {username} already exists")
            return client_crt, client_key
        
        # Generate client private key
        subprocess.run([
            "openssl", "genrsa", "-out", str(client_key), "4096"
        ], check=True)
        
        # Generate client certificate request
        subprocess.run([
            "openssl", "req", "-new", "-key", str(client_key),
            "-out", str(client_csr),
            "-subj", f"/C=US/ST=CA/L=San Francisco/O=SecureVPN/CN={username}"
        ], check=True)
        
        # Sign client certificate
        subprocess.run([
            "openssl", "x509", "-req", "-days", "365",
            "-in", str(client_csr), "-CA", str(self.ca_dir / "ca.crt"),
            "-CAkey", str(self.ca_dir / "ca.key"), "-CAcreateserial",
            "-out", str(client_crt)
        ], check=True)
        
        print(f"✅ Client certificate generated for {username}")
        return client_crt, client_key
    
    def create_client_config(self, username, server_host="your-vpn-server.com", server_port=1194):
        """Create OpenVPN client configuration"""
        client_crt, client_key = self.generate_client_cert(username)
        
        # Read certificate files
        with open(self.ca_dir / "ca.crt", 'r') as f:
            ca_cert = f.read()
        
        with open(client_crt, 'r') as f:
            client_cert = f.read()
        
        with open(client_key, 'r') as f:
            client_key_content = f.read()
        
        with open(self.server_dir / "ta.key", 'r') as f:
            tls_auth = f.read()
        
        # Create client config
        config = f"""# Secure VPN Client Configuration
# Generated for: {username}
# Generated on: {time.strftime('%Y-%m-%d %H:%M:%S')}

client
dev tun
proto udp
remote {server_host} {server_port}
resolv-retry infinite
nobind
persist-key
persist-tun

# Authentication
auth-user-pass
auth-nocache

# Security
cipher AES-256-GCM
auth SHA256
tls-version-min 1.2
compress lz4-v2

# Certificates (embedded)
<ca>
{ca_cert}</ca>

<cert>
{client_cert}</cert>

<key>
{client_key_content}</key>

<tls-auth>
{tls_auth}</tls-auth>
key-direction 1

# Logging
verb 3
mute 20

# Windows-specific
route-method exe
route-delay 2
"""
        
        config_file = self.client_dir / f"{username}.ovpn"
        with open(config_file, 'w') as f:
            f.write(config)
        
        print(f"✅ Client config created: {config_file}")
        return config_file
    
    def setup_complete_server(self, server_host="localhost", server_port=1194):
        """Set up complete OpenVPN server"""
        print("🚀 Setting up complete OpenVPN server...")
        
        self.setup_ca()
        self.setup_server_certs()
        server_config = self.create_server_config(server_host, server_port)
        auth_script = self.create_auth_script()
        
        print("\n✅ OpenVPN server setup complete!")
        print(f"📁 Server config: {server_config}")
        print(f"🔐 Auth script: {auth_script}")
        print(f"📂 Base directory: {self.base_dir}")
        
        return server_config
    
    def create_user_profile(self, email, server_host="your-vpn-server.com"):
        """Create complete user profile with OpenVPN config"""
        # Get username from email
        username = email.split('@')[0]
        
        # Generate client config
        config_file = self.create_client_config(username, server_host)
        
        # Create user instructions
        instructions_file = self.client_dir / f"{username}_instructions.txt"
        instructions = f"""
Secure VPN - OpenVPN Setup Instructions
======================================

User: {email}
Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}

STEP 1: Download OpenVPN Client
- Windows: https://openvpn.net/community-downloads/
- macOS: Tunnelblick (https://tunnelblick.net/)
- Linux: sudo apt install openvpn (Ubuntu/Debian)
- Android: OpenVPN Connect (Google Play)
- iOS: OpenVPN Connect (App Store)

STEP 2: Import Configuration
- Use the file: {username}.ovpn
- When prompted for credentials, use:
  Username: {email}
  Password: [your account password]

STEP 3: Connect
- Select the imported profile
- Enter your credentials
- Click Connect

TROUBLESHOOTING:
- Ensure port {1194}/UDP is not blocked by firewall
- Try different servers if connection fails
- Contact support: <EMAIL>

Your OpenVPN profile is ready to use!
"""
        
        with open(instructions_file, 'w') as f:
            f.write(instructions)
        
        print(f"✅ User profile created for {email}")
        print(f"📄 Config file: {config_file}")
        print(f"📋 Instructions: {instructions_file}")
        
        return config_file, instructions_file

def main():
    """Main setup function"""
    print("🔒 Secure VPN - OpenVPN Integration Setup")
    print("=" * 50)
    
    manager = OpenVPNManager()
    
    # Check if OpenSSL is available
    try:
        subprocess.run(["openssl", "version"], check=True, capture_output=True)
        print("✅ OpenSSL found")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ OpenSSL not found. Please install OpenSSL first.")
        print("Windows: Download from https://slproweb.com/products/Win32OpenSSL.html")
        print("Linux: sudo apt install openssl")
        return
    
    # Setup server
    try:
        server_config = manager.setup_complete_server()
        
        print("\n🎯 Next Steps:")
        print("1. Install OpenVPN server:")
        print("   Windows: Download from https://openvpn.net/community-downloads/")
        print("   Linux: sudo apt install openvpn")
        
        print(f"\n2. Start OpenVPN server:")
        print(f"   sudo openvpn --config {server_config}")
        
        print("\n3. Create client profiles:")
        print("   python openvpn_integration.py --create-profile <EMAIL>")
        
        print("\n4. Configure firewall:")
        print("   sudo ufw allow 1194/udp")
        print("   # Or for Windows Firewall, allow port 1194 UDP")
        
        # Create sample client profile
        print("\n📱 Creating sample client profile...")
        config_file, instructions = manager.create_user_profile("<EMAIL>", "localhost")
        
        print(f"\n✅ Sample profile created!")
        print(f"📄 Download: {config_file}")
        print(f"📋 Instructions: {instructions}")
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        print("Make sure you have OpenSSL installed and accessible from command line")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--create-profile":
        if len(sys.argv) < 3:
            print("Usage: python openvpn_integration.py --create-profile <EMAIL>")
            sys.exit(1)
        
        email = sys.argv[2]
        manager = OpenVPNManager()
        manager.create_user_profile(email)
    else:
        main()
