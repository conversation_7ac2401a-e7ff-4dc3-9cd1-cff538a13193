#!/usr/bin/env python3
"""
Start OpenVPN Server - Bridge to your VPN
This creates an OpenVPN server that forwards to your custom VPN
"""

import socket
import threading
import time
import sys

class SimpleOpenVPNBridge:
    def __init__(self, listen_port=1194, target_host="127.0.0.1", target_port=8443):
        self.listen_port = listen_port
        self.target_host = target_host
        self.target_port = target_port
        self.running = False
    
    def handle_client(self, client_socket, addr):
        """Handle OpenVPN client connection"""
        print(f"📱 OpenVPN client connected from {addr}")
        
        try:
            # Connect to your VPN server
            vpn_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            vpn_socket.connect((self.target_host, self.target_port))
            print(f"🔗 Connected to VPN server at {self.target_host}:{self.target_port}")
            
            # Bridge the connections
            def forward_data(src, dst, direction):
                try:
                    while True:
                        data = src.recv(4096)
                        if not data:
                            break
                        dst.send(data)
                        print(f"📡 Forwarded {len(data)} bytes {direction}")
                except:
                    pass
                finally:
                    src.close()
                    dst.close()
            
            # Start forwarding in both directions
            thread1 = threading.Thread(target=forward_data, args=(client_socket, vpn_socket, "to VPN"))
            thread2 = threading.Thread(target=forward_data, args=(vpn_socket, client_socket, "to client"))
            
            thread1.start()
            thread2.start()
            
            thread1.join()
            thread2.join()
            
        except Exception as e:
            print(f"❌ Error handling client: {e}")
        finally:
            client_socket.close()
            print(f"🔌 Client {addr} disconnected")
    
    def start(self):
        """Start the OpenVPN bridge server"""
        print("🚀 Starting OpenVPN Bridge Server")
        print(f"🔌 Listening on port {self.listen_port}")
        print(f"🎯 Forwarding to {self.target_host}:{self.target_port}")
        print("🛑 Press Ctrl+C to stop")
        print("-" * 40)
        
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        try:
            server_socket.bind(("0.0.0.0", self.listen_port))
            server_socket.listen(5)
            self.running = True
            
            while self.running:
                try:
                    client_socket, addr = server_socket.accept()
                    # Handle each client in a separate thread
                    client_thread = threading.Thread(
                        target=self.handle_client, 
                        args=(client_socket, addr)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except KeyboardInterrupt:
                    break
                except Exception as e:
                    print(f"❌ Server error: {e}")
                    
        except Exception as e:
            print(f"❌ Failed to start server: {e}")
        finally:
            server_socket.close()
            self.running = False
            print("\n🛑 OpenVPN bridge server stopped")

def main():
    """Main function"""
    print("🔗 OpenVPN Bridge to Your VPN Server")
    print("=" * 40)
    
    # Create and start the bridge
    bridge = SimpleOpenVPNBridge(
        listen_port=1194,      # OpenVPN standard port
        target_host="127.0.0.1",  # Your VPN server
        target_port=8443       # Your VPN server port
    )
    
    try:
        bridge.start()
    except KeyboardInterrupt:
        print("\n🛑 Stopping server...")

if __name__ == "__main__":
    main()
