# Simple OpenVPN Configuration
# Works with any OpenVPN client

client
dev tun
proto udp
remote ************* 1194
resolv-retry infinite
nobind
persist-key
persist-tun

# Authentication
auth-user-pass
auth-nocache

# Security (basic)
cipher AES-256-CBC
auth SHA1

# Disable certificate verification for testing
verify-x509-name ************* name-prefix
remote-cert-tls server

# Logging
verb 3
mute 20

# Windows compatibility
route-method exe
route-delay 2

# DNS
dhcp-option DNS *******
dhcp-option DNS *******
