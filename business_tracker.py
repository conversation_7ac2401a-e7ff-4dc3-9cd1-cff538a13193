#!/usr/bin/env python3
"""
Business Tracker - Track your VPN-in-a-Box business metrics
"""

import tkinter as tk
from tkinter import ttk
import json
import datetime
from pathlib import Path

class BusinessTracker:
    def __init__(self):
        self.data_file = Path("business_data.json")
        self.load_data()
        
        self.root = tk.Tk()
        self.setup_ui()
    
    def load_data(self):
        """Load business data"""
        if self.data_file.exists():
            with open(self.data_file, 'r') as f:
                self.data = json.load(f)
        else:
            self.data = {
                'customers': [],
                'revenue': 0,
                'signups': [],
                'goals': {
                    'week1_customers': 10,
                    'month1_revenue': 500,
                    'month3_revenue': 5000
                }
            }
    
    def save_data(self):
        """Save business data"""
        with open(self.data_file, 'w') as f:
            json.dump(self.data, f, indent=2)
    
    def setup_ui(self):
        """Setup business dashboard UI"""
        self.root.title("VPN-in-a-Box Business Dashboard")
        self.root.geometry("800x600")
        self.root.configure(bg="#1e1e1e")
        
        # Header
        header = tk.Frame(self.root, bg="#2c3e50", height=80)
        header.pack(fill=tk.X)
        header.pack_propagate(False)
        
        title = tk.Label(header, text="📊 VPN-in-a-Box Business Dashboard", 
                        font=("Arial", 18, "bold"), fg="white", bg="#2c3e50")
        title.pack(expand=True)
        
        # Main content
        main_frame = tk.Frame(self.root, bg="#1e1e1e")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Metrics row
        metrics_frame = tk.Frame(main_frame, bg="#1e1e1e")
        metrics_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Revenue card
        self.create_metric_card(metrics_frame, "💰 Total Revenue", 
                               f"${self.data['revenue']}", "#27ae60")
        
        # Customers card
        customer_count = len(self.data['customers'])
        self.create_metric_card(metrics_frame, "👥 Customers", 
                               str(customer_count), "#3498db")
        
        # Signups today
        today_signups = len([s for s in self.data['signups'] 
                           if s['date'] == datetime.date.today().isoformat()])
        self.create_metric_card(metrics_frame, "📈 Today's Signups", 
                               str(today_signups), "#e74c3c")
        
        # Goals progress
        goals_frame = tk.Frame(main_frame, bg="#2d2d2d", relief=tk.RAISED, bd=1)
        goals_frame.pack(fill=tk.X, pady=(0, 20))
        
        goals_title = tk.Label(goals_frame, text="🎯 Goals Progress", 
                              font=("Arial", 14, "bold"), fg="white", bg="#2d2d2d")
        goals_title.pack(pady=10)
        
        # Week 1 goal
        week1_progress = min(customer_count / self.data['goals']['week1_customers'] * 100, 100)
        self.create_progress_bar(goals_frame, "Week 1: 10 Customers", week1_progress)
        
        # Month 1 goal
        month1_progress = min(self.data['revenue'] / self.data['goals']['month1_revenue'] * 100, 100)
        self.create_progress_bar(goals_frame, "Month 1: $500 Revenue", month1_progress)
        
        # Actions frame
        actions_frame = tk.Frame(main_frame, bg="#2d2d2d", relief=tk.RAISED, bd=1)
        actions_frame.pack(fill=tk.X, pady=(0, 20))
        
        actions_title = tk.Label(actions_frame, text="🚀 Quick Actions", 
                                font=("Arial", 14, "bold"), fg="white", bg="#2d2d2d")
        actions_title.pack(pady=10)
        
        btn_frame = tk.Frame(actions_frame, bg="#2d2d2d")
        btn_frame.pack(pady=(0, 15))
        
        tk.Button(btn_frame, text="Add Customer", command=self.add_customer,
                 bg="#27ae60", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
        
        tk.Button(btn_frame, text="Record Sale", command=self.record_sale,
                 bg="#3498db", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
        
        tk.Button(btn_frame, text="Launch Marketing", command=self.launch_marketing,
                 bg="#e74c3c", fg="white", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
        
        # Recent activity
        activity_frame = tk.Frame(main_frame, bg="#2d2d2d", relief=tk.RAISED, bd=1)
        activity_frame.pack(fill=tk.BOTH, expand=True)
        
        activity_title = tk.Label(activity_frame, text="📋 Recent Activity", 
                                 font=("Arial", 14, "bold"), fg="white", bg="#2d2d2d")
        activity_title.pack(pady=10)
        
        self.activity_text = tk.Text(activity_frame, height=10, bg="#1e1e1e", fg="#ffffff",
                                    font=("Consolas", 10))
        self.activity_text.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))
        
        self.update_activity_log()
    
    def create_metric_card(self, parent, title, value, color):
        """Create a metric display card"""
        card = tk.Frame(parent, bg=color, width=200, height=100)
        card.pack(side=tk.LEFT, padx=10, fill=tk.Y)
        card.pack_propagate(False)
        
        tk.Label(card, text=title, font=("Arial", 10), 
                fg="white", bg=color).pack(pady=(15, 5))
        tk.Label(card, text=value, font=("Arial", 20, "bold"), 
                fg="white", bg=color).pack()
    
    def create_progress_bar(self, parent, label, progress):
        """Create a progress bar with label"""
        frame = tk.Frame(parent, bg="#2d2d2d")
        frame.pack(fill=tk.X, padx=15, pady=5)
        
        tk.Label(frame, text=label, font=("Arial", 10), 
                fg="white", bg="#2d2d2d").pack(anchor=tk.W)
        
        progress_bar = ttk.Progressbar(frame, length=300, mode='determinate')
        progress_bar.pack(anchor=tk.W, pady=(5, 10))
        progress_bar['value'] = progress
        
        tk.Label(frame, text=f"{progress:.1f}%", font=("Arial", 9), 
                fg="#cccccc", bg="#2d2d2d").pack(anchor=tk.W)
    
    def add_customer(self):
        """Add a new customer"""
        # Simple dialog for demo
        customer_name = tk.simpledialog.askstring("New Customer", "Customer email:")
        if customer_name:
            customer = {
                'email': customer_name,
                'signup_date': datetime.date.today().isoformat(),
                'plan': 'basic',
                'revenue': 50
            }
            self.data['customers'].append(customer)
            self.data['revenue'] += 50
            self.save_data()
            self.refresh_dashboard()
    
    def record_sale(self):
        """Record a sale"""
        amount = tk.simpledialog.askfloat("Record Sale", "Sale amount ($):")
        if amount:
            self.data['revenue'] += amount
            self.save_data()
            self.refresh_dashboard()
    
    def launch_marketing(self):
        """Launch marketing campaign"""
        campaigns = [
            "🐦 Twitter/X announcement",
            "📰 Hacker News submission", 
            "📱 Product Hunt launch",
            "📝 Blog post publication",
            "🎥 Demo video release"
        ]
        
        # Show marketing options
        marketing_window = tk.Toplevel(self.root)
        marketing_window.title("Marketing Campaigns")
        marketing_window.geometry("400x300")
        marketing_window.configure(bg="#1e1e1e")
        
        tk.Label(marketing_window, text="🚀 Launch Marketing Campaign", 
                font=("Arial", 14, "bold"), fg="white", bg="#1e1e1e").pack(pady=20)
        
        for campaign in campaigns:
            tk.Button(marketing_window, text=campaign, 
                     command=lambda c=campaign: self.execute_campaign(c),
                     bg="#3498db", fg="white", width=30).pack(pady=5)
    
    def execute_campaign(self, campaign):
        """Execute a marketing campaign"""
        # Add to activity log
        activity = f"{datetime.datetime.now().strftime('%H:%M')} - Launched: {campaign}"
        self.activity_text.insert(tk.END, activity + "\n")
        self.activity_text.see(tk.END)
    
    def update_activity_log(self):
        """Update the activity log"""
        activities = [
            "09:00 - VPN server started",
            "09:15 - Professional client launched", 
            "09:30 - Speed test feature added",
            "09:45 - Kill switch implemented",
            "10:00 - Business dashboard created",
            "10:15 - Ready for first customers!"
        ]
        
        for activity in activities:
            self.activity_text.insert(tk.END, activity + "\n")
    
    def refresh_dashboard(self):
        """Refresh the dashboard"""
        self.root.destroy()
        self.__init__()
        self.run()
    
    def run(self):
        """Run the dashboard"""
        self.root.mainloop()

if __name__ == "__main__":
    import tkinter.simpledialog
    
    dashboard = BusinessTracker()
    dashboard.run()
