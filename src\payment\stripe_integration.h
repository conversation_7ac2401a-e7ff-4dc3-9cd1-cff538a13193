#ifndef STRIPE_INTEGRATION_H
#define STRIPE_INTEGRATION_H

#include <stdint.h>
#include <stdbool.h>
#include <time.h>

// Stripe payment integration for VPN service
typedef enum {
    PAYMENT_SUCCESS = 0,
    PAYMENT_ERROR_INVALID_PARAM = -1,
    PAYMENT_ERROR_NETWORK = -2,
    PAYMENT_ERROR_AUTH = -3,
    PAYMENT_ERROR_DECLINED = -4,
    PAYMENT_ERROR_INSUFFICIENT_FUNDS = -5,
    PAYMENT_ERROR_EXPIRED_CARD = -6,
    PAYMENT_ERROR_INVALID_CARD = -7,
    PAYMENT_ERROR_PROCESSING = -8,
    PAYMENT_ERROR_WEBHOOK = -9
} payment_error_t;

typedef enum {
    PAYMENT_STATUS_PENDING = 0,
    PAYMENT_STATUS_SUCCEEDED = 1,
    PAYMENT_STATUS_FAILED = 2,
    PAYMENT_STATUS_CANCELED = 3,
    PAYMENT_STATUS_REFUNDED = 4
} payment_status_t;

typedef enum {
    SUBSCRIPTION_STATUS_ACTIVE = 0,
    SUBSCRIPTION_STATUS_PAST_DUE = 1,
    SUBSCRIPTION_STATUS_CANCELED = 2,
    SUBSCRIPTION_STATUS_UNPAID = 3,
    SUBSCRIPTION_STATUS_INCOMPLETE = 4
} subscription_status_t;

// Stripe customer information
typedef struct {
    char customer_id[64];        // Stripe customer ID
    char email[256];
    char name[128];
    char payment_method_id[64];  // Default payment method
    time_t created_at;
    bool is_active;
} stripe_customer_t;

// Stripe subscription information
typedef struct {
    char subscription_id[64];    // Stripe subscription ID
    char customer_id[64];
    char price_id[64];          // Stripe price ID for the plan
    subscription_status_t status;
    time_t current_period_start;
    time_t current_period_end;
    time_t trial_end;
    bool cancel_at_period_end;
    double amount;              // Amount in dollars
    char currency[4];
} stripe_subscription_t;

// Payment intent information
typedef struct {
    char payment_intent_id[64]; // Stripe payment intent ID
    char customer_id[64];
    double amount;
    char currency[4];
    payment_status_t status;
    char description[256];
    time_t created_at;
    char failure_reason[128];
} stripe_payment_intent_t;

// Webhook event information
typedef struct {
    char event_id[64];
    char event_type[64];        // e.g., "payment_intent.succeeded"
    char object_id[64];         // ID of the object that triggered the event
    time_t created_at;
    bool processed;
} stripe_webhook_event_t;

// Stripe configuration
typedef struct {
    char secret_key[128];       // Stripe secret key
    char publishable_key[128];  // Stripe publishable key
    char webhook_secret[128];   // Webhook endpoint secret
    char api_version[16];       // Stripe API version
    bool test_mode;             // Whether in test mode
} stripe_config_t;

// Price configuration for different plans
typedef struct {
    char price_id[64];          // Stripe price ID
    subscription_plan_t plan;
    double amount;              // Monthly amount in dollars
    char currency[4];
    char interval[16];          // "month" or "year"
    int interval_count;         // Number of intervals
} stripe_price_config_t;

// Initialize Stripe integration
payment_error_t stripe_init(const stripe_config_t *config);
void stripe_cleanup(void);

// Customer management
payment_error_t stripe_create_customer(const char *email, const char *name, 
                                      stripe_customer_t *customer);
payment_error_t stripe_get_customer(const char *customer_id, stripe_customer_t *customer);
payment_error_t stripe_update_customer(const stripe_customer_t *customer);
payment_error_t stripe_delete_customer(const char *customer_id);

// Subscription management
payment_error_t stripe_create_subscription(const char *customer_id, const char *price_id,
                                          stripe_subscription_t *subscription);
payment_error_t stripe_get_subscription(const char *subscription_id, 
                                       stripe_subscription_t *subscription);
payment_error_t stripe_update_subscription(const char *subscription_id, const char *new_price_id);
payment_error_t stripe_cancel_subscription(const char *subscription_id, bool at_period_end);

// Payment processing
payment_error_t stripe_create_payment_intent(const char *customer_id, double amount,
                                            const char *currency, const char *description,
                                            stripe_payment_intent_t *payment_intent);
payment_error_t stripe_confirm_payment_intent(const char *payment_intent_id,
                                             const char *payment_method_id);
payment_error_t stripe_get_payment_intent(const char *payment_intent_id,
                                         stripe_payment_intent_t *payment_intent);

// Webhook handling
payment_error_t stripe_verify_webhook_signature(const char *payload, const char *signature,
                                               const char *webhook_secret);
payment_error_t stripe_process_webhook_event(const char *payload, 
                                            stripe_webhook_event_t *event);

// Price management
payment_error_t stripe_get_price_for_plan(subscription_plan_t plan, stripe_price_config_t *price);
payment_error_t stripe_create_checkout_session(const char *customer_id, const char *price_id,
                                              const char *success_url, const char *cancel_url,
                                              char *session_url, size_t url_size);

// Utility functions
const char* payment_error_string(payment_error_t error);
const char* payment_status_string(payment_status_t status);
const char* subscription_status_string(subscription_status_t status);
double stripe_get_plan_price(subscription_plan_t plan);
const char* stripe_get_plan_price_id(subscription_plan_t plan);

// Integration with VPN user management
payment_error_t stripe_sync_user_subscription(uint64_t user_id, const char *customer_id);
payment_error_t stripe_handle_subscription_updated(const char *subscription_id);
payment_error_t stripe_handle_payment_succeeded(const char *payment_intent_id);
payment_error_t stripe_handle_payment_failed(const char *payment_intent_id);

#endif // STRIPE_INTEGRATION_H
