#!/usr/bin/env python3
"""
COMPLETE OPENVPN SETUP - ONE CLICK SOLUTION
This will get everything working automatically
"""

import os
import sys
import subprocess
import time
import urllib.request
from pathlib import Path

def print_banner():
    print("🚀 COMPLETE OPENVPN SETUP")
    print("=" * 50)
    print("This will:")
    print("✅ Install OpenVPN")
    print("✅ Configure certificates")
    print("✅ Setup firewall")
    print("✅ Create test user")
    print("✅ Generate .ovpn file")
    print("✅ Start VPN server")
    print("=" * 50)

def check_admin():
    """Check and request admin privileges"""
    try:
        import ctypes
        if not ctypes.windll.shell32.IsUserAnAdmin():
            print("🔐 Requesting administrator privileges...")
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            return False
        return True
    except:
        return True

def install_openvpn():
    """Install OpenVPN if not present"""
    openvpn_exe = r"C:\Program Files\OpenVPN\bin\openvpn.exe"
    
    if os.path.exists(openvpn_exe):
        print("✅ OpenVPN already installed")
        return True
    
    print("📥 Installing OpenVPN...")
    
    # Try winget first
    try:
        subprocess.run([
            "winget", "install", "OpenVPNTechnologies.OpenVPN", 
            "--accept-source-agreements", "--accept-package-agreements", "--silent"
        ], check=True, capture_output=True, timeout=300)
        print("✅ OpenVPN installed via winget")
        return True
    except:
        pass
    
    # Download and install manually
    try:
        url = "https://swupdate.openvpn.org/community/releases/OpenVPN-2.6.8-I001-amd64.msi"
        installer = "openvpn_installer.msi"
        
        print("📥 Downloading OpenVPN...")
        urllib.request.urlretrieve(url, installer)
        
        print("🔧 Installing OpenVPN...")
        subprocess.run([
            "msiexec", "/i", installer, "/quiet", "/norestart"
        ], check=True, timeout=300)
        
        os.remove(installer)
        print("✅ OpenVPN installed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Failed to install OpenVPN: {e}")
        return False

def setup_certificates():
    """Setup OpenVPN certificates and configuration"""
    print("🔐 Setting up certificates...")
    
    try:
        # Run the OpenVPN integration script
        subprocess.run([sys.executable, "openvpn_integration.py"], check=True)
        print("✅ Certificates created")
        return True
    except Exception as e:
        print(f"❌ Certificate setup failed: {e}")
        return False

def create_test_user():
    """Create test user and generate profile"""
    print("👤 Creating test user...")
    
    try:
        # Create user
        subprocess.run([
            sys.executable, "vpn_manager_tool.py",
            "--create-user", "<EMAIL>",
            "--plan", "premium"
        ], check=True)
        
        # Generate profile
        import socket
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        
        subprocess.run([
            sys.executable, "vpn_manager_tool.py",
            "--generate-profiles", "<EMAIL>",
            "--server", local_ip
        ], check=True)
        
        print("✅ Test user created")
        return True
        
    except Exception as e:
        print(f"❌ User creation failed: {e}")
        return False

def configure_system():
    """Configure Windows system for VPN"""
    print("🔧 Configuring Windows system...")
    
    try:
        # Configure firewall
        subprocess.run([
            "netsh", "advfirewall", "firewall", "add", "rule",
            "name=OpenVPN", "dir=in", "action=allow",
            "protocol=UDP", "localport=1194"
        ], check=True, capture_output=True)
        
        # Enable IP forwarding
        subprocess.run([
            "reg", "add",
            "HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\Tcpip\\Parameters",
            "/v", "IPEnableRouter", "/t", "REG_DWORD", "/d", "1", "/f"
        ], check=True, capture_output=True)
        
        print("✅ System configured")
        return True
        
    except Exception as e:
        print(f"❌ System configuration failed: {e}")
        return False

def start_server():
    """Start the OpenVPN server"""
    print("🎯 Starting OpenVPN server...")
    
    config_file = Path("openvpn/server/server.conf")
    if not config_file.exists():
        print("❌ Server config not found")
        return False
    
    try:
        openvpn_exe = r"C:\Program Files\OpenVPN\bin\openvpn.exe"
        
        # Change to server directory
        os.chdir("openvpn/server")
        
        print("🚀 OpenVPN server is starting...")
        print("🌐 Listening on port 1194/UDP")
        print("📱 Client file: ../../vpn_profiles/test_openvpn.ovpn")
        print("🔑 Login: <EMAIL> / password")
        print("🛑 Press Ctrl+C to stop server")
        print("-" * 50)
        
        # Start server
        subprocess.run([openvpn_exe, "--config", "server.conf"])
        
    except KeyboardInterrupt:
        print("\n🛑 OpenVPN server stopped")
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        return False
    
    return True

def main():
    """Main setup function"""
    print_banner()
    
    # Check admin privileges
    if not check_admin():
        return
    
    print("✅ Running with administrator privileges")
    
    # Step 1: Install OpenVPN
    if not install_openvpn():
        print("❌ Setup failed at OpenVPN installation")
        return
    
    # Wait a moment for installation to complete
    time.sleep(3)
    
    # Step 2: Setup certificates
    if not setup_certificates():
        print("❌ Setup failed at certificate creation")
        return
    
    # Step 3: Create test user
    if not create_test_user():
        print("❌ Setup failed at user creation")
        return
    
    # Step 4: Configure system
    if not configure_system():
        print("❌ Setup failed at system configuration")
        return
    
    print("\n🎉 SETUP COMPLETE!")
    print("=" * 50)
    print("📱 Your VPN profile: vpn_profiles/test_openvpn.ovpn")
    print("🔑 Username: <EMAIL>")
    print("🔑 Password: password")
    print("\n📲 Download OpenVPN Connect:")
    print("   https://openvpn.net/client/")
    print("\n🔌 To connect:")
    print("   1. Import the .ovpn file")
    print("   2. Enter username/password")
    print("   3. Connect!")
    print("\n🚀 Starting server in 5 seconds...")
    
    for i in range(5, 0, -1):
        print(f"   {i}...")
        time.sleep(1)
    
    # Step 5: Start server
    start_server()

if __name__ == "__main__":
    main()
