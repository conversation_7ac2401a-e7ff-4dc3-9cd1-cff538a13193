apiVersion: v1
kind: Namespace
metadata:
  name: securevpn
  labels:
    name: securevpn
    app.kubernetes.io/name: securevpn
    app.kubernetes.io/version: "1.0.0"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: securevpn-config
  namespace: securevpn
data:
  vpn-port: "8443"
  api-port: "8080"
  metrics-port: "9090"
  max-clients: "1000"
  log-level: "INFO"
  database-path: "/data/vpn.db"
  private-key-file: "/ssl/server.key"
  public-key-file: "/ssl/server.pub"
---
apiVersion: v1
kind: Secret
metadata:
  name: securevpn-secrets
  namespace: securevpn
type: Opaque
data:
  # Base64 encoded values - replace with actual secrets
  stripe-secret-key: ""
  stripe-webhook-secret: ""
  postgres-password: ""
  redis-password: ""
  admin-password: ""
---
apiVersion: v1
kind: Secret
metadata:
  name: securevpn-ssl
  namespace: securevpn
type: kubernetes.io/tls
data:
  # Base64 encoded SSL certificates
  tls.crt: ""
  tls.key: ""
  server.key: ""
  server.pub: ""
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: securevpn-data
  namespace: securevpn
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: securevpn-logs
  namespace: securevpn
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard
---
apiVersion: v1
kind: Service
metadata:
  name: securevpn-server
  namespace: securevpn
  labels:
    app: securevpn-server
spec:
  type: LoadBalancer
  ports:
    - name: vpn
      port: 8443
      targetPort: 8443
      protocol: UDP
    - name: api
      port: 8080
      targetPort: 8080
      protocol: TCP
    - name: metrics
      port: 9090
      targetPort: 9090
      protocol: TCP
  selector:
    app: securevpn-server
---
apiVersion: v1
kind: Service
metadata:
  name: securevpn-postgres
  namespace: securevpn
  labels:
    app: securevpn-postgres
spec:
  type: ClusterIP
  ports:
    - port: 5432
      targetPort: 5432
  selector:
    app: securevpn-postgres
---
apiVersion: v1
kind: Service
metadata:
  name: securevpn-redis
  namespace: securevpn
  labels:
    app: securevpn-redis
spec:
  type: ClusterIP
  ports:
    - port: 6379
      targetPort: 6379
  selector:
    app: securevpn-redis
