#!/usr/bin/env python3
"""
Secure VPN Client - Working Implementation
A functional VPN client that connects to the VPN server
"""

import socket
import json
import time
import threading
import sys
import argparse

class VPNClient:
    def __init__(self, server_host='localhost', server_port=8443):
        self.server_host = server_host
        self.server_port = server_port
        self.connected = False
        self.socket = None
        self.session_info = None
        
    def connect_with_credentials(self, email, password):
        """Connect using email/password authentication"""
        auth_data = {
            'type': 'credentials',
            'email': email,
            'password': password
        }
        return self._connect(auth_data)
    
    def connect_with_license(self, license_key):
        """Connect using license key authentication"""
        auth_data = {
            'type': 'license',
            'license_key': license_key
        }
        return self._connect(auth_data)
    
    def _connect(self, auth_data):
        """Internal connection method"""
        try:
            print(f"Connecting to VPN server at {self.server_host}:{self.server_port}")
            
            # Create socket connection
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.server_host, self.server_port))
            
            # Send authentication data
            auth_json = json.dumps(auth_data)
            self.socket.send(auth_json.encode('utf-8'))
            
            # Receive response
            response_data = self.socket.recv(1024).decode('utf-8')
            
            if response_data == 'AUTH_FAILED':
                print("Authentication failed")
                return False
            elif response_data == 'LIMIT_EXCEEDED':
                print("Connection limit exceeded for your account")
                return False
            
            # Parse successful response
            try:
                self.session_info = json.loads(response_data)
                if self.session_info.get('status') == 'success':
                    self.connected = True
                    print("Connected to VPN server!")
                    print(f"   Session ID: {self.session_info['session_id']}")
                    print(f"   Virtual IP: {self.session_info['virtual_ip']}")
                    print(f"   Plan: {self.session_info['plan']}")
                    print(f"   DNS Servers: {', '.join(self.session_info['dns_servers'])}")
                    return True
                else:
                    print(f"❌ Connection failed: {self.session_info}")
                    return False
            except json.JSONDecodeError:
                print(f"❌ Invalid response from server: {response_data}")
                return False
                
        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False
    
    def send_test_data(self, message="Hello VPN Server!"):
        """Send test data through the VPN tunnel"""
        if not self.connected:
            print("❌ Not connected to VPN")
            return False
        
        try:
            print(f"📤 Sending: {message}")
            self.socket.send(message.encode('utf-8'))
            
            # Receive echo response
            response = self.socket.recv(1024).decode('utf-8')
            print(f"📥 Received: {response}")
            
            return True
        except Exception as e:
            print(f"❌ Error sending data: {e}")
            return False
    
    def start_traffic_simulation(self, duration=30):
        """Simulate VPN traffic for testing"""
        if not self.connected:
            print("❌ Not connected to VPN")
            return
        
        print(f"🚀 Starting traffic simulation for {duration} seconds...")
        
        def send_periodic_data():
            start_time = time.time()
            packet_count = 0
            
            while time.time() - start_time < duration and self.connected:
                try:
                    # Send test packet
                    test_data = f"Test packet #{packet_count} at {time.time()}"
                    self.socket.send(test_data.encode('utf-8'))
                    
                    # Receive response
                    response = self.socket.recv(1024)
                    packet_count += 1
                    
                    if packet_count % 10 == 0:
                        print(f"📊 Sent {packet_count} packets...")
                    
                    time.sleep(1)  # Send packet every second
                    
                except Exception as e:
                    print(f"❌ Error in traffic simulation: {e}")
                    break
            
            print(f"✅ Traffic simulation completed. Sent {packet_count} packets.")
        
        # Start traffic in separate thread
        traffic_thread = threading.Thread(target=send_periodic_data)
        traffic_thread.daemon = True
        traffic_thread.start()
        
        return traffic_thread
    
    def disconnect(self):
        """Disconnect from VPN server"""
        if self.connected:
            print("🔌 Disconnecting from VPN server...")
            self.connected = False
            
            if self.socket:
                try:
                    self.socket.close()
                except:
                    pass
                self.socket = None
            
            print("✅ Disconnected from VPN")
    
    def get_status(self):
        """Get current connection status"""
        if self.connected and self.session_info:
            return {
                'connected': True,
                'session_id': self.session_info['session_id'],
                'virtual_ip': self.session_info['virtual_ip'],
                'plan': self.session_info['plan'],
                'server': f"{self.server_host}:{self.server_port}"
            }
        else:
            return {'connected': False}

def main():
    """Main function for VPN client"""
    parser = argparse.ArgumentParser(description='Secure VPN Client')
    parser.add_argument('--server', default='localhost', help='VPN server address')
    parser.add_argument('--port', type=int, default=8443, help='VPN server port')
    parser.add_argument('--email', help='Email for authentication')
    parser.add_argument('--password', help='Password for authentication')
    parser.add_argument('--license', help='License key for authentication')
    parser.add_argument('--test', action='store_true', help='Run traffic test after connecting')
    parser.add_argument('--duration', type=int, default=30, help='Test duration in seconds')
    
    args = parser.parse_args()
    
    print("Secure VPN Client")
    print("=" * 30)
    
    # Create client
    client = VPNClient(args.server, args.port)
    
    try:
        # Authenticate
        if args.license:
            success = client.connect_with_license(args.license)
        elif args.email and args.password:
            success = client.connect_with_credentials(args.email, args.password)
        else:
            # Use default admin credentials for demo
            print("Using default admin credentials for demo...")
            success = client.connect_with_credentials('<EMAIL>', 'admin123')
        
        if not success:
            print("❌ Failed to connect to VPN")
            return 1
        
        # Run test if requested
        if args.test:
            traffic_thread = client.start_traffic_simulation(args.duration)
            
            # Wait for test to complete
            traffic_thread.join()
        else:
            # Interactive mode
            print("\n📋 VPN Client Commands:")
            print("  'test' - Send test message")
            print("  'traffic' - Start traffic simulation")
            print("  'status' - Show connection status")
            print("  'quit' - Disconnect and exit")
            print()
            
            while client.connected:
                try:
                    command = input("VPN> ").strip().lower()
                    
                    if command == 'quit':
                        break
                    elif command == 'test':
                        message = input("Enter message (or press Enter for default): ").strip()
                        if not message:
                            message = "Hello VPN Server!"
                        client.send_test_data(message)
                    elif command == 'traffic':
                        duration = input("Duration in seconds (default 30): ").strip()
                        duration = int(duration) if duration.isdigit() else 30
                        traffic_thread = client.start_traffic_simulation(duration)
                        print("Traffic simulation started. Type 'quit' to stop.")
                    elif command == 'status':
                        status = client.get_status()
                        print(f"📊 Status: {json.dumps(status, indent=2)}")
                    elif command == '':
                        continue
                    else:
                        print("❓ Unknown command. Type 'quit' to exit.")
                        
                except KeyboardInterrupt:
                    break
                except EOFError:
                    break
    
    except KeyboardInterrupt:
        print("\n🛑 Client stopped by user")
    finally:
        client.disconnect()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
