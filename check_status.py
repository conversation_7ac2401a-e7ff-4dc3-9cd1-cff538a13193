#!/usr/bin/env python3
"""
Quick Status Check - Show current VPN service status
"""

import sqlite3
import requests
import json
from datetime import datetime

def check_database():
    """Check database status"""
    try:
        conn = sqlite3.connect("vpn_production.db")
        cursor = conn.cursor()
        
        # Get user count
        cursor.execute('SELECT COUNT(*) FROM users WHERE is_active = 1')
        total_users = cursor.fetchone()[0]
        
        # Get session count
        cursor.execute('SELECT COUNT(*) FROM sessions')
        total_sessions = cursor.fetchone()[0]
        
        # Get active sessions
        cursor.execute('SELECT COUNT(*) FROM sessions WHERE is_active = 1')
        active_sessions = cursor.fetchone()[0]
        
        # Get traffic stats
        cursor.execute('SELECT SUM(bytes_sent), SUM(bytes_received) FROM sessions')
        traffic = cursor.fetchone()
        bytes_sent = traffic[0] or 0
        bytes_received = traffic[1] or 0
        
        conn.close()
        
        print("DATABASE STATUS:")
        print(f"  Total Users: {total_users}")
        print(f"  Total Sessions: {total_sessions}")
        print(f"  Active Sessions: {active_sessions}")
        print(f"  Bytes Sent: {bytes_sent:,}")
        print(f"  Bytes Received: {bytes_received:,}")
        print(f"  Total Traffic: {(bytes_sent + bytes_received) / (1024*1024):.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"DATABASE ERROR: {e}")
        return False

def check_api():
    """Check API status"""
    try:
        # Check health
        response = requests.get("http://localhost:8090/health", timeout=5)
        if response.status_code == 200:
            print("API HEALTH: OK")
        else:
            print(f"API HEALTH: ERROR {response.status_code}")
            return False
        
        # Check stats
        response = requests.get("http://localhost:8090/api/stats", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print("API STATS:")
            print(f"  Total Users: {stats['total_users']}")
            print(f"  Active Connections: {stats['active_connections']}")
            print(f"  Total Bandwidth: {stats['total_bandwidth_gb']} GB")
            print(f"  Monthly Revenue: ${stats['monthly_revenue']}")
            print(f"  Success Rate: {stats['success_rate']}%")
        else:
            print(f"API STATS: ERROR {response.status_code}")
            return False
            
        return True
        
    except Exception as e:
        print(f"API ERROR: {e}")
        return False

def check_vpn_server():
    """Check VPN server status"""
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('localhost', 8443))
        sock.close()
        
        if result == 0:
            print("VPN SERVER: RUNNING (Port 8443)")
            return True
        else:
            print("VPN SERVER: NOT RESPONDING (Port 8443)")
            return False
            
    except Exception as e:
        print(f"VPN SERVER ERROR: {e}")
        return False

def main():
    print("=" * 50)
    print("SECURE VPN SERVICE STATUS CHECK")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    db_ok = check_database()
    print()
    
    api_ok = check_api()
    print()
    
    vpn_ok = check_vpn_server()
    print()
    
    print("=" * 50)
    if db_ok and api_ok and vpn_ok:
        print("OVERALL STATUS: ALL SYSTEMS OPERATIONAL")
        print("Dashboard: http://localhost:8090/docs/dashboard.html")
    else:
        print("OVERALL STATUS: SOME ISSUES DETECTED")
    print("=" * 50)

if __name__ == "__main__":
    main()
