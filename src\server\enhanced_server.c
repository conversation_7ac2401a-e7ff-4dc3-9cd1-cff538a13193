#include "securevpn.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <signal.h>
#include <errno.h>
#include <pthread.h>

// Enhanced server configuration
typedef struct {
    uint16_t vpn_port;
    uint16_t api_port;
    char database_path[512];
    char private_key_file[256];
    char public_key_file[256];
    bool verbose;
    int max_clients;
} enhanced_server_config_t;

// Enhanced client session with database integration
typedef struct {
    int socket_fd;
    struct sockaddr_in client_addr;
    socklen_t client_addr_len;
    uint8_t shared_secret[X25519_KEY_SIZE];
    bool authenticated;
    time_t last_activity;
    pthread_t thread;
    
    // Database-backed session info
    uint64_t session_id;
    uint64_t user_id;
    user_account_t user_info;
    license_record_t license_info;
    uint64_t bytes_sent;
    uint64_t bytes_received;
} enhanced_client_session_t;

// Global server state
static enhanced_client_session_t *client_sessions = NULL;
static int max_clients = 50;
static int server_socket = -1;
static bool running = true;
static pthread_mutex_t sessions_mutex = PTHREAD_MUTEX_INITIALIZER;

// External license manager functions
extern svpn_error_t license_manager_init(const char *db_path, const char *private_key_file, 
                                        const char *public_key_file);
extern void license_manager_cleanup(void);
extern svpn_error_t license_manager_validate_license(const char *license_key, user_account_t *user,
                                                    license_record_t *license);
extern svpn_error_t license_manager_check_session_limit(uint64_t user_id, bool *can_connect);
extern svpn_error_t license_manager_create_session(uint64_t user_id, const char *client_ip,
                                                  session_record_t *session);
extern svpn_error_t license_manager_update_session(uint64_t session_id, uint64_t bytes_sent, 
                                                  uint64_t bytes_received);
extern svpn_error_t license_manager_end_session(uint64_t session_id);

// Signal handler
static void signal_handler(int sig) {
    printf("\nReceived signal %d, shutting down...\n", sig);
    running = false;
    if (server_socket >= 0) {
        close(server_socket);
    }
}

// Find free session slot
static enhanced_client_session_t* find_free_session(void) {
    pthread_mutex_lock(&sessions_mutex);
    
    for (int i = 0; i < max_clients; i++) {
        if (client_sessions[i].socket_fd == -1) {
            pthread_mutex_unlock(&sessions_mutex);
            return &client_sessions[i];
        }
    }
    
    pthread_mutex_unlock(&sessions_mutex);
    return NULL;
}

// Cleanup session
static void cleanup_session(enhanced_client_session_t *session) {
    if (!session) return;
    
    pthread_mutex_lock(&sessions_mutex);
    
    if (session->socket_fd >= 0) {
        close(session->socket_fd);
        session->socket_fd = -1;
    }
    
    // End database session if active
    if (session->session_id > 0) {
        license_manager_end_session(session->session_id);
        session->session_id = 0;
    }
    
    // Clear sensitive data
    secure_zero(&session->shared_secret, sizeof(session->shared_secret));
    secure_zero(&session->user_info, sizeof(session->user_info));
    secure_zero(&session->license_info, sizeof(session->license_info));
    
    session->authenticated = false;
    session->user_id = 0;
    session->bytes_sent = 0;
    session->bytes_received = 0;
    
    pthread_mutex_unlock(&sessions_mutex);
}

// Enhanced client handler with database integration
static void* handle_enhanced_client(void *arg) {
    enhanced_client_session_t *session = (enhanced_client_session_t*)arg;
    vpn_packet_t packet;
    x25519_keypair_t server_keypair;
    svpn_error_t result;
    ssize_t bytes_received;
    
    char client_ip[INET_ADDRSTRLEN];
    inet_ntop(AF_INET, &session->client_addr.sin_addr, client_ip, INET_ADDRSTRLEN);
    
    printf("New client connected from %s:%d\n", 
           client_ip, ntohs(session->client_addr.sin_port));
    
    // Generate server keypair for this session
    result = x25519_generate_keypair(&server_keypair);
    if (result != SVPN_SUCCESS) {
        printf("Failed to generate server keypair\n");
        goto cleanup;
    }
    
    // Wait for client handshake
    bytes_received = recv(session->socket_fd, &packet, sizeof(packet), 0);
    if (bytes_received < 0) {
        perror("recv handshake");
        goto cleanup;
    }
    
    // Process handshake (X25519 key exchange)
    if (ntohs(packet.type) != PKT_TYPE_HANDSHAKE) {
        printf("Expected handshake packet\n");
        goto cleanup;
    }
    
    uint8_t client_public_key[X25519_KEY_SIZE];
    memcpy(client_public_key, packet.payload, X25519_KEY_SIZE);
    
    // Compute shared secret
    result = x25519_shared_secret(server_keypair.private_key, client_public_key, 
                                 session->shared_secret);
    if (result != SVPN_SUCCESS) {
        printf("Failed to compute shared secret\n");
        goto cleanup;
    }
    
    // Send server public key
    result = packet_create(&packet, PKT_TYPE_HANDSHAKE, 
                          server_keypair.public_key, X25519_KEY_SIZE);
    if (result != SVPN_SUCCESS) {
        goto cleanup;
    }
    
    if (send(session->socket_fd, &packet, VPN_HEADER_SIZE + X25519_KEY_SIZE, 0) < 0) {
        perror("send handshake response");
        goto cleanup;
    }
    
    // Wait for license authentication
    bytes_received = recv(session->socket_fd, &packet, sizeof(packet), 0);
    if (bytes_received < 0) {
        perror("recv auth");
        goto cleanup;
    }
    
    if (ntohs(packet.type) != PKT_TYPE_AUTH) {
        printf("Expected auth packet\n");
        goto cleanup;
    }
    
    // Decrypt license packet
    result = packet_decrypt(&packet, session->shared_secret);
    if (result != SVPN_SUCCESS) {
        printf("Failed to decrypt license packet\n");
        goto cleanup;
    }
    
    // Extract license key from payload
    char license_key[512];
    uint16_t payload_len = ntohs(packet.payload_len);
    if (payload_len >= sizeof(license_key)) {
        printf("License key too long\n");
        goto cleanup;
    }
    
    memcpy(license_key, packet.payload, payload_len);
    license_key[payload_len] = '\0';
    
    // Validate license with database
    result = license_manager_validate_license(license_key, &session->user_info, 
                                            &session->license_info);
    
    uint32_t auth_result = (result == SVPN_SUCCESS) ? 0 : 1;
    
    // Check session limits if license is valid
    if (result == SVPN_SUCCESS) {
        bool can_connect = false;
        svpn_error_t limit_result = license_manager_check_session_limit(session->user_info.user_id, 
                                                                       &can_connect);
        if (limit_result != SVPN_SUCCESS || !can_connect) {
            auth_result = 2; // Too many connections
        }
    }
    
    // Send authentication response
    result = packet_create(&packet, PKT_TYPE_AUTH, 
                          (uint8_t*)&auth_result, sizeof(auth_result));
    if (result != SVPN_SUCCESS) {
        goto cleanup;
    }
    
    result = packet_encrypt(&packet, session->shared_secret);
    if (result != SVPN_SUCCESS) {
        goto cleanup;
    }
    
    if (send(session->socket_fd, &packet, VPN_HEADER_SIZE + sizeof(auth_result), 0) < 0) {
        perror("send auth response");
        goto cleanup;
    }
    
    if (auth_result == 0) {
        session->authenticated = true;
        session->last_activity = time(NULL);
        session->user_id = session->user_info.user_id;
        
        // Create database session record
        session_record_t db_session;
        result = license_manager_create_session(session->user_id, client_ip, &db_session);
        if (result == SVPN_SUCCESS) {
            session->session_id = db_session.session_id;
        }
        
        printf("Client authenticated successfully (user: %s, plan: %s)\n", 
               session->user_info.email, db_plan_to_string(session->user_info.plan));
        
        // Main client handling loop with usage tracking
        while (running && session->authenticated) {
            fd_set read_fds;
            struct timeval timeout;
            
            FD_ZERO(&read_fds);
            FD_SET(session->socket_fd, &read_fds);
            
            timeout.tv_sec = 60;  // 60 second timeout
            timeout.tv_usec = 0;
            
            int select_result = select(session->socket_fd + 1, &read_fds, NULL, NULL, &timeout);
            
            if (select_result < 0) {
                if (errno != EINTR) {
                    perror("select");
                    break;
                }
                continue;
            }
            
            if (select_result == 0) {
                // Timeout - update session activity
                time_t current_time = time(NULL);
                if (current_time - session->last_activity > 120) {  // 2 minutes
                    printf("Client timeout\n");
                    break;
                }
                
                // Update session in database
                if (session->session_id > 0) {
                    license_manager_update_session(session->session_id, 
                                                 session->bytes_sent, session->bytes_received);
                }
                continue;
            }
            
            if (FD_ISSET(session->socket_fd, &read_fds)) {
                bytes_received = recv(session->socket_fd, &packet, sizeof(packet), 0);
                if (bytes_received <= 0) {
                    if (bytes_received < 0) {
                        perror("recv");
                    }
                    break;
                }
                
                session->last_activity = time(NULL);
                session->bytes_received += bytes_received;
                
                // Handle different packet types
                uint16_t packet_type = ntohs(packet.type);
                switch (packet_type) {
                    case PKT_TYPE_KEEPALIVE:
                        // Just update last activity time
                        break;
                    case PKT_TYPE_DATA:
                        // Handle VPN data packet (tunnel implementation would go here)
                        session->bytes_sent += ntohs(packet.payload_len);
                        break;
                    case PKT_TYPE_DISCONNECT:
                        printf("Client requested disconnect\n");
                        goto cleanup;
                    default:
                        printf("Unknown packet type: %d\n", packet_type);
                        break;
                }
            }
        }
    } else {
        printf("Client authentication failed (code: %d)\n", auth_result);
    }
    
cleanup:
    printf("Client disconnected\n");
    cleanup_session(session);
    secure_zero(&server_keypair, sizeof(server_keypair));
    return NULL;
}
