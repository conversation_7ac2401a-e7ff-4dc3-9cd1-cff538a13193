#include "securevpn.h"
#include <windows.h>
#include <commctrl.h>
#include <shellapi.h>
#include <wininet.h>
#include <thread>
#include <mutex>
#include <vector>
#include <string>

// Windows native GUI client for Secure VPN
#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "shell32.lib")
#pragma comment(lib, "wininet.lib")

// Resource IDs
#define IDI_MAIN_ICON           101
#define IDI_CONNECTED_ICON      102
#define IDI_DISCONNECTED_ICON   103
#define IDM_CONNECT             1001
#define IDM_DISCONNECT          1002
#define IDM_SETTINGS            1003
#define IDM_ABOUT               1004
#define IDM_EXIT                1005
#define IDC_SERVER_COMBO        2001
#define IDC_STATUS_TEXT         2002
#define IDC_CONNECT_BUTTON      2003
#define IDC_PROGRESS_BAR        2004
#define IDC_STATS_TEXT          2005

// VPN states
enum class VpnState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    DISCONNECTING,
    ERROR
};

// Server information
struct ServerInfo {
    std::wstring name;
    std::wstring address;
    uint16_t port;
    std::wstring country;
    std::wstring city;
    bool premiumOnly;
};

// Application state
class VpnApplication {
private:
    HWND m_hWnd;
    HWND m_hServerCombo;
    HWND m_hStatusText;
    HWND m_hConnectButton;
    HWND m_hProgressBar;
    HWND m_hStatsText;
    NOTIFYICONDATA m_nid;
    
    VpnState m_state;
    std::vector<ServerInfo> m_servers;
    std::thread m_vpnThread;
    std::mutex m_stateMutex;
    bool m_shouldExit;
    
    // Statistics
    uint64_t m_bytesSent;
    uint64_t m_bytesReceived;
    DWORD m_connectedSince;
    std::wstring m_virtualIp;
    std::wstring m_publicIp;

public:
    VpnApplication() : m_hWnd(nullptr), m_state(VpnState::DISCONNECTED), 
                      m_shouldExit(false), m_bytesSent(0), m_bytesReceived(0),
                      m_connectedSince(0) {
        InitializeServers();
    }

    ~VpnApplication() {
        if (m_vpnThread.joinable()) {
            m_shouldExit = true;
            m_vpnThread.join();
        }
    }

    void InitializeServers() {
        m_servers = {
            {L"US East (New York)", L"us-east.securevpn.com", 8443, L"United States", L"New York", false},
            {L"US West (Los Angeles)", L"us-west.securevpn.com", 8443, L"United States", L"Los Angeles", false},
            {L"UK (London)", L"uk.securevpn.com", 8443, L"United Kingdom", L"London", false},
            {L"Germany (Frankfurt)", L"de.securevpn.com", 8443, L"Germany", L"Frankfurt", true},
            {L"Japan (Tokyo)", L"jp.securevpn.com", 8443, L"Japan", L"Tokyo", true},
            {L"Canada (Toronto)", L"ca.securevpn.com", 8443, L"Canada", L"Toronto", false},
            {L"Netherlands (Amsterdam)", L"nl.securevpn.com", 8443, L"Netherlands", L"Amsterdam", true},
            {L"Singapore", L"sg.securevpn.com", 8443, L"Singapore", L"Singapore", true}
        };
    }

    bool Initialize(HINSTANCE hInstance) {
        // Initialize common controls
        INITCOMMONCONTROLSEX icex;
        icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
        icex.dwICC = ICC_PROGRESS_CLASS | ICC_STANDARD_CLASSES;
        InitCommonControlsEx(&icex);

        // Register window class
        WNDCLASSEX wcex = {};
        wcex.cbSize = sizeof(WNDCLASSEX);
        wcex.style = CS_HREDRAW | CS_VREDRAW;
        wcex.lpfnWndProc = WindowProc;
        wcex.hInstance = hInstance;
        wcex.hIcon = LoadIcon(hInstance, MAKEINTRESOURCE(IDI_MAIN_ICON));
        wcex.hCursor = LoadCursor(nullptr, IDC_ARROW);
        wcex.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wcex.lpszClassName = L"SecureVPNWindow";
        wcex.hIconSm = LoadIcon(hInstance, MAKEINTRESOURCE(IDI_MAIN_ICON));

        if (!RegisterClassEx(&wcex)) {
            return false;
        }

        // Create main window
        m_hWnd = CreateWindowEx(
            0,
            L"SecureVPNWindow",
            L"Secure VPN",
            WS_OVERLAPPED | WS_CAPTION | WS_SYSMENU | WS_MINIMIZEBOX,
            CW_USEDEFAULT, CW_USEDEFAULT,
            400, 350,
            nullptr, nullptr, hInstance, this
        );

        if (!m_hWnd) {
            return false;
        }

        CreateControls();
        SetupSystemTray(hInstance);
        UpdateUI();

        ShowWindow(m_hWnd, SW_SHOW);
        UpdateWindow(m_hWnd);

        return true;
    }

    void CreateControls() {
        // Server selection label
        CreateWindow(L"STATIC", L"Server:",
            WS_VISIBLE | WS_CHILD,
            20, 20, 60, 20,
            m_hWnd, nullptr, GetModuleHandle(nullptr), nullptr);

        // Server combo box
        m_hServerCombo = CreateWindow(L"COMBOBOX", nullptr,
            WS_VISIBLE | WS_CHILD | CBS_DROPDOWNLIST | WS_VSCROLL,
            90, 18, 280, 200,
            m_hWnd, (HMENU)IDC_SERVER_COMBO, GetModuleHandle(nullptr), nullptr);

        // Populate server list
        for (const auto& server : m_servers) {
            std::wstring displayName = server.name + L" (" + server.country + L")";
            SendMessage(m_hServerCombo, CB_ADDSTRING, 0, (LPARAM)displayName.c_str());
        }
        SendMessage(m_hServerCombo, CB_SETCURSEL, 0, 0);

        // Status text
        m_hStatusText = CreateWindow(L"STATIC", L"Disconnected",
            WS_VISIBLE | WS_CHILD | SS_CENTER,
            20, 60, 350, 30,
            m_hWnd, (HMENU)IDC_STATUS_TEXT, GetModuleHandle(nullptr), nullptr);

        // Progress bar
        m_hProgressBar = CreateWindow(PROGRESS_CLASS, nullptr,
            WS_CHILD | PBS_SMOOTH,
            20, 100, 350, 20,
            m_hWnd, (HMENU)IDC_PROGRESS_BAR, GetModuleHandle(nullptr), nullptr);

        // Connect button
        m_hConnectButton = CreateWindow(L"BUTTON", L"Connect",
            WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            150, 130, 100, 35,
            m_hWnd, (HMENU)IDC_CONNECT_BUTTON, GetModuleHandle(nullptr), nullptr);

        // Statistics text
        m_hStatsText = CreateWindow(L"STATIC", L"",
            WS_CHILD | SS_LEFT,
            20, 180, 350, 120,
            m_hWnd, (HMENU)IDC_STATS_TEXT, GetModuleHandle(nullptr), nullptr);
    }

    void SetupSystemTray(HINSTANCE hInstance) {
        m_nid.cbSize = sizeof(NOTIFYICONDATA);
        m_nid.hWnd = m_hWnd;
        m_nid.uID = 1;
        m_nid.uFlags = NIF_ICON | NIF_MESSAGE | NIF_TIP;
        m_nid.uCallbackMessage = WM_USER + 1;
        m_nid.hIcon = LoadIcon(hInstance, MAKEINTRESOURCE(IDI_DISCONNECTED_ICON));
        wcscpy_s(m_nid.szTip, L"Secure VPN - Disconnected");
        
        Shell_NotifyIcon(NIM_ADD, &m_nid);
    }

    void UpdateUI() {
        std::lock_guard<std::mutex> lock(m_stateMutex);
        
        const wchar_t* statusText;
        const wchar_t* buttonText;
        bool buttonEnabled = true;
        bool progressVisible = false;

        switch (m_state) {
            case VpnState::DISCONNECTED:
                statusText = L"Disconnected";
                buttonText = L"Connect";
                m_nid.hIcon = LoadIcon(GetModuleHandle(nullptr), MAKEINTRESOURCE(IDI_DISCONNECTED_ICON));
                wcscpy_s(m_nid.szTip, L"Secure VPN - Disconnected");
                break;
            case VpnState::CONNECTING:
                statusText = L"Connecting...";
                buttonText = L"Cancel";
                progressVisible = true;
                break;
            case VpnState::CONNECTED:
                statusText = L"Connected";
                buttonText = L"Disconnect";
                m_nid.hIcon = LoadIcon(GetModuleHandle(nullptr), MAKEINTRESOURCE(IDI_CONNECTED_ICON));
                wcscpy_s(m_nid.szTip, L"Secure VPN - Connected");
                break;
            case VpnState::DISCONNECTING:
                statusText = L"Disconnecting...";
                buttonText = L"Disconnect";
                buttonEnabled = false;
                progressVisible = true;
                break;
            case VpnState::ERROR:
                statusText = L"Connection Error";
                buttonText = L"Retry";
                break;
        }

        SetWindowText(m_hStatusText, statusText);
        SetWindowText(m_hConnectButton, buttonText);
        EnableWindow(m_hConnectButton, buttonEnabled);
        ShowWindow(m_hProgressBar, progressVisible ? SW_SHOW : SW_HIDE);

        Shell_NotifyIcon(NIM_MODIFY, &m_nid);

        UpdateStatsDisplay();
    }

    void UpdateStatsDisplay() {
        if (m_state == VpnState::CONNECTED) {
            DWORD currentTime = GetTickCount();
            DWORD duration = (currentTime - m_connectedSince) / 1000;
            DWORD hours = duration / 3600;
            DWORD minutes = (duration % 3600) / 60;
            DWORD seconds = duration % 60;

            double mbSent = m_bytesSent / (1024.0 * 1024.0);
            double mbReceived = m_bytesReceived / (1024.0 * 1024.0);

            wchar_t statsText[512];
            swprintf_s(statsText, 512,
                L"Connected for: %02d:%02d:%02d\r\n"
                L"Data sent: %.2f MB\r\n"
                L"Data received: %.2f MB\r\n"
                L"Virtual IP: %s\r\n"
                L"Public IP: %s",
                hours, minutes, seconds,
                mbSent, mbReceived,
                m_virtualIp.c_str(),
                m_publicIp.c_str()
            );

            SetWindowText(m_hStatsText, statsText);
            ShowWindow(m_hStatsText, SW_SHOW);
        } else {
            ShowWindow(m_hStatsText, SW_HIDE);
        }
    }

    void OnConnect() {
        if (m_state == VpnState::DISCONNECTED || m_state == VpnState::ERROR) {
            StartConnection();
        } else if (m_state == VpnState::CONNECTED) {
            StopConnection();
        } else if (m_state == VpnState::CONNECTING) {
            CancelConnection();
        }
    }

    void StartConnection() {
        {
            std::lock_guard<std::mutex> lock(m_stateMutex);
            m_state = VpnState::CONNECTING;
        }
        UpdateUI();

        m_shouldExit = false;
        m_vpnThread = std::thread(&VpnApplication::VpnConnectionThread, this);
    }

    void StopConnection() {
        {
            std::lock_guard<std::mutex> lock(m_stateMutex);
            m_state = VpnState::DISCONNECTING;
        }
        UpdateUI();

        m_shouldExit = true;
        if (m_vpnThread.joinable()) {
            m_vpnThread.join();
        }

        {
            std::lock_guard<std::mutex> lock(m_stateMutex);
            m_state = VpnState::DISCONNECTED;
        }
        UpdateUI();
    }

    void CancelConnection() {
        m_shouldExit = true;
    }

    void VpnConnectionThread() {
        // Simulate connection process
        for (int i = 0; i < 10 && !m_shouldExit; i++) {
            Sleep(1000);
            SendMessage(m_hProgressBar, PBM_SETPOS, (i + 1) * 10, 0);
        }

        if (!m_shouldExit) {
            {
                std::lock_guard<std::mutex> lock(m_stateMutex);
                m_state = VpnState::CONNECTED;
                m_connectedSince = GetTickCount();
                m_virtualIp = L"********";
                m_publicIp = L"***********";
                m_bytesSent = 0;
                m_bytesReceived = 0;
            }

            PostMessage(m_hWnd, WM_USER + 2, 0, 0); // Update UI

            // Simulate traffic
            while (m_state == VpnState::CONNECTED && !m_shouldExit) {
                Sleep(1000);
                m_bytesSent += 1024 + (rand() % 10240);
                m_bytesReceived += 2048 + (rand() % 20480);
                PostMessage(m_hWnd, WM_USER + 2, 0, 0); // Update UI
            }
        }

        {
            std::lock_guard<std::mutex> lock(m_stateMutex);
            m_state = VpnState::DISCONNECTED;
        }
        PostMessage(m_hWnd, WM_USER + 2, 0, 0); // Update UI
    }

    static LRESULT CALLBACK WindowProc(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam) {
        VpnApplication* app = nullptr;

        if (message == WM_NCCREATE) {
            CREATESTRUCT* cs = (CREATESTRUCT*)lParam;
            app = (VpnApplication*)cs->lpCreateParams;
            SetWindowLongPtr(hWnd, GWLP_USERDATA, (LONG_PTR)app);
        } else {
            app = (VpnApplication*)GetWindowLongPtr(hWnd, GWLP_USERDATA);
        }

        if (app) {
            return app->HandleMessage(hWnd, message, wParam, lParam);
        }

        return DefWindowProc(hWnd, message, wParam, lParam);
    }

    LRESULT HandleMessage(HWND hWnd, UINT message, WPARAM wParam, LPARAM lParam) {
        switch (message) {
            case WM_COMMAND:
                if (LOWORD(wParam) == IDC_CONNECT_BUTTON) {
                    OnConnect();
                }
                break;

            case WM_USER + 1: // System tray message
                if (lParam == WM_LBUTTONDBLCLK) {
                    ShowWindow(hWnd, IsWindowVisible(hWnd) ? SW_HIDE : SW_SHOW);
                }
                break;

            case WM_USER + 2: // Update UI message
                UpdateUI();
                break;

            case WM_CLOSE:
                ShowWindow(hWnd, SW_HIDE);
                return 0;

            case WM_DESTROY:
                Shell_NotifyIcon(NIM_DELETE, &m_nid);
                PostQuitMessage(0);
                break;

            default:
                return DefWindowProc(hWnd, message, wParam, lParam);
        }
        return 0;
    }

    void Run() {
        MSG msg;
        while (GetMessage(&msg, nullptr, 0, 0)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
    }
};

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    VpnApplication app;
    
    if (!app.Initialize(hInstance)) {
        MessageBox(nullptr, L"Failed to initialize application", L"Error", MB_OK | MB_ICONERROR);
        return 1;
    }

    app.Run();
    return 0;
}
