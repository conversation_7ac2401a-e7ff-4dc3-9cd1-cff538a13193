#!/usr/bin/env python3
"""
Traffic Simulator - Generate Real VPN Activity
Creates multiple concurrent connections to show real-time dashboard updates
"""

import threading
import time
import subprocess
import sys
import random

def run_client_session(user_email, password, duration=30):
    """Run a VPN client session"""
    try:
        print(f"🔗 Starting session for {user_email}")
        
        # Run VPN client
        result = subprocess.run([
            sys.executable, 'vpn_client.py',
            '--email', user_email,
            '--password', password,
            '--test',
            '--duration', str(duration)
        ], capture_output=True, text=True, timeout=duration + 10)
        
        if result.returncode == 0:
            print(f"✅ Session completed for {user_email}")
        else:
            print(f"❌ Session failed for {user_email}: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print(f"⏰ Session timed out for {user_email}")
    except Exception as e:
        print(f"❌ Error in session for {user_email}: {e}")

def create_test_users():
    """Create test users for simulation"""
    test_users = [
        ('<EMAIL>', 'admin123', 'enterprise'),
        ('<EMAIL>', 'password123', 'premium'),
        ('<EMAIL>', 'securepass456', 'basic'),
        ('<EMAIL>', 'mypassword789', 'premium'),
        ('<EMAIL>', 'strongpass321', 'enterprise'),
        ('<EMAIL>', 'password456', 'basic'),
        ('<EMAIL>', 'securekey123', 'premium')
    ]
    
    print("👥 Creating test users...")
    
    for email, password, plan in test_users[3:]:  # Skip first 3 as they already exist
        try:
            result = subprocess.run([
                sys.executable, 'vpn_manager_tool.py',
                'create-user', email, password, plan
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ Created user: {email} ({plan})")
            else:
                print(f"⚠️  User might already exist: {email}")
                
        except Exception as e:
            print(f"❌ Error creating user {email}: {e}")
    
    return test_users

def simulate_concurrent_traffic():
    """Simulate multiple concurrent VPN sessions"""
    print("🚀 Starting VPN Traffic Simulation")
    print("=" * 50)
    
    # Create test users
    users = create_test_users()
    
    print(f"\n📊 Dashboard: http://localhost:8090/docs/dashboard.html")
    print("🔄 Watch the dashboard for real-time updates!")
    print("\nStarting concurrent sessions...\n")
    
    threads = []
    
    # Start multiple concurrent sessions
    for i, (email, password, plan) in enumerate(users[:5]):  # Use first 5 users
        duration = random.randint(20, 60)  # Random duration 20-60 seconds
        
        thread = threading.Thread(
            target=run_client_session,
            args=(email, password, duration),
            name=f"Client-{i+1}"
        )
        thread.daemon = True
        thread.start()
        threads.append(thread)
        
        # Stagger the starts
        time.sleep(random.randint(2, 8))
    
    print(f"🔥 Started {len(threads)} concurrent VPN sessions!")
    print("📈 Check the dashboard to see real-time updates:")
    print("   - Active Connections should increase")
    print("   - Bandwidth usage should grow")
    print("   - User activity should be visible")
    print()
    
    # Monitor progress
    start_time = time.time()
    while any(t.is_alive() for t in threads):
        alive_count = sum(1 for t in threads if t.is_alive())
        elapsed = int(time.time() - start_time)
        
        print(f"⏱️  {elapsed}s elapsed | {alive_count} sessions active", end='\r')
        time.sleep(2)
    
    print(f"\n✅ All sessions completed after {int(time.time() - start_time)} seconds")
    print("📊 Check the dashboard for final statistics!")

def continuous_simulation():
    """Run continuous traffic simulation"""
    print("🔄 Starting Continuous Traffic Simulation")
    print("Press Ctrl+C to stop")
    print("=" * 50)
    
    users = create_test_users()
    
    try:
        while True:
            # Pick random user
            email, password, plan = random.choice(users)
            duration = random.randint(10, 30)
            
            print(f"🔗 Starting session: {email} ({duration}s)")
            
            # Start session in background
            thread = threading.Thread(
                target=run_client_session,
                args=(email, password, duration)
            )
            thread.daemon = True
            thread.start()
            
            # Wait before starting next session
            wait_time = random.randint(5, 15)
            time.sleep(wait_time)
            
    except KeyboardInterrupt:
        print("\n🛑 Stopping continuous simulation...")

def main():
    """Main function"""
    print("🎯 VPN Traffic Simulator")
    print("=" * 30)
    print("1. Concurrent Sessions (5 users at once)")
    print("2. Continuous Traffic (ongoing simulation)")
    print("3. Single Test Session")
    
    choice = input("\nSelect option (1-3): ").strip()
    
    if choice == '1':
        simulate_concurrent_traffic()
    elif choice == '2':
        continuous_simulation()
    elif choice == '3':
        print("🔗 Running single test session...")
        run_client_session('<EMAIL>', 'admin123', 20)
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()
