@echo off
REM Complete OpenVPN Setup for Windows
REM This will install and configure everything automatically

echo 🚀 Complete OpenVPN Setup for Windows
echo ========================================

REM Check if running as admin
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 🔐 Requesting administrator privileges...
    powershell -Command "Start-Process cmd -ArgumentList '/c %~f0' -Verb RunAs"
    exit /b
)

echo ✅ Running as administrator

REM Run the Python setup script
echo 🔧 Running OpenVPN setup...
python windows_openvpn_setup.py

echo.
echo 🎯 Setup complete! 
echo.
echo 📋 Next steps:
echo 1. Run: python windows_openvpn_setup.py --start
echo 2. Import vpn_profiles\test_openvpn.ovpn into OpenVPN client
echo 3. Connect with: <EMAIL> / password
echo.
pause
