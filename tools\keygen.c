#include "securevpn.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <time.h>

static void print_usage(const char *program_name) {
    printf("Secure VPN License Key Generator v%d.%d.%d\n", 
           SVPN_VERSION_MAJOR, SVPN_VERSION_MINOR, SVPN_VERSION_PATCH);
    printf("Usage: %s [options]\n", program_name);
    printf("Options:\n");
    printf("  -g                Generate new RSA keypair\n");
    printf("  -u <user_id>      User ID for license\n");
    printf("  -d <days>         License duration in days (default: 30)\n");
    printf("  -k <private_key>  Private key file for signing\n");
    printf("  -o <output_file>  Output license file (default: license.lic)\n");
    printf("  -p <pub_key_out>  Output public key file (when generating keypair)\n");
    printf("  -h                Show this help\n");
}

static svpn_error_t generate_rsa_keypair(const char *private_key_file, 
                                         const char *public_key_file) {
    uint8_t private_key[RSA_KEY_SIZE * 2];  // Private key + modulus
    uint8_t public_key[RSA_KEY_SIZE];       // Just modulus for public key
    FILE *file;
    
    printf("Generating RSA-4096 keypair...\n");
    
    // Generate random key material (simplified - in production use proper RSA generation)
    secure_random(private_key, sizeof(private_key));
    memcpy(public_key, private_key + RSA_KEY_SIZE, RSA_KEY_SIZE);
    
    // Save private key
    file = fopen(private_key_file, "wb");
    if (!file) {
        perror("fopen private key");
        return SVPN_ERROR_SYSTEM;
    }
    
    if (fwrite(private_key, 1, sizeof(private_key), file) != sizeof(private_key)) {
        fclose(file);
        return SVPN_ERROR_SYSTEM;
    }
    fclose(file);
    
    // Save public key
    file = fopen(public_key_file, "wb");
    if (!file) {
        perror("fopen public key");
        return SVPN_ERROR_SYSTEM;
    }
    
    if (fwrite(public_key, 1, sizeof(public_key), file) != sizeof(public_key)) {
        fclose(file);
        return SVPN_ERROR_SYSTEM;
    }
    fclose(file);
    
    printf("RSA keypair generated:\n");
    printf("  Private key: %s\n", private_key_file);
    printf("  Public key:  %s\n", public_key_file);
    
    // Clear sensitive data
    secure_zero(private_key, sizeof(private_key));
    
    return SVPN_SUCCESS;
}

static svpn_error_t load_private_key(const char *filename, uint8_t *private_key) {
    FILE *file = fopen(filename, "rb");
    if (!file) {
        perror("fopen private key");
        return SVPN_ERROR_SYSTEM;
    }
    
    size_t read_bytes = fread(private_key, 1, RSA_KEY_SIZE * 2, file);
    fclose(file);
    
    if (read_bytes != RSA_KEY_SIZE * 2) {
        return SVPN_ERROR_SYSTEM;
    }
    
    return SVPN_SUCCESS;
}

static svpn_error_t create_license(const char *user_id, int duration_days,
                                  const char *private_key_file, 
                                  const char *output_file) {
    license_t license;
    uint8_t private_key[RSA_KEY_SIZE * 2];
    svpn_error_t result;
    time_t duration_seconds = duration_days * 24 * 60 * 60;
    
    printf("Creating license for user: %s\n", user_id);
    printf("Duration: %d days\n", duration_days);
    
    // Load private key
    result = load_private_key(private_key_file, private_key);
    if (result != SVPN_SUCCESS) {
        fprintf(stderr, "Error: Failed to load private key\n");
        return result;
    }
    
    // Generate license
    result = license_generate(&license, private_key, user_id, duration_seconds);
    if (result != SVPN_SUCCESS) {
        fprintf(stderr, "Error: Failed to generate license\n");
        secure_zero(private_key, sizeof(private_key));
        return result;
    }
    
    // Save license to file
    result = license_save_to_file(&license, output_file);
    if (result != SVPN_SUCCESS) {
        fprintf(stderr, "Error: Failed to save license file\n");
        secure_zero(private_key, sizeof(private_key));
        return result;
    }
    
    printf("License created successfully: %s\n", output_file);
    printf("License details:\n");
    printf("  User ID: %s\n", license.user_id);
    printf("  Issued:  %s", ctime(&license.issued_at));
    printf("  Expires: %s", ctime(&license.expires_at));
    printf("  Max connections: %d\n", license.max_connections);
    
    // Clear sensitive data
    secure_zero(private_key, sizeof(private_key));
    
    return SVPN_SUCCESS;
}

int main(int argc, char *argv[]) {
    int opt;
    bool generate_keypair = false;
    char user_id[64] = "";
    int duration_days = 30;
    char private_key_file[256] = "server.key";
    char public_key_file[256] = "server.pub";
    char output_file[256] = "license.lic";
    svpn_error_t result;
    
    printf("Secure VPN License Key Generator v%d.%d.%d\n", 
           SVPN_VERSION_MAJOR, SVPN_VERSION_MINOR, SVPN_VERSION_PATCH);
    
    if (argc < 2) {
        print_usage(argv[0]);
        return 1;
    }
    
    while ((opt = getopt(argc, argv, "gu:d:k:o:p:h")) != -1) {
        switch (opt) {
            case 'g':
                generate_keypair = true;
                break;
            case 'u':
                strncpy(user_id, optarg, sizeof(user_id) - 1);
                break;
            case 'd':
                duration_days = atoi(optarg);
                break;
            case 'k':
                strncpy(private_key_file, optarg, sizeof(private_key_file) - 1);
                break;
            case 'o':
                strncpy(output_file, optarg, sizeof(output_file) - 1);
                break;
            case 'p':
                strncpy(public_key_file, optarg, sizeof(public_key_file) - 1);
                break;
            case 'h':
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    if (generate_keypair) {
        result = generate_rsa_keypair(private_key_file, public_key_file);
        if (result != SVPN_SUCCESS) {
            fprintf(stderr, "Error: Failed to generate RSA keypair\n");
            return 1;
        }
        return 0;
    }
    
    if (strlen(user_id) == 0) {
        fprintf(stderr, "Error: User ID is required for license generation\n");
        print_usage(argv[0]);
        return 1;
    }
    
    if (duration_days <= 0) {
        fprintf(stderr, "Error: Duration must be positive\n");
        return 1;
    }
    
    result = create_license(user_id, duration_days, private_key_file, output_file);
    if (result != SVPN_SUCCESS) {
        return 1;
    }
    
    return 0;
}
