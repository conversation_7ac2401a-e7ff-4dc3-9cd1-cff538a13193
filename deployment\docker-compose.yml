# FREE Production Docker Compose for Secure VPN Service
# Optimized for free tier cloud servers (1GB RAM)
# Deploy with: docker-compose -f deployment/docker-compose.yml up -d

version: '3.8'

services:
  # Main VPN Server
  vpn-server:
    build:
      context: ..
      dockerfile: deployment/docker/Dockerfile
      target: production
    container_name: securevpn-server
    restart: unless-stopped
    ports:
      - "8443:8443"  # VPN port
      - "8080:8080"  # Web API port
      - "9090:9090"  # Metrics port
    volumes:
      - vpn-data:/opt/securevpn/data
      - vpn-logs:/opt/securevpn/logs
      - vpn-config:/opt/securevpn/config
      - ./ssl:/opt/securevpn/ssl:ro
    environment:
      - VPN_PORT=8443
      - API_PORT=8080
      - METRICS_PORT=9090
      - DATABASE_PATH=/opt/securevpn/data/vpn.db
      - PRIVATE_KEY_FILE=/opt/securevpn/ssl/server.key
      - PUBLIC_KEY_FILE=/opt/securevpn/ssl/server.pub
      - LOG_LEVEL=INFO
      - MAX_CLIENTS=1000
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
    depends_on:
      - redis
      - postgres
    networks:
      - vpn-network
    cap_add:
      - NET_ADMIN
    devices:
      - /dev/net/tun
    sysctls:
      - net.ipv4.ip_forward=1
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: securevpn-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=securevpn
      - POSTGRES_USER=vpnuser
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - vpn-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U vpnuser -d securevpn"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: securevpn-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis-data:/data
    networks:
      - vpn-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: securevpn-prometheus
    restart: unless-stopped
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - vpn-network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: securevpn-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - vpn-network
    depends_on:
      - prometheus

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: securevpn-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/html:/usr/share/nginx/html:ro
    networks:
      - vpn-network
    depends_on:
      - vpn-server

  # Log Aggregation
  loki:
    image: grafana/loki:latest
    container_name: securevpn-loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki.yml:/etc/loki/local-config.yaml:ro
      - loki-data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - vpn-network

  # Log Shipper
  promtail:
    image: grafana/promtail:latest
    container_name: securevpn-promtail
    restart: unless-stopped
    volumes:
      - ./monitoring/promtail.yml:/etc/promtail/config.yml:ro
      - vpn-logs:/var/log/securevpn:ro
      - /var/log:/var/log:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - vpn-network
    depends_on:
      - loki

  # Backup Service
  backup:
    image: alpine:latest
    container_name: securevpn-backup
    restart: "no"
    volumes:
      - vpn-data:/backup/data:ro
      - postgres-data:/backup/postgres:ro
      - ./backup:/backup/scripts:ro
      - backup-storage:/backup/output
    command: /backup/scripts/backup.sh
    networks:
      - vpn-network
    depends_on:
      - postgres
      - vpn-server

  # Certificate Management
  certbot:
    image: certbot/certbot:latest
    container_name: securevpn-certbot
    restart: "no"
    volumes:
      - ./ssl:/etc/letsencrypt
      - ./nginx/html:/var/www/html
    command: certonly --webroot --webroot-path=/var/www/html --email ${ADMIN_EMAIL} --agree-tos --no-eff-email -d ${DOMAIN_NAME}

volumes:
  vpn-data:
    driver: local
  vpn-logs:
    driver: local
  vpn-config:
    driver: local
  postgres-data:
    driver: local
  redis-data:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  loki-data:
    driver: local
  backup-storage:
    driver: local

networks:
  vpn-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
