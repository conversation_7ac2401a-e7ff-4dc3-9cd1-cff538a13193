#include "prometheus_metrics.h"
#include "securevpn.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <pthread.h>
#include <unistd.h>
#include <sys/sysinfo.h>
#include <sys/statvfs.h>

metrics_error_t metrics_init(metrics_registry_t *registry, const char *instance_id, 
                           const char *version) {
    if (!registry) {
        return METRICS_ERROR_INVALID_PARAM;
    }

    memset(registry, 0, sizeof(metrics_registry_t));

    // Initialize metrics array
    registry->metric_capacity = 100;
    registry->metrics = calloc(registry->metric_capacity, sizeof(metric_t));
    if (!registry->metrics) {
        return METRICS_ERROR_MEMORY;
    }

    // Initialize histograms array
    registry->histogram_capacity = 20;
    registry->histograms = calloc(registry->histogram_capacity, sizeof(histogram_metric_t));
    if (!registry->histograms) {
        free(registry->metrics);
        return METRICS_ERROR_MEMORY;
    }

    // Set instance information
    if (instance_id) {
        strncpy(registry->instance_id, instance_id, sizeof(registry->instance_id) - 1);
    } else {
        snprintf(registry->instance_id, sizeof(registry->instance_id), "vpn-server-%d", getpid());
    }

    if (version) {
        strncpy(registry->version, version, sizeof(registry->version) - 1);
    } else {
        snprintf(registry->version, sizeof(registry->version), "%d.%d.%d", 
                SVPN_VERSION_MAJOR, SVPN_VERSION_MINOR, SVPN_VERSION_PATCH);
    }

    registry->is_initialized = true;
    return METRICS_SUCCESS;
}

void metrics_cleanup(metrics_registry_t *registry) {
    if (!registry || !registry->is_initialized) {
        return;
    }

    if (registry->metrics) {
        free(registry->metrics);
        registry->metrics = NULL;
    }

    if (registry->histograms) {
        free(registry->histograms);
        registry->histograms = NULL;
    }

    registry->is_initialized = false;
}

static metric_t* find_metric(metrics_registry_t *registry, const char *name, const char *labels) {
    for (uint32_t i = 0; i < registry->metric_count; i++) {
        if (strcmp(registry->metrics[i].name, name) == 0 &&
            strcmp(registry->metrics[i].labels, labels ? labels : "") == 0) {
            return &registry->metrics[i];
        }
    }
    return NULL;
}

metrics_error_t metrics_register_counter(metrics_registry_t *registry, const char *name,
                                        const char *help, const char *labels) {
    if (!registry || !registry->is_initialized || !name || !help) {
        return METRICS_ERROR_INVALID_PARAM;
    }

    if (registry->metric_count >= registry->metric_capacity) {
        return METRICS_ERROR_MEMORY;
    }

    metric_t *metric = &registry->metrics[registry->metric_count];
    strncpy(metric->name, name, sizeof(metric->name) - 1);
    strncpy(metric->help, help, sizeof(metric->help) - 1);
    strncpy(metric->labels, labels ? labels : "", sizeof(metric->labels) - 1);
    metric->type = METRIC_TYPE_COUNTER;
    metric->value = 0.0;
    metric->last_updated = time(NULL);

    registry->metric_count++;
    return METRICS_SUCCESS;
}

metrics_error_t metrics_register_gauge(metrics_registry_t *registry, const char *name,
                                      const char *help, const char *labels) {
    if (!registry || !registry->is_initialized || !name || !help) {
        return METRICS_ERROR_INVALID_PARAM;
    }

    if (registry->metric_count >= registry->metric_capacity) {
        return METRICS_ERROR_MEMORY;
    }

    metric_t *metric = &registry->metrics[registry->metric_count];
    strncpy(metric->name, name, sizeof(metric->name) - 1);
    strncpy(metric->help, help, sizeof(metric->help) - 1);
    strncpy(metric->labels, labels ? labels : "", sizeof(metric->labels) - 1);
    metric->type = METRIC_TYPE_GAUGE;
    metric->value = 0.0;
    metric->last_updated = time(NULL);

    registry->metric_count++;
    return METRICS_SUCCESS;
}

metrics_error_t metrics_counter_inc(metrics_registry_t *registry, const char *name,
                                   const char *labels) {
    return metrics_counter_add(registry, name, labels, 1.0);
}

metrics_error_t metrics_counter_add(metrics_registry_t *registry, const char *name,
                                   const char *labels, double value) {
    if (!registry || !registry->is_initialized || !name || value < 0) {
        return METRICS_ERROR_INVALID_PARAM;
    }

    metric_t *metric = find_metric(registry, name, labels);
    if (!metric) {
        return METRICS_ERROR_INVALID_PARAM;
    }

    if (metric->type != METRIC_TYPE_COUNTER) {
        return METRICS_ERROR_INVALID_PARAM;
    }

    metric->value += value;
    metric->last_updated = time(NULL);
    return METRICS_SUCCESS;
}

metrics_error_t metrics_gauge_set(metrics_registry_t *registry, const char *name,
                                 const char *labels, double value) {
    if (!registry || !registry->is_initialized || !name) {
        return METRICS_ERROR_INVALID_PARAM;
    }

    metric_t *metric = find_metric(registry, name, labels);
    if (!metric) {
        return METRICS_ERROR_INVALID_PARAM;
    }

    if (metric->type != METRIC_TYPE_GAUGE) {
        return METRICS_ERROR_INVALID_PARAM;
    }

    metric->value = value;
    metric->last_updated = time(NULL);
    return METRICS_SUCCESS;
}

metrics_error_t metrics_init_vpn_metrics(metrics_registry_t *registry) {
    if (!registry || !registry->is_initialized) {
        return METRICS_ERROR_INVALID_PARAM;
    }

    // Connection metrics
    metrics_register_counter(registry, "vpn_connections_total", 
                            "Total number of VPN connections", "");
    metrics_register_gauge(registry, "vpn_connections_active", 
                          "Number of active VPN connections", "");
    metrics_register_counter(registry, "vpn_connections_failed_total", 
                           "Total number of failed VPN connections", "");

    // Traffic metrics
    metrics_register_counter(registry, "vpn_bytes_sent_total", 
                           "Total bytes sent through VPN", "");
    metrics_register_counter(registry, "vpn_bytes_received_total", 
                           "Total bytes received through VPN", "");
    metrics_register_counter(registry, "vpn_packets_sent_total", 
                           "Total packets sent through VPN", "");
    metrics_register_counter(registry, "vpn_packets_received_total", 
                           "Total packets received through VPN", "");

    // User metrics
    metrics_register_gauge(registry, "vpn_users_total", 
                         "Total number of registered users", "");
    metrics_register_gauge(registry, "vpn_users_active_24h", 
                         "Number of users active in last 24 hours", "");
    metrics_register_counter(registry, "vpn_users_new_today", 
                           "Number of new users registered today", "");

    // Performance metrics
    metrics_register_gauge(registry, "vpn_connection_time_avg_ms", 
                         "Average connection establishment time in milliseconds", "");
    metrics_register_gauge(registry, "vpn_throughput_avg_mbps", 
                         "Average throughput in Mbps", "");
    metrics_register_gauge(registry, "system_cpu_usage_percent", 
                         "CPU usage percentage", "");
    metrics_register_gauge(registry, "system_memory_usage_percent", 
                         "Memory usage percentage", "");
    metrics_register_gauge(registry, "system_disk_usage_percent", 
                         "Disk usage percentage", "");

    // Security metrics
    metrics_register_counter(registry, "vpn_connections_blocked_total", 
                           "Total number of blocked connections", "");
    metrics_register_counter(registry, "vpn_auth_failures_total", 
                           "Total number of authentication failures", "");
    metrics_register_gauge(registry, "vpn_banned_ips", 
                         "Number of currently banned IP addresses", "");
    metrics_register_counter(registry, "vpn_security_events_total", 
                           "Total number of security events", "");

    // Business metrics
    metrics_register_gauge(registry, "vpn_revenue_monthly", 
                         "Monthly revenue in USD", "");
    metrics_register_gauge(registry, "vpn_arpu", 
                         "Average revenue per user", "");
    metrics_register_counter(registry, "vpn_payment_failures_total", 
                           "Total number of payment failures", "");
    metrics_register_counter(registry, "vpn_refunds_total", 
                           "Total number of refunds", "");

    return METRICS_SUCCESS;
}

metrics_error_t metrics_record_connection(metrics_registry_t *registry, 
                                         subscription_plan_t plan, bool success) {
    if (!registry || !registry->is_initialized) {
        return METRICS_ERROR_INVALID_PARAM;
    }

    if (success) {
        metrics_counter_inc(registry, "vpn_connections_total", "");
        
        // Record by plan
        char plan_label[64];
        snprintf(plan_label, sizeof(plan_label), "plan=\"%s\"", db_plan_to_string(plan));
        metrics_counter_inc(registry, "vpn_connections_total", plan_label);
    } else {
        metrics_counter_inc(registry, "vpn_connections_failed_total", "");
    }

    return METRICS_SUCCESS;
}

metrics_error_t metrics_record_traffic(metrics_registry_t *registry, uint64_t bytes_sent,
                                      uint64_t bytes_received) {
    if (!registry || !registry->is_initialized) {
        return METRICS_ERROR_INVALID_PARAM;
    }

    metrics_counter_add(registry, "vpn_bytes_sent_total", "", (double)bytes_sent);
    metrics_counter_add(registry, "vpn_bytes_received_total", "", (double)bytes_received);

    return METRICS_SUCCESS;
}

metrics_error_t metrics_export_prometheus(metrics_registry_t *registry, char *output,
                                         size_t output_size) {
    if (!registry || !registry->is_initialized || !output || output_size == 0) {
        return METRICS_ERROR_INVALID_PARAM;
    }

    size_t offset = 0;
    
    // Add header comment
    offset += snprintf(output + offset, output_size - offset,
                      "# Secure VPN Metrics\n"
                      "# Instance: %s\n"
                      "# Version: %s\n"
                      "# Generated: %ld\n\n",
                      registry->instance_id, registry->version, time(NULL));

    // Export regular metrics
    for (uint32_t i = 0; i < registry->metric_count && offset < output_size; i++) {
        metric_t *metric = &registry->metrics[i];
        
        // Add help text
        offset += snprintf(output + offset, output_size - offset,
                          "# HELP %s %s\n", metric->name, metric->help);
        
        // Add type
        offset += snprintf(output + offset, output_size - offset,
                          "# TYPE %s %s\n", metric->name, metric_type_string(metric->type));
        
        // Add metric value
        if (strlen(metric->labels) > 0) {
            offset += snprintf(output + offset, output_size - offset,
                              "%s{%s} %.2f\n", metric->name, metric->labels, metric->value);
        } else {
            offset += snprintf(output + offset, output_size - offset,
                              "%s %.2f\n", metric->name, metric->value);
        }
        
        offset += snprintf(output + offset, output_size - offset, "\n");
    }

    return METRICS_SUCCESS;
}

metrics_error_t metrics_get_system_stats(double *cpu_percent, double *memory_percent,
                                        double *disk_percent) {
    if (!cpu_percent || !memory_percent || !disk_percent) {
        return METRICS_ERROR_INVALID_PARAM;
    }

    // Get memory info
    struct sysinfo si;
    if (sysinfo(&si) == 0) {
        *memory_percent = ((double)(si.totalram - si.freeram) / si.totalram) * 100.0;
    } else {
        *memory_percent = 0.0;
    }

    // Get disk info for root filesystem
    struct statvfs vfs;
    if (statvfs("/", &vfs) == 0) {
        uint64_t total = vfs.f_blocks * vfs.f_frsize;
        uint64_t free = vfs.f_bavail * vfs.f_frsize;
        *disk_percent = ((double)(total - free) / total) * 100.0;
    } else {
        *disk_percent = 0.0;
    }

    // CPU usage is more complex to calculate accurately
    // For now, return a placeholder
    *cpu_percent = 0.0;

    return METRICS_SUCCESS;
}

const char* metrics_error_string(metrics_error_t error) {
    switch (error) {
        case METRICS_SUCCESS:
            return "Success";
        case METRICS_ERROR_INVALID_PARAM:
            return "Invalid parameter";
        case METRICS_ERROR_MEMORY:
            return "Memory allocation error";
        case METRICS_ERROR_IO:
            return "I/O error";
        case METRICS_ERROR_FORMAT:
            return "Format error";
        default:
            return "Unknown error";
    }
}

const char* metric_type_string(metric_type_t type) {
    switch (type) {
        case METRIC_TYPE_COUNTER:
            return "counter";
        case METRIC_TYPE_GAUGE:
            return "gauge";
        case METRIC_TYPE_HISTOGRAM:
            return "histogram";
        case METRIC_TYPE_SUMMARY:
            return "summary";
        default:
            return "unknown";
    }
}
