# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/d/secure-vpn

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/d/secure-vpn/build

# Include any dependencies generated for this target.
include CMakeFiles/svpn_client.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/svpn_client.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/svpn_client.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/svpn_client.dir/flags.make

CMakeFiles/svpn_client.dir/src/client/main.c.o: CMakeFiles/svpn_client.dir/flags.make
CMakeFiles/svpn_client.dir/src/client/main.c.o: ../src/client/main.c
CMakeFiles/svpn_client.dir/src/client/main.c.o: CMakeFiles/svpn_client.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/d/secure-vpn/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/svpn_client.dir/src/client/main.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/svpn_client.dir/src/client/main.c.o -MF CMakeFiles/svpn_client.dir/src/client/main.c.o.d -o CMakeFiles/svpn_client.dir/src/client/main.c.o -c /mnt/d/secure-vpn/src/client/main.c

CMakeFiles/svpn_client.dir/src/client/main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/svpn_client.dir/src/client/main.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /mnt/d/secure-vpn/src/client/main.c > CMakeFiles/svpn_client.dir/src/client/main.c.i

CMakeFiles/svpn_client.dir/src/client/main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/svpn_client.dir/src/client/main.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /mnt/d/secure-vpn/src/client/main.c -o CMakeFiles/svpn_client.dir/src/client/main.c.s

# Object files for target svpn_client
svpn_client_OBJECTS = \
"CMakeFiles/svpn_client.dir/src/client/main.c.o"

# External object files for target svpn_client
svpn_client_EXTERNAL_OBJECTS =

svpn_client: CMakeFiles/svpn_client.dir/src/client/main.c.o
svpn_client: CMakeFiles/svpn_client.dir/build.make
svpn_client: libsecurevpn_core.a
svpn_client: CMakeFiles/svpn_client.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/mnt/d/secure-vpn/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable svpn_client"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/svpn_client.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/svpn_client.dir/build: svpn_client
.PHONY : CMakeFiles/svpn_client.dir/build

CMakeFiles/svpn_client.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/svpn_client.dir/cmake_clean.cmake
.PHONY : CMakeFiles/svpn_client.dir/clean

CMakeFiles/svpn_client.dir/depend:
	cd /mnt/d/secure-vpn/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /mnt/d/secure-vpn /mnt/d/secure-vpn /mnt/d/secure-vpn/build /mnt/d/secure-vpn/build /mnt/d/secure-vpn/build/CMakeFiles/svpn_client.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/svpn_client.dir/depend

