#include "database.h"
#include <stdio.h>
#include <string.h>

const char* db_error_string(db_error_t error) {
    switch (error) {
        case DB_SUCCESS:
            return "Success";
        case DB_ERROR_INIT:
            return "Database initialization failed";
        case DB_ERROR_QUERY:
            return "Database query failed";
        case DB_ERROR_NOT_FOUND:
            return "Record not found";
        case DB_ERROR_DUPLICATE:
            return "Duplicate record";
        case DB_ERROR_INVALID_PARAM:
            return "Invalid parameter";
        case DB_ERROR_MEMORY:
            return "Memory allocation failed";
        default:
            return "Unknown error";
    }
}

subscription_plan_t db_plan_from_string(const char *plan_str) {
    if (!plan_str) return PLAN_FREE;
    
    if (strcmp(plan_str, "free") == 0) return PLAN_FREE;
    if (strcmp(plan_str, "basic") == 0) return PLAN_BASIC;
    if (strcmp(plan_str, "premium") == 0) return PLAN_PREMIUM;
    if (strcmp(plan_str, "enterprise") == 0) return PLAN_ENTERPRISE;
    
    return PLAN_FREE;
}

const char* db_plan_to_string(subscription_plan_t plan) {
    switch (plan) {
        case PLAN_FREE:
            return "free";
        case PLAN_BASIC:
            return "basic";
        case PLAN_PREMIUM:
            return "premium";
        case PLAN_ENTERPRISE:
            return "enterprise";
        default:
            return "free";
    }
}

uint32_t db_plan_get_max_connections(subscription_plan_t plan) {
    switch (plan) {
        case PLAN_FREE:
            return 1;
        case PLAN_BASIC:
            return 3;
        case PLAN_PREMIUM:
            return 5;
        case PLAN_ENTERPRISE:
            return 10;
        default:
            return 1;
    }
}

uint64_t db_plan_get_daily_limit(subscription_plan_t plan) {
    switch (plan) {
        case PLAN_FREE:
            return 1024ULL * 1024 * 1024;      // 1 GB
        case PLAN_BASIC:
            return 10ULL * 1024 * 1024 * 1024; // 10 GB
        case PLAN_PREMIUM:
            return 100ULL * 1024 * 1024 * 1024; // 100 GB
        case PLAN_ENTERPRISE:
            return 0; // Unlimited
        default:
            return 1024ULL * 1024 * 1024;      // 1 GB
    }
}

// License validation function that works with database
db_error_t db_license_validate(database_ctx_t *ctx, const char *license_key, 
                              license_record_t *license, user_account_t *user) {
    if (!ctx || !ctx->is_initialized || !license_key || !license || !user) {
        return DB_ERROR_INVALID_PARAM;
    }

    sqlite3_stmt *stmt;
    const char *sql = "SELECT l.license_id, l.user_id, l.license_key, l.issued_at, "
                     "l.expires_at, l.is_revoked, l.device_name, "
                     "u.email, u.plan, u.subscription_expires, u.is_active, "
                     "u.max_connections, u.bytes_used_today, u.bytes_limit_daily "
                     "FROM licenses l "
                     "JOIN users u ON l.user_id = u.user_id "
                     "WHERE l.license_key = ? AND l.is_revoked = 0;";

    int rc = sqlite3_prepare_v2(ctx->db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        return DB_ERROR_QUERY;
    }

    sqlite3_bind_text(stmt, 1, license_key, -1, SQLITE_STATIC);

    rc = sqlite3_step(stmt);
    if (rc != SQLITE_ROW) {
        sqlite3_finalize(stmt);
        return DB_ERROR_NOT_FOUND;
    }

    time_t now = time(NULL);
    time_t license_expires = sqlite3_column_int64(stmt, 4);
    time_t subscription_expires = sqlite3_column_int64(stmt, 9);
    bool user_active = sqlite3_column_int(stmt, 10);

    // Check if license has expired
    if (now > license_expires) {
        sqlite3_finalize(stmt);
        return DB_ERROR_NOT_FOUND;
    }

    // Check if user subscription has expired
    if (now > subscription_expires) {
        sqlite3_finalize(stmt);
        return DB_ERROR_NOT_FOUND;
    }

    // Check if user account is active
    if (!user_active) {
        sqlite3_finalize(stmt);
        return DB_ERROR_NOT_FOUND;
    }

    // Fill license structure
    memset(license, 0, sizeof(license_record_t));
    license->license_id = sqlite3_column_int64(stmt, 0);
    license->user_id = sqlite3_column_int64(stmt, 1);
    strncpy(license->license_key, (const char*)sqlite3_column_text(stmt, 2), 
            sizeof(license->license_key) - 1);
    license->issued_at = sqlite3_column_int64(stmt, 3);
    license->expires_at = license_expires;
    license->is_revoked = false;
    strncpy(license->device_name, (const char*)sqlite3_column_text(stmt, 6), 
            sizeof(license->device_name) - 1);

    // Fill user structure
    memset(user, 0, sizeof(user_account_t));
    user->user_id = license->user_id;
    strncpy(user->email, (const char*)sqlite3_column_text(stmt, 7), sizeof(user->email) - 1);
    user->plan = sqlite3_column_int(stmt, 8);
    user->subscription_expires = subscription_expires;
    user->is_active = user_active;
    user->max_connections = sqlite3_column_int(stmt, 11);
    user->bytes_used_today = sqlite3_column_int64(stmt, 12);
    user->bytes_limit_daily = sqlite3_column_int64(stmt, 13);

    sqlite3_finalize(stmt);
    return DB_SUCCESS;
}

db_error_t db_session_create(database_ctx_t *ctx, uint64_t user_id, const char *client_ip,
                            session_record_t *session) {
    if (!ctx || !ctx->is_initialized || !client_ip || !session) {
        return DB_ERROR_INVALID_PARAM;
    }

    sqlite3_stmt *stmt;
    const char *sql = "INSERT INTO sessions (user_id, client_ip, connected_at, last_activity) "
                     "VALUES (?, ?, ?, ?);";

    int rc = sqlite3_prepare_v2(ctx->db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        return DB_ERROR_QUERY;
    }

    time_t now = time(NULL);

    sqlite3_bind_int64(stmt, 1, user_id);
    sqlite3_bind_text(stmt, 2, client_ip, -1, SQLITE_STATIC);
    sqlite3_bind_int64(stmt, 3, now);
    sqlite3_bind_int64(stmt, 4, now);

    rc = sqlite3_step(stmt);
    if (rc != SQLITE_DONE) {
        sqlite3_finalize(stmt);
        return DB_ERROR_QUERY;
    }

    // Fill session structure
    memset(session, 0, sizeof(session_record_t));
    session->session_id = sqlite3_last_insert_rowid(ctx->db);
    session->user_id = user_id;
    strncpy(session->client_ip, client_ip, sizeof(session->client_ip) - 1);
    session->connected_at = now;
    session->last_activity = now;
    session->bytes_sent = 0;
    session->bytes_received = 0;
    session->is_active = true;

    sqlite3_finalize(stmt);
    return DB_SUCCESS;
}

db_error_t db_session_get_active_count(database_ctx_t *ctx, uint64_t user_id, int *count) {
    if (!ctx || !ctx->is_initialized || !count) {
        return DB_ERROR_INVALID_PARAM;
    }

    sqlite3_stmt *stmt;
    const char *sql = "SELECT COUNT(*) FROM sessions WHERE user_id = ? AND is_active = 1;";

    int rc = sqlite3_prepare_v2(ctx->db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        return DB_ERROR_QUERY;
    }

    sqlite3_bind_int64(stmt, 1, user_id);

    rc = sqlite3_step(stmt);
    if (rc != SQLITE_ROW) {
        sqlite3_finalize(stmt);
        return DB_ERROR_QUERY;
    }

    *count = sqlite3_column_int(stmt, 0);

    sqlite3_finalize(stmt);
    return DB_SUCCESS;
}

db_error_t db_session_update_activity(database_ctx_t *ctx, uint64_t session_id, 
                                     uint64_t bytes_sent, uint64_t bytes_received) {
    if (!ctx || !ctx->is_initialized) {
        return DB_ERROR_INVALID_PARAM;
    }

    sqlite3_stmt *stmt;
    const char *sql = "UPDATE sessions SET last_activity = ?, bytes_sent = ?, bytes_received = ? "
                     "WHERE session_id = ?;";

    int rc = sqlite3_prepare_v2(ctx->db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        return DB_ERROR_QUERY;
    }

    time_t now = time(NULL);

    sqlite3_bind_int64(stmt, 1, now);
    sqlite3_bind_int64(stmt, 2, bytes_sent);
    sqlite3_bind_int64(stmt, 3, bytes_received);
    sqlite3_bind_int64(stmt, 4, session_id);

    rc = sqlite3_step(stmt);
    if (rc != SQLITE_DONE) {
        sqlite3_finalize(stmt);
        return DB_ERROR_QUERY;
    }

    sqlite3_finalize(stmt);
    return DB_SUCCESS;
}

db_error_t db_session_end(database_ctx_t *ctx, uint64_t session_id) {
    if (!ctx || !ctx->is_initialized) {
        return DB_ERROR_INVALID_PARAM;
    }

    sqlite3_stmt *stmt;
    const char *sql = "UPDATE sessions SET is_active = 0, last_activity = ? WHERE session_id = ?;";

    int rc = sqlite3_prepare_v2(ctx->db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        return DB_ERROR_QUERY;
    }

    time_t now = time(NULL);

    sqlite3_bind_int64(stmt, 1, now);
    sqlite3_bind_int64(stmt, 2, session_id);

    rc = sqlite3_step(stmt);
    if (rc != SQLITE_DONE) {
        sqlite3_finalize(stmt);
        return DB_ERROR_QUERY;
    }

    sqlite3_finalize(stmt);
    return DB_SUCCESS;
}
