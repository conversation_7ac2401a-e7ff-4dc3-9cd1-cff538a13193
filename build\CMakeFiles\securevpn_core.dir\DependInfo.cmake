
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/mnt/d/secure-vpn/src/auth/license.c" "CMakeFiles/securevpn_core.dir/src/auth/license.c.o" "gcc" "CMakeFiles/securevpn_core.dir/src/auth/license.c.o.d"
  "/mnt/d/secure-vpn/src/crypto/chacha20.c" "CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.o" "gcc" "CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.o.d"
  "/mnt/d/secure-vpn/src/crypto/poly1305.c" "CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.o" "gcc" "CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.o.d"
  "/mnt/d/secure-vpn/src/crypto/utils.c" "CMakeFiles/securevpn_core.dir/src/crypto/utils.c.o" "gcc" "CMakeFiles/securevpn_core.dir/src/crypto/utils.c.o.d"
  "/mnt/d/secure-vpn/src/crypto/x25519.c" "CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.o" "gcc" "CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.o.d"
  "/mnt/d/secure-vpn/src/network/packet.c" "CMakeFiles/securevpn_core.dir/src/network/packet.c.o" "gcc" "CMakeFiles/securevpn_core.dir/src/network/packet.c.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
