#include "securevpn.h"
#include <string.h>

// ChaCha20 constants
#define CHACHA20_CONSTANT_0 0x61707865
#define CHACHA20_CONSTANT_1 0x3320646e
#define CHACHA20_CONSTANT_2 0x79622d32
#define CHACHA20_CONSTANT_3 0x6b206574

// Rotate left macro
#define ROTL(a, b) (((a) << (b)) | ((a) >> (32 - (b))))

// Quarter round function
#define QR(a, b, c, d) do { \
    a += b; d ^= a; d = ROTL(d, 16); \
    c += d; b ^= c; b = ROTL(b, 12); \
    a += b; d ^= a; d = ROTL(d, 8);  \
    c += d; b ^= c; b = ROTL(b, 7);  \
} while(0)

static void chacha20_block(uint32_t state[16], uint8_t output[64]) {
    uint32_t x[16];
    int i;
    
    // Copy state
    for (i = 0; i < 16; i++) {
        x[i] = state[i];
    }
    
    // 20 rounds (10 double rounds)
    for (i = 0; i < 10; i++) {
        // Column rounds
        QR(x[0], x[4], x[8],  x[12]);
        QR(x[1], x[5], x[9],  x[13]);
        QR(x[2], x[6], x[10], x[14]);
        QR(x[3], x[7], x[11], x[15]);
        
        // Diagonal rounds
        QR(x[0], x[5], x[10], x[15]);
        QR(x[1], x[6], x[11], x[12]);
        QR(x[2], x[7], x[8],  x[13]);
        QR(x[3], x[4], x[9],  x[14]);
    }
    
    // Add original state
    for (i = 0; i < 16; i++) {
        x[i] += state[i];
    }
    
    // Convert to bytes (little endian)
    for (i = 0; i < 16; i++) {
        output[i * 4 + 0] = x[i] & 0xff;
        output[i * 4 + 1] = (x[i] >> 8) & 0xff;
        output[i * 4 + 2] = (x[i] >> 16) & 0xff;
        output[i * 4 + 3] = (x[i] >> 24) & 0xff;
    }
}

svpn_error_t chacha20_init(chacha20_ctx_t *ctx, const uint8_t *key, const uint8_t *nonce) {
    if (!ctx || !key || !nonce) {
        return SVPN_ERROR_INVALID_PARAM;
    }
    
    memcpy(ctx->key, key, CHACHA20_KEY_SIZE);
    memcpy(ctx->nonce, nonce, CHACHA20_NONCE_SIZE);
    ctx->counter = 0;
    
    return SVPN_SUCCESS;
}

svpn_error_t chacha20_encrypt(chacha20_ctx_t *ctx, const uint8_t *plaintext, 
                             uint8_t *ciphertext, size_t len) {
    if (!ctx || !plaintext || !ciphertext) {
        return SVPN_ERROR_INVALID_PARAM;
    }
    
    uint32_t state[16];
    uint8_t keystream[64];
    size_t i, j;
    
    // Setup state
    state[0] = CHACHA20_CONSTANT_0;
    state[1] = CHACHA20_CONSTANT_1;
    state[2] = CHACHA20_CONSTANT_2;
    state[3] = CHACHA20_CONSTANT_3;
    
    // Key (8 words)
    for (i = 0; i < 8; i++) {
        state[4 + i] = ((uint32_t)ctx->key[i * 4 + 0]) |
                       ((uint32_t)ctx->key[i * 4 + 1] << 8) |
                       ((uint32_t)ctx->key[i * 4 + 2] << 16) |
                       ((uint32_t)ctx->key[i * 4 + 3] << 24);
    }
    
    // Counter (1 word)
    state[12] = ctx->counter;
    
    // Nonce (3 words)
    for (i = 0; i < 3; i++) {
        state[13 + i] = ((uint32_t)ctx->nonce[i * 4 + 0]) |
                        ((uint32_t)ctx->nonce[i * 4 + 1] << 8) |
                        ((uint32_t)ctx->nonce[i * 4 + 2] << 16) |
                        ((uint32_t)ctx->nonce[i * 4 + 3] << 24);
    }
    
    // Process data in 64-byte blocks
    for (i = 0; i < len; i += 64) {
        chacha20_block(state, keystream);
        
        // XOR with plaintext
        for (j = 0; j < 64 && (i + j) < len; j++) {
            ciphertext[i + j] = plaintext[i + j] ^ keystream[j];
        }
        
        // Increment counter
        state[12]++;
        ctx->counter++;
    }
    
    // Clear keystream
    secure_zero(keystream, sizeof(keystream));
    
    return SVPN_SUCCESS;
}

svpn_error_t chacha20_decrypt(chacha20_ctx_t *ctx, const uint8_t *ciphertext, 
                             uint8_t *plaintext, size_t len) {
    // ChaCha20 is symmetric, so decryption is the same as encryption
    return chacha20_encrypt(ctx, ciphertext, plaintext, len);
}
