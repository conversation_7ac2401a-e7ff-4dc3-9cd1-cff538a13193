@echo off
REM Secure VPN Windows Installation Script
setlocal enabledelayedexpansion

echo ========================================
echo Secure VPN Windows Installation
echo ========================================
echo.

REM Check for administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

REM Set installation directory
set INSTALL_DIR=C:\Program Files\SecureVPN
set SERVICE_NAME=SecureVPN
set DATA_DIR=%PROGRAMDATA%\SecureVPN

echo Installing Secure VPN to: %INSTALL_DIR%
echo Data directory: %DATA_DIR%
echo.

REM Create directories
echo Creating directories...
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"
if not exist "%INSTALL_DIR%\bin" mkdir "%INSTALL_DIR%\bin"
if not exist "%INSTALL_DIR%\config" mkdir "%INSTALL_DIR%\config"
if not exist "%INSTALL_DIR%\ssl" mkdir "%INSTALL_DIR%\ssl"
if not exist "%DATA_DIR%" mkdir "%DATA_DIR%"
if not exist "%DATA_DIR%\logs" mkdir "%DATA_DIR%\logs"

REM Copy files
echo Copying application files...
copy /Y "svpn_enhanced_server.exe" "%INSTALL_DIR%\bin\" >nul
copy /Y "svpn_web_api.exe" "%INSTALL_DIR%\bin\" >nul
copy /Y "vpn_manager.exe" "%INSTALL_DIR%\bin\" >nul
copy /Y "keygen.exe" "%INSTALL_DIR%\bin\" >nul
copy /Y "SecureVPN.exe" "%INSTALL_DIR%\bin\" >nul

REM Copy configuration files
echo Copying configuration files...
if exist "config\*.conf" copy /Y "config\*.conf" "%INSTALL_DIR%\config\" >nul

REM Copy SSL certificates if they exist
if exist "ssl\*.*" copy /Y "ssl\*.*" "%INSTALL_DIR%\ssl\" >nul

REM Install TAP driver
echo Installing TAP network driver...
if exist "tap-driver\tap-windows-*.exe" (
    echo Installing TAP driver...
    for %%f in (tap-driver\tap-windows-*.exe) do (
        "%%f" /S
        if !errorLevel! neq 0 (
            echo WARNING: TAP driver installation failed
        ) else (
            echo TAP driver installed successfully
        )
    )
) else (
    echo WARNING: TAP driver not found, VPN functionality may be limited
)

REM Install Visual C++ Redistributable
echo Installing Visual C++ Redistributable...
if exist "vcredist_x64.exe" (
    vcredist_x64.exe /quiet /norestart
    if !errorLevel! neq 0 (
        echo WARNING: Visual C++ Redistributable installation failed
    ) else (
        echo Visual C++ Redistributable installed successfully
    )
) else (
    echo WARNING: Visual C++ Redistributable not found
)

REM Generate SSL certificates if they don't exist
if not exist "%INSTALL_DIR%\ssl\server.key" (
    echo Generating SSL certificates...
    "%INSTALL_DIR%\bin\keygen.exe" -g -k "%INSTALL_DIR%\ssl\server.key" -p "%INSTALL_DIR%\ssl\server.pub"
    if !errorLevel! neq 0 (
        echo ERROR: Failed to generate SSL certificates
        pause
        exit /b 1
    )
    echo SSL certificates generated successfully
)

REM Initialize database
if not exist "%DATA_DIR%\vpn.db" (
    echo Initializing database...
    "%INSTALL_DIR%\bin\vpn_manager.exe" -d "%DATA_DIR%\vpn.db" -k "%INSTALL_DIR%\ssl\server.key" -p "%INSTALL_DIR%\ssl\server.pub" create-user "<EMAIL>" "admin123" "enterprise"
    if !errorLevel! neq 0 (
        echo ERROR: Failed to initialize database
        pause
        exit /b 1
    )
    echo Database initialized successfully
    echo Default admin user created: <EMAIL> / admin123
)

REM Create Windows service
echo Creating Windows service...
sc create %SERVICE_NAME% binPath= "\"%INSTALL_DIR%\bin\svpn_enhanced_server.exe\" --service --database \"%DATA_DIR%\vpn.db\" --private-key \"%INSTALL_DIR%\ssl\server.key\" --public-key \"%INSTALL_DIR%\ssl\server.pub\" --vpn-port 8443 --api-port 8080" DisplayName= "Secure VPN Service" start= auto

if %errorLevel% neq 0 (
    echo WARNING: Failed to create Windows service
) else (
    echo Windows service created successfully
)

REM Configure Windows Firewall
echo Configuring Windows Firewall...
netsh advfirewall firewall add rule name="Secure VPN Server" dir=in action=allow protocol=UDP localport=8443 >nul 2>&1
netsh advfirewall firewall add rule name="Secure VPN API" dir=in action=allow protocol=TCP localport=8080 >nul 2>&1
netsh advfirewall firewall add rule name="Secure VPN Metrics" dir=in action=allow protocol=TCP localport=9090 >nul 2>&1

if %errorLevel% neq 0 (
    echo WARNING: Failed to configure Windows Firewall
) else (
    echo Windows Firewall configured successfully
)

REM Create desktop shortcut
echo Creating desktop shortcut...
set DESKTOP=%USERPROFILE%\Desktop
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateShortcut.vbs"
echo sLinkFile = "%DESKTOP%\Secure VPN.lnk" >> "%TEMP%\CreateShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateShortcut.vbs"
echo oLink.TargetPath = "%INSTALL_DIR%\bin\SecureVPN.exe" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.WorkingDirectory = "%INSTALL_DIR%\bin" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Description = "Secure VPN Client" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Save >> "%TEMP%\CreateShortcut.vbs"
cscript "%TEMP%\CreateShortcut.vbs" >nul 2>&1
del "%TEMP%\CreateShortcut.vbs" >nul 2>&1

REM Create Start Menu shortcut
echo Creating Start Menu shortcut...
set STARTMENU=%PROGRAMDATA%\Microsoft\Windows\Start Menu\Programs
if not exist "%STARTMENU%\SecureVPN" mkdir "%STARTMENU%\SecureVPN"
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateStartMenu.vbs"
echo sLinkFile = "%STARTMENU%\SecureVPN\Secure VPN.lnk" >> "%TEMP%\CreateStartMenu.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateStartMenu.vbs"
echo oLink.TargetPath = "%INSTALL_DIR%\bin\SecureVPN.exe" >> "%TEMP%\CreateStartMenu.vbs"
echo oLink.WorkingDirectory = "%INSTALL_DIR%\bin" >> "%TEMP%\CreateStartMenu.vbs"
echo oLink.Description = "Secure VPN Client" >> "%TEMP%\CreateStartMenu.vbs"
echo oLink.Save >> "%TEMP%\CreateStartMenu.vbs"
cscript "%TEMP%\CreateStartMenu.vbs" >nul 2>&1
del "%TEMP%\CreateStartMenu.vbs" >nul 2>&1

REM Add to PATH
echo Adding to system PATH...
for /f "tokens=2*" %%A in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set CURRENT_PATH=%%B
echo !CURRENT_PATH! | find /i "%INSTALL_DIR%\bin" >nul
if %errorLevel% neq 0 (
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH /t REG_EXPAND_SZ /d "!CURRENT_PATH!;%INSTALL_DIR%\bin" /f >nul
    if !errorLevel! equ 0 (
        echo System PATH updated successfully
    ) else (
        echo WARNING: Failed to update system PATH
    )
) else (
    echo System PATH already contains installation directory
)

REM Set permissions
echo Setting file permissions...
icacls "%INSTALL_DIR%" /grant "Users:(OI)(CI)RX" /T >nul 2>&1
icacls "%DATA_DIR%" /grant "Users:(OI)(CI)F" /T >nul 2>&1

REM Start the service
echo Starting Secure VPN service...
sc start %SERVICE_NAME% >nul 2>&1
if %errorLevel% neq 0 (
    echo WARNING: Failed to start service automatically
    echo You can start it manually from Services or by running:
    echo sc start %SERVICE_NAME%
) else (
    echo Service started successfully
)

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo Installation directory: %INSTALL_DIR%
echo Data directory: %DATA_DIR%
echo Service name: %SERVICE_NAME%
echo.
echo Default admin credentials:
echo   Email: <EMAIL>
echo   Password: admin123
echo.
echo VPN Server: localhost:8443
echo Web API: http://localhost:8080
echo Metrics: http://localhost:9090/metrics
echo.
echo You can now:
echo 1. Launch the GUI client from the desktop shortcut
echo 2. Access the web dashboard at http://localhost:8080
echo 3. Manage users with: vpn_manager.exe
echo.
echo For support, visit: https://securevpn.com/support
echo.
pause
