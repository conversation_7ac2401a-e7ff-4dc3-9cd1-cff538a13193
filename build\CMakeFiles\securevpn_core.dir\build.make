# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/d/secure-vpn

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/d/secure-vpn/build

# Include any dependencies generated for this target.
include CMakeFiles/securevpn_core.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/securevpn_core.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/securevpn_core.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/securevpn_core.dir/flags.make

CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.o: CMakeFiles/securevpn_core.dir/flags.make
CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.o: ../src/crypto/chacha20.c
CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.o: CMakeFiles/securevpn_core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/d/secure-vpn/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.o -MF CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.o.d -o CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.o -c /mnt/d/secure-vpn/src/crypto/chacha20.c

CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /mnt/d/secure-vpn/src/crypto/chacha20.c > CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.i

CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /mnt/d/secure-vpn/src/crypto/chacha20.c -o CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.s

CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.o: CMakeFiles/securevpn_core.dir/flags.make
CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.o: ../src/crypto/poly1305.c
CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.o: CMakeFiles/securevpn_core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/d/secure-vpn/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.o -MF CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.o.d -o CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.o -c /mnt/d/secure-vpn/src/crypto/poly1305.c

CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /mnt/d/secure-vpn/src/crypto/poly1305.c > CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.i

CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /mnt/d/secure-vpn/src/crypto/poly1305.c -o CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.s

CMakeFiles/securevpn_core.dir/src/crypto/utils.c.o: CMakeFiles/securevpn_core.dir/flags.make
CMakeFiles/securevpn_core.dir/src/crypto/utils.c.o: ../src/crypto/utils.c
CMakeFiles/securevpn_core.dir/src/crypto/utils.c.o: CMakeFiles/securevpn_core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/d/secure-vpn/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/securevpn_core.dir/src/crypto/utils.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/securevpn_core.dir/src/crypto/utils.c.o -MF CMakeFiles/securevpn_core.dir/src/crypto/utils.c.o.d -o CMakeFiles/securevpn_core.dir/src/crypto/utils.c.o -c /mnt/d/secure-vpn/src/crypto/utils.c

CMakeFiles/securevpn_core.dir/src/crypto/utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/securevpn_core.dir/src/crypto/utils.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /mnt/d/secure-vpn/src/crypto/utils.c > CMakeFiles/securevpn_core.dir/src/crypto/utils.c.i

CMakeFiles/securevpn_core.dir/src/crypto/utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/securevpn_core.dir/src/crypto/utils.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /mnt/d/secure-vpn/src/crypto/utils.c -o CMakeFiles/securevpn_core.dir/src/crypto/utils.c.s

CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.o: CMakeFiles/securevpn_core.dir/flags.make
CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.o: ../src/crypto/x25519.c
CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.o: CMakeFiles/securevpn_core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/d/secure-vpn/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.o -MF CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.o.d -o CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.o -c /mnt/d/secure-vpn/src/crypto/x25519.c

CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /mnt/d/secure-vpn/src/crypto/x25519.c > CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.i

CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /mnt/d/secure-vpn/src/crypto/x25519.c -o CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.s

CMakeFiles/securevpn_core.dir/src/network/packet.c.o: CMakeFiles/securevpn_core.dir/flags.make
CMakeFiles/securevpn_core.dir/src/network/packet.c.o: ../src/network/packet.c
CMakeFiles/securevpn_core.dir/src/network/packet.c.o: CMakeFiles/securevpn_core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/d/secure-vpn/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/securevpn_core.dir/src/network/packet.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/securevpn_core.dir/src/network/packet.c.o -MF CMakeFiles/securevpn_core.dir/src/network/packet.c.o.d -o CMakeFiles/securevpn_core.dir/src/network/packet.c.o -c /mnt/d/secure-vpn/src/network/packet.c

CMakeFiles/securevpn_core.dir/src/network/packet.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/securevpn_core.dir/src/network/packet.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /mnt/d/secure-vpn/src/network/packet.c > CMakeFiles/securevpn_core.dir/src/network/packet.c.i

CMakeFiles/securevpn_core.dir/src/network/packet.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/securevpn_core.dir/src/network/packet.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /mnt/d/secure-vpn/src/network/packet.c -o CMakeFiles/securevpn_core.dir/src/network/packet.c.s

CMakeFiles/securevpn_core.dir/src/auth/license.c.o: CMakeFiles/securevpn_core.dir/flags.make
CMakeFiles/securevpn_core.dir/src/auth/license.c.o: ../src/auth/license.c
CMakeFiles/securevpn_core.dir/src/auth/license.c.o: CMakeFiles/securevpn_core.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/d/secure-vpn/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/securevpn_core.dir/src/auth/license.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/securevpn_core.dir/src/auth/license.c.o -MF CMakeFiles/securevpn_core.dir/src/auth/license.c.o.d -o CMakeFiles/securevpn_core.dir/src/auth/license.c.o -c /mnt/d/secure-vpn/src/auth/license.c

CMakeFiles/securevpn_core.dir/src/auth/license.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/securevpn_core.dir/src/auth/license.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /mnt/d/secure-vpn/src/auth/license.c > CMakeFiles/securevpn_core.dir/src/auth/license.c.i

CMakeFiles/securevpn_core.dir/src/auth/license.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/securevpn_core.dir/src/auth/license.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /mnt/d/secure-vpn/src/auth/license.c -o CMakeFiles/securevpn_core.dir/src/auth/license.c.s

# Object files for target securevpn_core
securevpn_core_OBJECTS = \
"CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.o" \
"CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.o" \
"CMakeFiles/securevpn_core.dir/src/crypto/utils.c.o" \
"CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.o" \
"CMakeFiles/securevpn_core.dir/src/network/packet.c.o" \
"CMakeFiles/securevpn_core.dir/src/auth/license.c.o"

# External object files for target securevpn_core
securevpn_core_EXTERNAL_OBJECTS =

libsecurevpn_core.a: CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.o
libsecurevpn_core.a: CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.o
libsecurevpn_core.a: CMakeFiles/securevpn_core.dir/src/crypto/utils.c.o
libsecurevpn_core.a: CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.o
libsecurevpn_core.a: CMakeFiles/securevpn_core.dir/src/network/packet.c.o
libsecurevpn_core.a: CMakeFiles/securevpn_core.dir/src/auth/license.c.o
libsecurevpn_core.a: CMakeFiles/securevpn_core.dir/build.make
libsecurevpn_core.a: CMakeFiles/securevpn_core.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/mnt/d/secure-vpn/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking C static library libsecurevpn_core.a"
	$(CMAKE_COMMAND) -P CMakeFiles/securevpn_core.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/securevpn_core.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/securevpn_core.dir/build: libsecurevpn_core.a
.PHONY : CMakeFiles/securevpn_core.dir/build

CMakeFiles/securevpn_core.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/securevpn_core.dir/cmake_clean.cmake
.PHONY : CMakeFiles/securevpn_core.dir/clean

CMakeFiles/securevpn_core.dir/depend:
	cd /mnt/d/secure-vpn/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /mnt/d/secure-vpn /mnt/d/secure-vpn /mnt/d/secure-vpn/build /mnt/d/secure-vpn/build /mnt/d/secure-vpn/build/CMakeFiles/securevpn_core.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/securevpn_core.dir/depend

