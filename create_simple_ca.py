#!/usr/bin/env python3
"""
Create Simple CA Certificate for OpenVPN
"""

import subprocess
import os
from pathlib import Path

def create_openssl_config():
    """Create a simple OpenSSL config"""
    config = """
[req]
distinguished_name = req_distinguished_name
x509_extensions = v3_ca
prompt = no

[req_distinguished_name]
C = US
ST = CA
L = SF
O = SecureVPN
CN = SecureVPN-CA

[v3_ca]
basicConstraints = CA:TRUE
keyUsage = keyCertSign, cRLSign
"""
    
    with open("openssl.cnf", "w") as f:
        f.write(config)
    
    return "openssl.cnf"

def create_ca_cert():
    """Create CA certificate"""
    print("🔐 Creating CA certificate...")
    
    # Create directories
    Path("openvpn/ca").mkdir(parents=True, exist_ok=True)
    
    # Create OpenSSL config
    config_file = create_openssl_config()
    
    try:
        # Generate CA key
        subprocess.run([
            "openssl", "genrsa", "-out", "openvpn/ca/ca.key", "2048"
        ], check=True, env={**os.environ, "OPENSSL_CONF": config_file})
        
        # Generate CA certificate
        subprocess.run([
            "openssl", "req", "-new", "-x509", "-days", "3650",
            "-key", "openvpn/ca/ca.key", "-out", "openvpn/ca/ca.crt",
            "-config", config_file
        ], check=True, env={**os.environ, "OPENSSL_CONF": config_file})
        
        print("✅ CA certificate created")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create CA: {e}")
        return False
    finally:
        # Clean up
        if os.path.exists(config_file):
            os.remove(config_file)

def create_openvpn_config_with_ca():
    """Create OpenVPN config with embedded CA"""
    print("📱 Creating OpenVPN config with CA...")
    
    # Read CA certificate
    try:
        with open("openvpn/ca/ca.crt", "r") as f:
            ca_cert = f.read()
    except:
        print("❌ CA certificate not found")
        return False
    
    # Create OpenVPN config with embedded CA
    config = f"""# OpenVPN Config with Embedded CA Certificate
# Username: <EMAIL>
# Password: password

client
dev tun
proto tcp
remote ************* 1194
resolv-retry infinite
nobind
persist-key
persist-tun

# Authentication
auth-user-pass

# Modern security
data-ciphers AES-256-GCM:AES-128-GCM
cipher AES-256-GCM
auth SHA256

# Embedded CA Certificate
<ca>
{ca_cert}</ca>

# Logging
verb 3
mute 20

# Windows compatibility
route-method exe
route-delay 2

# DNS
dhcp-option DNS *******
dhcp-option DNS *******
"""
    
    # Save config
    Path("vpn_profiles").mkdir(exist_ok=True)
    config_file = Path("vpn_profiles/working_with_ca.ovpn")
    
    with open(config_file, "w") as f:
        f.write(config)
    
    print(f"✅ OpenVPN config created: {config_file}")
    return config_file

def main():
    """Main function"""
    print("🔐 Creating Simple CA for OpenVPN")
    print("=" * 40)
    
    if create_ca_cert():
        config_file = create_openvpn_config_with_ca()
        if config_file:
            print(f"\n🎉 Ready to use!")
            print(f"📱 Config file: {config_file}")
            print("🔑 Username: <EMAIL>")
            print("🔑 Password: password")
    else:
        print("❌ Failed to create CA certificate")

if __name__ == "__main__":
    main()
