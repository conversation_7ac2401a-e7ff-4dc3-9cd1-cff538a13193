# Working OpenVPN Config for Your VPN
# This connects through the bridge to your custom VPN server

client
dev tun
proto tcp
remote ************* 1194
resolv-retry infinite
nobind
persist-key
persist-tun

# Authentication
auth-user-pass

# Basic security
cipher AES-256-CBC
auth SHA1

# Disable certificate verification for bridge
# verify-x509-name none
# remote-cert-tls server

# Logging
verb 3
mute 20

# Compatibility
route-method exe
route-delay 2

# DNS
dhcp-option DNS *******
dhcp-option DNS *******
