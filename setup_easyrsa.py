#!/usr/bin/env python3
"""
Setup Easy-RSA for Windows OpenVPN Certificate Generation
"""

import os
import sys
import subprocess
import urllib.request
import zipfile
from pathlib import Path

def download_easyrsa():
    """Download Easy-RSA for Windows"""
    print("📥 Downloading Easy-RSA...")
    
    url = "https://github.com/OpenVPN/easy-rsa/releases/download/v3.1.7/EasyRSA-3.1.7-win64.zip"
    zip_file = "EasyRSA.zip"
    
    try:
        urllib.request.urlretrieve(url, zip_file)
        print("✅ Easy-RSA downloaded")
        
        # Extract
        with zipfile.ZipFile(zip_file, 'r') as zip_ref:
            zip_ref.extractall(".")
        
        os.remove(zip_file)
        print("✅ Easy-RSA extracted")
        return True
        
    except Exception as e:
        print(f"❌ Failed to download Easy-RSA: {e}")
        return False

def setup_easyrsa():
    """Setup Easy-RSA environment"""
    print("🔧 Setting up Easy-RSA...")
    
    # Find Easy-RSA directory
    easyrsa_dirs = [d for d in os.listdir(".") if d.startswith("EasyRSA")]
    if not easyrsa_dirs:
        print("❌ Easy-RSA directory not found")
        return False
    
    easyrsa_dir = easyrsa_dirs[0]
    os.chdir(easyrsa_dir)
    
    # Initialize PKI
    try:
        subprocess.run(["EasyRSA-Start.bat", "init-pki"], check=True, shell=True)
        print("✅ PKI initialized")
        return True
    except Exception as e:
        print(f"❌ Failed to initialize PKI: {e}")
        return False

def create_certificates():
    """Create all necessary certificates"""
    print("🔐 Creating certificates...")
    
    try:
        # Build CA (non-interactive)
        subprocess.run([
            "EasyRSA-Start.bat", "build-ca", "nopass"
        ], input="SecureVPN-CA\n", text=True, check=True, shell=True)
        print("✅ CA certificate created")
        
        # Build server certificate
        subprocess.run([
            "EasyRSA-Start.bat", "build-server-full", "server", "nopass"
        ], check=True, shell=True)
        print("✅ Server certificate created")
        
        # Build client certificate
        subprocess.run([
            "EasyRSA-Start.bat", "build-client-full", "client1", "nopass"
        ], check=True, shell=True)
        print("✅ Client certificate created")
        
        # Generate DH parameters
        subprocess.run([
            "EasyRSA-Start.bat", "gen-dh"
        ], check=True, shell=True)
        print("✅ DH parameters generated")
        
        return True
        
    except Exception as e:
        print(f"❌ Certificate creation failed: {e}")
        return False

def create_openvpn_config():
    """Create OpenVPN config with proper certificates"""
    print("📱 Creating OpenVPN configuration...")
    
    try:
        # Read certificates
        with open("pki/ca.crt", "r") as f:
            ca_cert = f.read()
        
        with open("pki/issued/client1.crt", "r") as f:
            client_cert = f.read()
        
        with open("pki/private/client1.key", "r") as f:
            client_key = f.read()
        
        # Create config with embedded certificates
        config = f"""# OpenVPN Config with Proper Certificates
# Username: <EMAIL>
# Password: password

client
dev tun
proto tcp
remote ************* 1194
resolv-retry infinite
nobind
persist-key
persist-tun

# Authentication
auth-user-pass

# Modern security
data-ciphers AES-256-GCM:AES-128-GCM:CHACHA20-POLY1305
cipher AES-256-GCM
auth SHA256

# Embedded certificates
<ca>
{ca_cert}</ca>

<cert>
{client_cert}</cert>

<key>
{client_key}</key>

# Logging
verb 3
mute 20

# Windows compatibility
route-method exe
route-delay 2

# DNS
dhcp-option DNS *******
dhcp-option DNS *******
"""
        
        # Go back to main directory
        os.chdir("..")
        
        # Save config
        Path("vpn_profiles").mkdir(exist_ok=True)
        config_file = Path("vpn_profiles/proper_openvpn.ovpn")
        
        with open(config_file, "w") as f:
            f.write(config)
        
        print(f"✅ OpenVPN config created: {config_file}")
        return config_file
        
    except Exception as e:
        print(f"❌ Failed to create config: {e}")
        return None

def main():
    """Main setup function"""
    print("🔐 Easy-RSA OpenVPN Certificate Setup")
    print("=" * 50)
    
    # Check if Easy-RSA already exists
    easyrsa_dirs = [d for d in os.listdir(".") if d.startswith("EasyRSA")]
    
    if not easyrsa_dirs:
        if not download_easyrsa():
            return
    
    if setup_easyrsa():
        if create_certificates():
            config_file = create_openvpn_config()
            if config_file:
                print("\n🎉 Setup Complete!")
                print("=" * 30)
                print(f"📱 Config file: {config_file}")
                print("🔑 Username: <EMAIL>")
                print("🔑 Password: password")
                print("\n🔌 This config has proper certificates and should work!")

if __name__ == "__main__":
    main()
