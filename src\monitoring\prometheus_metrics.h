#ifndef PROMETHEUS_METRICS_H
#define PROMETHEUS_METRICS_H

#include <stdint.h>
#include <stdbool.h>
#include <time.h>

// Prometheus metrics export for VPN monitoring
typedef enum {
    METRICS_SUCCESS = 0,
    METRICS_ERROR_INVALID_PARAM = -1,
    METRICS_ERROR_MEMORY = -2,
    METRICS_ERROR_IO = -3,
    METRICS_ERROR_FORMAT = -4
} metrics_error_t;

typedef enum {
    METRIC_TYPE_COUNTER = 0,
    METRIC_TYPE_GAUGE = 1,
    METRIC_TYPE_HISTOGRAM = 2,
    METRIC_TYPE_SUMMARY = 3
} metric_type_t;

// Individual metric structure
typedef struct {
    char name[128];
    char help[256];
    metric_type_t type;
    double value;
    char labels[512];  // JSON-like format: {"label1":"value1","label2":"value2"}
    time_t last_updated;
} metric_t;

// Histogram bucket
typedef struct {
    double upper_bound;
    uint64_t count;
} histogram_bucket_t;

// Histogram metric
typedef struct {
    char name[128];
    char help[256];
    histogram_bucket_t buckets[20];  // Support up to 20 buckets
    uint32_t bucket_count;
    uint64_t total_count;
    double total_sum;
    char labels[512];
    time_t last_updated;
} histogram_metric_t;

// Metrics registry
typedef struct {
    metric_t *metrics;
    uint32_t metric_count;
    uint32_t metric_capacity;
    
    histogram_metric_t *histograms;
    uint32_t histogram_count;
    uint32_t histogram_capacity;
    
    bool is_initialized;
    char instance_id[64];
    char version[32];
} metrics_registry_t;

// VPN-specific metrics
typedef struct {
    // Connection metrics
    uint64_t total_connections;
    uint64_t active_connections;
    uint64_t failed_connections;
    uint64_t connections_per_plan[4];  // Free, Basic, Premium, Enterprise
    
    // Traffic metrics
    uint64_t bytes_sent_total;
    uint64_t bytes_received_total;
    uint64_t packets_sent_total;
    uint64_t packets_received_total;
    
    // User metrics
    uint64_t total_users;
    uint64_t active_users_24h;
    uint64_t new_users_today;
    uint64_t subscription_renewals;
    uint64_t subscription_cancellations;
    
    // Performance metrics
    double avg_connection_time_ms;
    double avg_throughput_mbps;
    double cpu_usage_percent;
    double memory_usage_percent;
    double disk_usage_percent;
    
    // Security metrics
    uint64_t blocked_connections;
    uint64_t failed_auth_attempts;
    uint64_t banned_ips;
    uint64_t security_events;
    
    // Business metrics
    double monthly_revenue;
    double average_revenue_per_user;
    uint64_t payment_failures;
    uint64_t refunds;
    
    time_t last_updated;
} vpn_metrics_t;

// Initialize metrics system
metrics_error_t metrics_init(metrics_registry_t *registry, const char *instance_id, 
                           const char *version);
void metrics_cleanup(metrics_registry_t *registry);

// Basic metric operations
metrics_error_t metrics_register_counter(metrics_registry_t *registry, const char *name,
                                        const char *help, const char *labels);
metrics_error_t metrics_register_gauge(metrics_registry_t *registry, const char *name,
                                      const char *help, const char *labels);
metrics_error_t metrics_register_histogram(metrics_registry_t *registry, const char *name,
                                          const char *help, const char *labels,
                                          const double *buckets, uint32_t bucket_count);

// Update metric values
metrics_error_t metrics_counter_inc(metrics_registry_t *registry, const char *name,
                                   const char *labels);
metrics_error_t metrics_counter_add(metrics_registry_t *registry, const char *name,
                                   const char *labels, double value);
metrics_error_t metrics_gauge_set(metrics_registry_t *registry, const char *name,
                                 const char *labels, double value);
metrics_error_t metrics_gauge_inc(metrics_registry_t *registry, const char *name,
                                 const char *labels);
metrics_error_t metrics_gauge_dec(metrics_registry_t *registry, const char *name,
                                 const char *labels);
metrics_error_t metrics_histogram_observe(metrics_registry_t *registry, const char *name,
                                         const char *labels, double value);

// VPN-specific metric functions
metrics_error_t metrics_init_vpn_metrics(metrics_registry_t *registry);
metrics_error_t metrics_update_vpn_metrics(metrics_registry_t *registry, 
                                          const vpn_metrics_t *vpn_metrics);
metrics_error_t metrics_record_connection(metrics_registry_t *registry, 
                                         subscription_plan_t plan, bool success);
metrics_error_t metrics_record_traffic(metrics_registry_t *registry, uint64_t bytes_sent,
                                      uint64_t bytes_received);
metrics_error_t metrics_record_user_activity(metrics_registry_t *registry, 
                                            subscription_plan_t plan, bool new_user);
metrics_error_t metrics_record_security_event(metrics_registry_t *registry, 
                                             const char *event_type, const char *severity);
metrics_error_t metrics_record_payment(metrics_registry_t *registry, subscription_plan_t plan,
                                      double amount, bool success);

// Export functions
metrics_error_t metrics_export_prometheus(metrics_registry_t *registry, char *output,
                                         size_t output_size);
metrics_error_t metrics_export_json(metrics_registry_t *registry, char *output,
                                   size_t output_size);
metrics_error_t metrics_save_to_file(metrics_registry_t *registry, const char *filename,
                                    const char *format);

// HTTP endpoint for metrics
metrics_error_t metrics_start_http_server(metrics_registry_t *registry, uint16_t port);
void metrics_stop_http_server(void);

// Real-time metrics collection
typedef struct {
    metrics_registry_t *registry;
    uint32_t collection_interval_seconds;
    bool is_running;
    pthread_t collection_thread;
} metrics_collector_t;

metrics_error_t metrics_start_collector(metrics_collector_t *collector,
                                       metrics_registry_t *registry,
                                       uint32_t interval_seconds);
void metrics_stop_collector(metrics_collector_t *collector);

// Alerting system
typedef enum {
    ALERT_SEVERITY_INFO = 0,
    ALERT_SEVERITY_WARNING = 1,
    ALERT_SEVERITY_CRITICAL = 2
} alert_severity_t;

typedef struct {
    char name[128];
    char description[256];
    char metric_name[128];
    double threshold;
    alert_severity_t severity;
    bool is_active;
    time_t triggered_at;
    time_t last_sent;
} alert_rule_t;

typedef struct {
    alert_rule_t *rules;
    uint32_t rule_count;
    uint32_t rule_capacity;
    char webhook_url[512];
    char email_recipients[1024];
    bool is_initialized;
} alerting_system_t;

metrics_error_t alerting_init(alerting_system_t *alerting, const char *webhook_url,
                            const char *email_recipients);
void alerting_cleanup(alerting_system_t *alerting);
metrics_error_t alerting_add_rule(alerting_system_t *alerting, const char *name,
                                 const char *description, const char *metric_name,
                                 double threshold, alert_severity_t severity);
metrics_error_t alerting_check_rules(alerting_system_t *alerting, 
                                    metrics_registry_t *registry);
metrics_error_t alerting_send_notification(alerting_system_t *alerting,
                                          const alert_rule_t *rule, double current_value);

// Dashboard data export
typedef struct {
    char title[128];
    char description[256];
    metric_type_t type;
    char metric_name[128];
    char labels[512];
    double current_value;
    double previous_value;
    double change_percent;
    time_t last_updated;
} dashboard_widget_t;

typedef struct {
    dashboard_widget_t *widgets;
    uint32_t widget_count;
    uint32_t widget_capacity;
    char dashboard_name[128];
    time_t created_at;
    time_t last_updated;
} dashboard_t;

metrics_error_t dashboard_create(dashboard_t *dashboard, const char *name);
void dashboard_cleanup(dashboard_t *dashboard);
metrics_error_t dashboard_add_widget(dashboard_t *dashboard, const char *title,
                                    const char *description, const char *metric_name,
                                    const char *labels);
metrics_error_t dashboard_update_widgets(dashboard_t *dashboard, 
                                        metrics_registry_t *registry);
metrics_error_t dashboard_export_json(dashboard_t *dashboard, char *output,
                                     size_t output_size);

// Utility functions
const char* metrics_error_string(metrics_error_t error);
const char* metric_type_string(metric_type_t type);
const char* alert_severity_string(alert_severity_t severity);
double metrics_calculate_percentile(double *values, uint32_t count, double percentile);
metrics_error_t metrics_get_system_stats(double *cpu_percent, double *memory_percent,
                                        double *disk_percent);

#endif // PROMETHEUS_METRICS_H
