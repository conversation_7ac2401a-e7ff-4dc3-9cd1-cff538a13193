<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VPN-in-a-Box - The Only VPN You Actually Own</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
        }
        
        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            transition: opacity 0.3s;
        }
        
        .nav-links a:hover {
            opacity: 0.8;
        }
        
        .hero {
            padding: 120px 0 80px;
            text-align: center;
            color: white;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        .hero .subtitle {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .hero .description {
            font-size: 1.2rem;
            margin-bottom: 3rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #ff6b6b;
            color: white;
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        
        .features {
            background: white;
            padding: 80px 0;
        }
        
        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #333;
        }
        
        .comparison-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-bottom: 4rem;
        }
        
        .comparison-table table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 20px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .comparison-table .highlight {
            background: #e8f5e8;
            font-weight: 600;
            color: #27ae60;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-card h3 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: #333;
        }
        
        .feature-card ul {
            list-style: none;
        }
        
        .feature-card li {
            padding: 0.5rem 0;
            color: #666;
        }
        
        .feature-card li:before {
            content: "✅ ";
            margin-right: 0.5rem;
        }
        
        .pricing {
            background: #f8f9fa;
            padding: 80px 0;
        }
        
        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .pricing-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            position: relative;
        }
        
        .pricing-card.featured {
            border: 3px solid #ff6b6b;
            transform: scale(1.05);
        }
        
        .pricing-card.featured::before {
            content: "MOST POPULAR";
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: #ff6b6b;
            color: white;
            padding: 5px 20px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .price {
            font-size: 3rem;
            font-weight: 700;
            color: #333;
            margin: 1rem 0;
        }
        
        .price-note {
            color: #666;
            margin-bottom: 2rem;
        }
        
        .footer {
            background: #333;
            color: white;
            padding: 40px 0;
            text-align: center;
        }
        
        .screenshot {
            max-width: 100%;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            margin: 3rem 0;
        }
        
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .pricing-card.featured {
                transform: none;
            }
        }
    </style>
</head>
<body>
    <header>
        <nav class="container">
            <div class="logo">🔒 VPN-in-a-Box</div>
            <ul class="nav-links">
                <li><a href="#features">Features</a></li>
                <li><a href="#pricing">Pricing</a></li>
                <li><a href="#demo">Demo</a></li>
                <li><a href="https://github.com/yourusername/vpn-in-a-box">GitHub</a></li>
            </ul>
        </nav>
    </header>

    <section class="hero">
        <div class="container">
            <h1>🔒 VPN-in-a-Box</h1>
            <div class="subtitle">The Only VPN You Actually Own</div>
            <p class="description">
                Stop trusting VPN companies with your privacy. Build your own VPN server with enterprise-grade security and professional client apps.
            </p>
            <div class="cta-buttons">
                <a href="#pricing" class="btn btn-primary">Start Free</a>
                <a href="#demo" class="btn btn-secondary">Watch Demo</a>
            </div>
        </div>
    </section>

    <section class="features" id="features">
        <div class="container">
            <h2 class="section-title">Why VPN-in-a-Box?</h2>
            
            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Feature</th>
                            <th>Commercial VPNs</th>
                            <th class="highlight">VPN-in-a-Box</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Privacy</strong></td>
                            <td>"Trust us, we don't log"</td>
                            <td class="highlight"><strong>YOU own the server</strong></td>
                        </tr>
                        <tr>
                            <td><strong>Cost</strong></td>
                            <td>$12.95/month forever</td>
                            <td class="highlight"><strong>$50 one-time setup</strong></td>
                        </tr>
                        <tr>
                            <td><strong>Server</strong></td>
                            <td>Shared with thousands</td>
                            <td class="highlight"><strong>Dedicated personal server</strong></td>
                        </tr>
                        <tr>
                            <td><strong>Control</strong></td>
                            <td>Fixed features</td>
                            <td class="highlight"><strong>Fully customizable</strong></td>
                        </tr>
                        <tr>
                            <td><strong>Trust</strong></td>
                            <td>Corporate promises</td>
                            <td class="highlight"><strong>Complete ownership</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🖥️ Professional Client Apps</h3>
                    <ul>
                        <li>Modern UI with real-time statistics</li>
                        <li>Cross-platform (Windows, macOS, Linux)</li>
                        <li>Live monitoring of connection status</li>
                        <li>Data usage tracking and speed metrics</li>
                        <li>One-click connect/disconnect</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🛡️ Enterprise Security</h3>
                    <ul>
                        <li>AES-256 encryption (military-grade)</li>
                        <li>Custom protocols + OpenVPN compatibility</li>
                        <li>Kill switch protection</li>
                        <li>DNS leak protection</li>
                        <li>Perfect forward secrecy</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🚀 Easy Deployment</h3>
                    <ul>
                        <li>5-minute setup with guided wizard</li>
                        <li>Cloud deployment (AWS, DigitalOcean, Vultr)</li>
                        <li>Local hosting on your own hardware</li>
                        <li>Automatic SSL certificates</li>
                        <li>Docker containers for easy scaling</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <section class="pricing" id="pricing">
        <div class="container">
            <h2 class="section-title">Simple, Honest Pricing</h2>
            
            <div class="pricing-grid">
                <div class="pricing-card">
                    <h3>🆓 Free</h3>
                    <div class="price">$0</div>
                    <div class="price-note">Forever</div>
                    <ul style="text-align: left;">
                        <li>✅ Basic VPN server</li>
                        <li>✅ 1 device</li>
                        <li>✅ 10GB/month</li>
                        <li>✅ Community support</li>
                        <li>❌ No professional client</li>
                    </ul>
                    <a href="https://github.com/yourusername/vpn-in-a-box" class="btn btn-secondary" style="margin-top: 2rem;">Start Free</a>
                </div>

                <div class="pricing-card featured">
                    <h3>🥈 Silver</h3>
                    <div class="price">$19.99</div>
                    <div class="price-note">Per month</div>
                    <ul style="text-align: left;">
                        <li>✅ Professional client apps</li>
                        <li>✅ 10 devices</li>
                        <li>✅ Unlimited bandwidth</li>
                        <li>✅ Mobile apps</li>
                        <li>✅ Kill switch & DNS protection</li>
                    </ul>
                    <a href="#" class="btn btn-primary" style="margin-top: 2rem;">Start Silver</a>
                </div>

                <div class="pricing-card">
                    <h3>🥇 Gold</h3>
                    <div class="price">$49.99</div>
                    <div class="price-note">Per month</div>
                    <ul style="text-align: left;">
                        <li>✅ Everything in Silver</li>
                        <li>✅ 50 devices</li>
                        <li>✅ Team management</li>
                        <li>✅ Multi-server support</li>
                        <li>✅ Priority support & SLA</li>
                    </ul>
                    <a href="#" class="btn btn-secondary" style="margin-top: 2rem;">Start Gold</a>
                </div>
            </div>

            <div style="text-align: center; margin-top: 3rem; padding: 2rem; background: rgba(255,255,255,0.1); border-radius: 15px;">
                <h3 style="color: #333; margin-bottom: 1rem;">💎 Want to Own It Forever?</h3>
                <p style="color: #666; margin-bottom: 1.5rem;">Skip monthly fees with our lifetime plans</p>
                <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <div style="background: white; padding: 1rem; border-radius: 10px; min-width: 150px;">
                        <strong>Silver Forever</strong><br>
                        <span style="font-size: 1.5rem; color: #27ae60;">$399</span><br>
                        <small>vs $2,400 over 10 years</small>
                    </div>
                    <div style="background: white; padding: 1rem; border-radius: 10px; min-width: 150px;">
                        <strong>Gold Forever</strong><br>
                        <span style="font-size: 1.5rem; color: #27ae60;">$999</span><br>
                        <small>vs $6,000 over 10 years</small>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 VPN-in-a-Box. Own Your Privacy.</p>
            <p>Made with ❤️ for privacy-conscious developers</p>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>
