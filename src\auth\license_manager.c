#include "securevpn.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>

// Enhanced license manager that integrates with database
typedef struct {
    database_ctx_t *db_ctx;
    uint8_t server_private_key[RSA_KEY_SIZE];
    uint8_t server_public_key[RSA_KEY_SIZE];
    bool is_initialized;
} license_manager_t;

static license_manager_t g_license_manager = {0};

// Initialize license manager with database
svpn_error_t license_manager_init(const char *db_path, const char *private_key_file, 
                                 const char *public_key_file) {
    if (!db_path || !private_key_file || !public_key_file) {
        return SVPN_ERROR_INVALID_PARAM;
    }

    // Initialize database
    g_license_manager.db_ctx = malloc(sizeof(database_ctx_t));
    if (!g_license_manager.db_ctx) {
        return SVPN_ERROR_MEMORY;
    }

    db_error_t db_result = db_init(g_license_manager.db_ctx, db_path);
    if (db_result != DB_SUCCESS) {
        free(g_license_manager.db_ctx);
        g_license_manager.db_ctx = NULL;
        return SVPN_ERROR_SYSTEM;
    }

    // Create database tables if they don't exist
    db_result = db_create_tables(g_license_manager.db_ctx);
    if (db_result != DB_SUCCESS) {
        db_close(g_license_manager.db_ctx);
        free(g_license_manager.db_ctx);
        g_license_manager.db_ctx = NULL;
        return SVPN_ERROR_SYSTEM;
    }

    // Load RSA keys (simplified - in production use proper key loading)
    FILE *priv_file = fopen(private_key_file, "rb");
    if (!priv_file) {
        db_close(g_license_manager.db_ctx);
        free(g_license_manager.db_ctx);
        g_license_manager.db_ctx = NULL;
        return SVPN_ERROR_SYSTEM;
    }

    size_t read_bytes = fread(g_license_manager.server_private_key, 1, RSA_KEY_SIZE, priv_file);
    fclose(priv_file);

    if (read_bytes != RSA_KEY_SIZE) {
        db_close(g_license_manager.db_ctx);
        free(g_license_manager.db_ctx);
        g_license_manager.db_ctx = NULL;
        return SVPN_ERROR_SYSTEM;
    }

    FILE *pub_file = fopen(public_key_file, "rb");
    if (!pub_file) {
        db_close(g_license_manager.db_ctx);
        free(g_license_manager.db_ctx);
        g_license_manager.db_ctx = NULL;
        return SVPN_ERROR_SYSTEM;
    }

    read_bytes = fread(g_license_manager.server_public_key, 1, RSA_KEY_SIZE, pub_file);
    fclose(pub_file);

    if (read_bytes != RSA_KEY_SIZE) {
        db_close(g_license_manager.db_ctx);
        free(g_license_manager.db_ctx);
        g_license_manager.db_ctx = NULL;
        return SVPN_ERROR_SYSTEM;
    }

    g_license_manager.is_initialized = true;
    return SVPN_SUCCESS;
}

// Cleanup license manager
void license_manager_cleanup(void) {
    if (g_license_manager.db_ctx) {
        db_close(g_license_manager.db_ctx);
        free(g_license_manager.db_ctx);
        g_license_manager.db_ctx = NULL;
    }

    // Clear sensitive data
    secure_zero(g_license_manager.server_private_key, sizeof(g_license_manager.server_private_key));
    secure_zero(g_license_manager.server_public_key, sizeof(g_license_manager.server_public_key));
    
    g_license_manager.is_initialized = false;
}

// Create user account
svpn_error_t license_manager_create_user(const char *email, const char *password, 
                                        subscription_plan_t plan, user_account_t *user) {
    if (!g_license_manager.is_initialized || !email || !password || !user) {
        return SVPN_ERROR_INVALID_PARAM;
    }

    db_error_t result = db_user_create(g_license_manager.db_ctx, email, password, plan, user);
    
    switch (result) {
        case DB_SUCCESS:
            return SVPN_SUCCESS;
        case DB_ERROR_DUPLICATE:
            return SVPN_ERROR_AUTH_FAIL;
        case DB_ERROR_INVALID_PARAM:
            return SVPN_ERROR_INVALID_PARAM;
        default:
            return SVPN_ERROR_SYSTEM;
    }
}

// Authenticate user
svpn_error_t license_manager_authenticate_user(const char *email, const char *password, 
                                              user_account_t *user) {
    if (!g_license_manager.is_initialized || !email || !password || !user) {
        return SVPN_ERROR_INVALID_PARAM;
    }

    db_error_t result = db_user_authenticate(g_license_manager.db_ctx, email, password, user);
    
    switch (result) {
        case DB_SUCCESS:
            return SVPN_SUCCESS;
        case DB_ERROR_NOT_FOUND:
            return SVPN_ERROR_AUTH_FAIL;
        case DB_ERROR_INVALID_PARAM:
            return SVPN_ERROR_INVALID_PARAM;
        default:
            return SVPN_ERROR_SYSTEM;
    }
}

// Generate license for user
svpn_error_t license_manager_generate_license(uint64_t user_id, const char *device_name,
                                             time_t duration, char *license_key_out, 
                                             size_t license_key_size) {
    if (!g_license_manager.is_initialized || !device_name || !license_key_out) {
        return SVPN_ERROR_INVALID_PARAM;
    }

    // Get user information
    user_account_t user;
    db_error_t db_result = db_user_get_by_id(g_license_manager.db_ctx, user_id, &user);
    if (db_result != DB_SUCCESS) {
        return SVPN_ERROR_AUTH_FAIL;
    }

    // Check if user subscription is valid
    time_t now = time(NULL);
    if (now > user.subscription_expires && user.plan != PLAN_FREE) {
        return SVPN_ERROR_LICENSE_EXPIRED;
    }

    // Create license structure
    license_t license;
    time_t expires_at = (duration > 0) ? now + duration : user.subscription_expires;
    
    svpn_error_t result = license_generate(&license, g_license_manager.server_private_key, 
                                          user.email, expires_at - now);
    if (result != SVPN_SUCCESS) {
        return result;
    }

    // Encode license to base64 (simplified)
    // In production, use proper base64 encoding
    char encoded_license[1024];
    snprintf(encoded_license, sizeof(encoded_license), 
             "SVPN_%lu_%s_%ld_%ld", user_id, user.email, now, expires_at);

    // Store license in database
    license_record_t license_record;
    db_result = db_license_create(g_license_manager.db_ctx, user_id, encoded_license, 
                                 expires_at, device_name, &license_record);
    if (db_result != DB_SUCCESS) {
        return SVPN_ERROR_SYSTEM;
    }

    // Return license key
    strncpy(license_key_out, encoded_license, license_key_size - 1);
    license_key_out[license_key_size - 1] = '\0';

    return SVPN_SUCCESS;
}

// Validate license with database lookup
svpn_error_t license_manager_validate_license(const char *license_key, user_account_t *user,
                                             license_record_t *license) {
    if (!g_license_manager.is_initialized || !license_key || !user || !license) {
        return SVPN_ERROR_INVALID_PARAM;
    }

    db_error_t result = db_license_validate(g_license_manager.db_ctx, license_key, license, user);
    
    switch (result) {
        case DB_SUCCESS:
            return SVPN_SUCCESS;
        case DB_ERROR_NOT_FOUND:
            return SVPN_ERROR_LICENSE_INVALID;
        case DB_ERROR_INVALID_PARAM:
            return SVPN_ERROR_INVALID_PARAM;
        default:
            return SVPN_ERROR_SYSTEM;
    }
}

// Check if user can create new session
svpn_error_t license_manager_check_session_limit(uint64_t user_id, bool *can_connect) {
    if (!g_license_manager.is_initialized || !can_connect) {
        return SVPN_ERROR_INVALID_PARAM;
    }

    // Get user information
    user_account_t user;
    db_error_t db_result = db_user_get_by_id(g_license_manager.db_ctx, user_id, &user);
    if (db_result != DB_SUCCESS) {
        return SVPN_ERROR_AUTH_FAIL;
    }

    // Get active session count
    int active_sessions;
    db_result = db_session_get_active_count(g_license_manager.db_ctx, user_id, &active_sessions);
    if (db_result != DB_SUCCESS) {
        return SVPN_ERROR_SYSTEM;
    }

    *can_connect = (active_sessions < (int)user.max_connections);
    return SVPN_SUCCESS;
}

// Create new session
svpn_error_t license_manager_create_session(uint64_t user_id, const char *client_ip,
                                           session_record_t *session) {
    if (!g_license_manager.is_initialized || !client_ip || !session) {
        return SVPN_ERROR_INVALID_PARAM;
    }

    db_error_t result = db_session_create(g_license_manager.db_ctx, user_id, client_ip, session);
    
    switch (result) {
        case DB_SUCCESS:
            return SVPN_SUCCESS;
        case DB_ERROR_INVALID_PARAM:
            return SVPN_ERROR_INVALID_PARAM;
        default:
            return SVPN_ERROR_SYSTEM;
    }
}

// Update session activity
svpn_error_t license_manager_update_session(uint64_t session_id, uint64_t bytes_sent, 
                                           uint64_t bytes_received) {
    if (!g_license_manager.is_initialized) {
        return SVPN_ERROR_INVALID_PARAM;
    }

    db_error_t result = db_session_update_activity(g_license_manager.db_ctx, session_id, 
                                                  bytes_sent, bytes_received);
    
    return (result == DB_SUCCESS) ? SVPN_SUCCESS : SVPN_ERROR_SYSTEM;
}

// End session
svpn_error_t license_manager_end_session(uint64_t session_id) {
    if (!g_license_manager.is_initialized) {
        return SVPN_ERROR_INVALID_PARAM;
    }

    db_error_t result = db_session_end(g_license_manager.db_ctx, session_id);
    
    return (result == DB_SUCCESS) ? SVPN_SUCCESS : SVPN_ERROR_SYSTEM;
}
