# Multi-stage Dockerfile for Secure VPN production deployment
FROM ubuntu:22.04 AS builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    git \
    pkg-config \
    libsqlite3-dev \
    libcjson-dev \
    libcurl4-openssl-dev \
    libssl-dev \
    libgtk-3-dev \
    && rm -rf /var/lib/apt/lists/*

# Create build directory
WORKDIR /build

# Copy source code
COPY . .

# Build the application
RUN mkdir -p build && cd build && \
    cmake .. -DCMAKE_BUILD_TYPE=Release && \
    make -j$(nproc)

# Production stage
FROM ubuntu:22.04 AS production

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    libsqlite3-0 \
    libcjson1 \
    libcurl4 \
    libssl3 \
    ca-certificates \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r vpn && useradd -r -g vpn -s /bin/false vpn

# Create application directories
RUN mkdir -p /opt/securevpn/{bin,config,data,logs} && \
    chown -R vpn:vpn /opt/securevpn

# Copy built binaries
COPY --from=builder /build/build/svpn_enhanced_server /opt/securevpn/bin/
COPY --from=builder /build/build/svpn_web_api /opt/securevpn/bin/
COPY --from=builder /build/build/vpn_manager /opt/securevpn/bin/
COPY --from=builder /build/build/keygen /opt/securevpn/bin/

# Copy configuration templates
COPY deployment/config/ /opt/securevpn/config/

# Copy startup scripts
COPY deployment/scripts/ /opt/securevpn/scripts/
RUN chmod +x /opt/securevpn/scripts/*.sh

# Set working directory
WORKDIR /opt/securevpn

# Switch to non-root user
USER vpn

# Expose ports
EXPOSE 8443 8080 9090

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Default command
CMD ["/opt/securevpn/scripts/start.sh"]

# Development stage (for testing)
FROM production AS development

USER root

# Install development tools
RUN apt-get update && apt-get install -y \
    gdb \
    valgrind \
    strace \
    tcpdump \
    netcat \
    vim \
    && rm -rf /var/lib/apt/lists/*

# Copy test data
COPY tests/ /opt/securevpn/tests/

USER vpn

# Override command for development
CMD ["/opt/securevpn/scripts/dev-start.sh"]
