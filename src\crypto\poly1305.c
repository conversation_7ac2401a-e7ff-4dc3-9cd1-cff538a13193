#include "securevpn.h"
#include <string.h>

// Poly1305 implementation using 64-bit arithmetic

static void poly1305_blocks(uint32_t h[5], const uint8_t *m, size_t bytes, 
                           uint32_t hibit, const uint32_t r[5]) {
    uint32_t r0, r1, r2, r3, r4;
    uint32_t s1, s2, s3, s4;
    uint32_t h0, h1, h2, h3, h4;
    uint64_t d0, d1, d2, d3, d4;
    uint32_t c;
    
    r0 = r[0]; r1 = r[1]; r2 = r[2]; r3 = r[3]; r4 = r[4];
    
    s1 = r1 * 5; s2 = r2 * 5; s3 = r3 * 5; s4 = r4 * 5;
    
    h0 = h[0]; h1 = h[1]; h2 = h[2]; h3 = h[3]; h4 = h[4];
    
    while (bytes >= 16) {
        // h += m[i]
        h0 += (((uint32_t)m[0]) | ((uint32_t)m[1] << 8) | 
               ((uint32_t)m[2] << 16) | ((uint32_t)m[3] << 24));
        h1 += (((uint32_t)m[4]) | ((uint32_t)m[5] << 8) | 
               ((uint32_t)m[6] << 16) | ((uint32_t)m[7] << 24));
        h2 += (((uint32_t)m[8]) | ((uint32_t)m[9] << 8) | 
               ((uint32_t)m[10] << 16) | ((uint32_t)m[11] << 24));
        h3 += (((uint32_t)m[12]) | ((uint32_t)m[13] << 8) | 
               ((uint32_t)m[14] << 16) | ((uint32_t)m[15] << 24));
        h4 += hibit;
        
        // h *= r
        d0 = ((uint64_t)h0 * r0) + ((uint64_t)h1 * s4) + 
             ((uint64_t)h2 * s3) + ((uint64_t)h3 * s2) + ((uint64_t)h4 * s1);
        d1 = ((uint64_t)h0 * r1) + ((uint64_t)h1 * r0) + 
             ((uint64_t)h2 * s4) + ((uint64_t)h3 * s3) + ((uint64_t)h4 * s2);
        d2 = ((uint64_t)h0 * r2) + ((uint64_t)h1 * r1) + 
             ((uint64_t)h2 * r0) + ((uint64_t)h3 * s4) + ((uint64_t)h4 * s3);
        d3 = ((uint64_t)h0 * r3) + ((uint64_t)h1 * r2) + 
             ((uint64_t)h2 * r1) + ((uint64_t)h3 * r0) + ((uint64_t)h4 * s4);
        d4 = ((uint64_t)h0 * r4) + ((uint64_t)h1 * r3) + 
             ((uint64_t)h2 * r2) + ((uint64_t)h3 * r1) + ((uint64_t)h4 * r0);
        
        // (partial) h %= p
        c = (uint32_t)(d0 >> 26); h0 = (uint32_t)d0 & 0x3ffffff;
        d1 += c;      c = (uint32_t)(d1 >> 26); h1 = (uint32_t)d1 & 0x3ffffff;
        d2 += c;      c = (uint32_t)(d2 >> 26); h2 = (uint32_t)d2 & 0x3ffffff;
        d3 += c;      c = (uint32_t)(d3 >> 26); h3 = (uint32_t)d3 & 0x3ffffff;
        d4 += c;      c = (uint32_t)(d4 >> 26); h4 = (uint32_t)d4 & 0x3ffffff;
        h0 += c * 5;  c = h0 >> 26; h0 = h0 & 0x3ffffff;
        h1 += c;
        
        m += 16;
        bytes -= 16;
    }
    
    h[0] = h0; h[1] = h1; h[2] = h2; h[3] = h3; h[4] = h4;
}

svpn_error_t poly1305_auth(const uint8_t *key, const uint8_t *data, size_t len, 
                          uint8_t *tag) {
    if (!key || !data || !tag) {
        return SVPN_ERROR_INVALID_PARAM;
    }
    
    uint32_t r[5], h[5], pad[4];
    uint8_t block[16];
    size_t i;
    uint64_t f0, f1, f2, f3;
    uint32_t g0, g1, g2, g3, g4;
    uint32_t mask;
    
    // r &= 0xffffffc0ffffffc0ffffffc0fffffff
    r[0] = (((uint32_t)key[0]) | ((uint32_t)key[1] << 8) | 
            ((uint32_t)key[2] << 16) | ((uint32_t)key[3] << 24)) & 0x3ffffff;
    r[1] = (((uint32_t)key[3] >> 2) | ((uint32_t)key[4] << 6) | 
            ((uint32_t)key[5] << 14) | ((uint32_t)key[6] << 22)) & 0x3ffff03;
    r[2] = (((uint32_t)key[6] >> 4) | ((uint32_t)key[7] << 4) | 
            ((uint32_t)key[8] << 12) | ((uint32_t)key[9] << 20)) & 0x3ffc0ff;
    r[3] = (((uint32_t)key[9] >> 6) | ((uint32_t)key[10] << 2) | 
            ((uint32_t)key[11] << 10) | ((uint32_t)key[12] << 18)) & 0x3f03fff;
    r[4] = (((uint32_t)key[12] >> 8) | ((uint32_t)key[13] << 0) | 
            ((uint32_t)key[14] << 8) | ((uint32_t)key[15] << 16)) & 0x00fffff;
    
    // h = 0
    h[0] = h[1] = h[2] = h[3] = h[4] = 0;
    
    // Process full blocks
    poly1305_blocks(h, data, len & ~15, 1, r);
    
    // Process final block
    if (len & 15) {
        for (i = 0; i < (len & 15); i++) {
            block[i] = data[len - (len & 15) + i];
        }
        block[len & 15] = 1;
        for (i = (len & 15) + 1; i < 16; i++) {
            block[i] = 0;
        }
        poly1305_blocks(h, block, 16, 0, r);
    }
    
    // Fully carry h
    h[1] += h[0] >> 26; h[0] &= 0x3ffffff;
    h[2] += h[1] >> 26; h[1] &= 0x3ffffff;
    h[3] += h[2] >> 26; h[2] &= 0x3ffffff;
    h[4] += h[3] >> 26; h[3] &= 0x3ffffff;
    h[0] += 5 * (h[4] >> 26); h[4] &= 0x3ffffff;
    h[1] += h[0] >> 26; h[0] &= 0x3ffffff;
    
    // Compute h + -p
    g0 = h[0] + 5; g1 = h[1] + (g0 >> 26); g0 &= 0x3ffffff;
    g2 = h[2] + (g1 >> 26); g1 &= 0x3ffffff;
    g3 = h[3] + (g2 >> 26); g2 &= 0x3ffffff;
    g4 = h[4] + (g3 >> 26) - (1 << 26); g3 &= 0x3ffffff;
    
    // Select h if h < p, or h + -p if h >= p
    mask = (g4 >> 31) - 1;
    g0 &= mask; g1 &= mask; g2 &= mask; g3 &= mask; g4 &= mask;
    mask = ~mask;
    h[0] = (h[0] & mask) | g0; h[1] = (h[1] & mask) | g1;
    h[2] = (h[2] & mask) | g2; h[3] = (h[3] & mask) | g3;
    h[4] = (h[4] & mask) | g4;
    
    // h = h % (2^128)
    h[0] = ((h[0]) | (h[1] << 26)) & 0xffffffff;
    h[1] = ((h[1] >> 6) | (h[2] << 20)) & 0xffffffff;
    h[2] = ((h[2] >> 12) | (h[3] << 14)) & 0xffffffff;
    h[3] = ((h[3] >> 18) | (h[4] << 8)) & 0xffffffff;
    
    // mac = (h + pad) % (2^128)
    pad[0] = ((uint32_t)key[16]) | ((uint32_t)key[17] << 8) | 
             ((uint32_t)key[18] << 16) | ((uint32_t)key[19] << 24);
    pad[1] = ((uint32_t)key[20]) | ((uint32_t)key[21] << 8) | 
             ((uint32_t)key[22] << 16) | ((uint32_t)key[23] << 24);
    pad[2] = ((uint32_t)key[24]) | ((uint32_t)key[25] << 8) | 
             ((uint32_t)key[26] << 16) | ((uint32_t)key[27] << 24);
    pad[3] = ((uint32_t)key[28]) | ((uint32_t)key[29] << 8) | 
             ((uint32_t)key[30] << 16) | ((uint32_t)key[31] << 24);
    
    f0 = (uint64_t)h[0] + pad[0]; h[0] = (uint32_t)f0;
    f1 = (uint64_t)h[1] + pad[1] + (f0 >> 32); h[1] = (uint32_t)f1;
    f2 = (uint64_t)h[2] + pad[2] + (f1 >> 32); h[2] = (uint32_t)f2;
    f3 = (uint64_t)h[3] + pad[3] + (f2 >> 32); h[3] = (uint32_t)f3;
    
    // Output tag
    tag[0] = h[0]; tag[1] = h[0] >> 8; tag[2] = h[0] >> 16; tag[3] = h[0] >> 24;
    tag[4] = h[1]; tag[5] = h[1] >> 8; tag[6] = h[1] >> 16; tag[7] = h[1] >> 24;
    tag[8] = h[2]; tag[9] = h[2] >> 8; tag[10] = h[2] >> 16; tag[11] = h[2] >> 24;
    tag[12] = h[3]; tag[13] = h[3] >> 8; tag[14] = h[3] >> 16; tag[15] = h[3] >> 24;
    
    return SVPN_SUCCESS;
}

svpn_error_t poly1305_verify(const uint8_t *key, const uint8_t *data, size_t len, 
                            const uint8_t *tag) {
    if (!key || !data || !tag) {
        return SVPN_ERROR_INVALID_PARAM;
    }
    
    uint8_t computed_tag[POLY1305_TAG_SIZE];
    svpn_error_t result;
    int diff = 0;
    size_t i;
    
    result = poly1305_auth(key, data, len, computed_tag);
    if (result != SVPN_SUCCESS) {
        return result;
    }
    
    // Constant-time comparison
    for (i = 0; i < POLY1305_TAG_SIZE; i++) {
        diff |= computed_tag[i] ^ tag[i];
    }
    
    secure_zero(computed_tag, sizeof(computed_tag));
    
    return (diff == 0) ? SVPN_SUCCESS : SVPN_ERROR_CRYPTO_FAIL;
}
