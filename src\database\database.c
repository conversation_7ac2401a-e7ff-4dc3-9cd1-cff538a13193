#include "database.h"
#include "securevpn.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>

// SQL schema for creating tables
static const char *CREATE_USERS_TABLE = 
    "CREATE TABLE IF NOT EXISTS users ("
    "user_id INTEGER PRIMARY KEY AUTOINCREMENT,"
    "email TEXT UNIQUE NOT NULL,"
    "password_hash TEXT NOT NULL,"
    "salt TEXT NOT NULL,"
    "plan INTEGER NOT NULL DEFAULT 0,"
    "created_at INTEGER NOT NULL,"
    "subscription_expires INTEGER NOT NULL,"
    "is_active INTEGER NOT NULL DEFAULT 1,"
    "max_connections INTEGER NOT NULL DEFAULT 1,"
    "bytes_used_today INTEGER NOT NULL DEFAULT 0,"
    "bytes_limit_daily INTEGER NOT NULL DEFAULT **********"  // 1GB default
    ");";

static const char *CREATE_LICENSES_TABLE = 
    "CREATE TABLE IF NOT EXISTS licenses ("
    "license_id INTEGER PRIMARY KEY AUTOINCREMENT,"
    "user_id INTEGER NOT NULL,"
    "license_key TEXT UNIQUE NOT NULL,"
    "issued_at INTEGER NOT NULL,"
    "expires_at INTEGER NOT NULL,"
    "is_revoked INTEGER NOT NULL DEFAULT 0,"
    "device_name TEXT NOT NULL,"
    "FOREIGN KEY (user_id) REFERENCES users(user_id)"
    ");";

static const char *CREATE_SESSIONS_TABLE = 
    "CREATE TABLE IF NOT EXISTS sessions ("
    "session_id INTEGER PRIMARY KEY AUTOINCREMENT,"
    "user_id INTEGER NOT NULL,"
    "client_ip TEXT NOT NULL,"
    "connected_at INTEGER NOT NULL,"
    "last_activity INTEGER NOT NULL,"
    "bytes_sent INTEGER NOT NULL DEFAULT 0,"
    "bytes_received INTEGER NOT NULL DEFAULT 0,"
    "is_active INTEGER NOT NULL DEFAULT 1,"
    "FOREIGN KEY (user_id) REFERENCES users(user_id)"
    ");";

static const char *CREATE_PAYMENTS_TABLE = 
    "CREATE TABLE IF NOT EXISTS payments ("
    "payment_id INTEGER PRIMARY KEY AUTOINCREMENT,"
    "user_id INTEGER NOT NULL,"
    "transaction_id TEXT UNIQUE NOT NULL,"
    "plan INTEGER NOT NULL,"
    "amount REAL NOT NULL,"
    "currency TEXT NOT NULL,"
    "payment_date INTEGER NOT NULL,"
    "subscription_start INTEGER NOT NULL,"
    "subscription_end INTEGER NOT NULL,"
    "payment_method TEXT NOT NULL,"
    "is_successful INTEGER NOT NULL DEFAULT 1,"
    "FOREIGN KEY (user_id) REFERENCES users(user_id)"
    ");";

// Create indexes for better performance
static const char *CREATE_INDEXES[] = {
    "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);",
    "CREATE INDEX IF NOT EXISTS idx_licenses_user_id ON licenses(user_id);",
    "CREATE INDEX IF NOT EXISTS idx_licenses_key ON licenses(license_key);",
    "CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions(user_id);",
    "CREATE INDEX IF NOT EXISTS idx_sessions_active ON sessions(is_active);",
    "CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id);",
    NULL
};

db_error_t db_init(database_ctx_t *ctx, const char *db_path) {
    if (!ctx || !db_path) {
        return DB_ERROR_INVALID_PARAM;
    }

    memset(ctx, 0, sizeof(database_ctx_t));
    strncpy(ctx->db_path, db_path, sizeof(ctx->db_path) - 1);

    int rc = sqlite3_open(db_path, &ctx->db);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "Cannot open database: %s\n", sqlite3_errmsg(ctx->db));
        sqlite3_close(ctx->db);
        return DB_ERROR_INIT;
    }

    // Enable foreign key constraints
    sqlite3_exec(ctx->db, "PRAGMA foreign_keys = ON;", NULL, NULL, NULL);
    
    // Enable WAL mode for better concurrency
    sqlite3_exec(ctx->db, "PRAGMA journal_mode = WAL;", NULL, NULL, NULL);

    ctx->is_initialized = true;
    return DB_SUCCESS;
}

db_error_t db_close(database_ctx_t *ctx) {
    if (!ctx || !ctx->is_initialized) {
        return DB_ERROR_INVALID_PARAM;
    }

    if (ctx->db) {
        sqlite3_close(ctx->db);
        ctx->db = NULL;
    }

    ctx->is_initialized = false;
    return DB_SUCCESS;
}

db_error_t db_create_tables(database_ctx_t *ctx) {
    if (!ctx || !ctx->is_initialized) {
        return DB_ERROR_INVALID_PARAM;
    }

    char *err_msg = NULL;
    int rc;

    // Create tables
    const char *tables[] = {
        CREATE_USERS_TABLE,
        CREATE_LICENSES_TABLE,
        CREATE_SESSIONS_TABLE,
        CREATE_PAYMENTS_TABLE,
        NULL
    };

    for (int i = 0; tables[i] != NULL; i++) {
        rc = sqlite3_exec(ctx->db, tables[i], NULL, NULL, &err_msg);
        if (rc != SQLITE_OK) {
            fprintf(stderr, "SQL error creating table: %s\n", err_msg);
            sqlite3_free(err_msg);
            return DB_ERROR_QUERY;
        }
    }

    // Create indexes
    for (int i = 0; CREATE_INDEXES[i] != NULL; i++) {
        rc = sqlite3_exec(ctx->db, CREATE_INDEXES[i], NULL, NULL, &err_msg);
        if (rc != SQLITE_OK) {
            fprintf(stderr, "SQL error creating index: %s\n", err_msg);
            sqlite3_free(err_msg);
            return DB_ERROR_QUERY;
        }
    }

    return DB_SUCCESS;
}

// Utility function to hash password with salt
static void hash_password(const char *password, const char *salt, char *hash_out) {
    // Simple SHA-256 implementation (in production, use a proper crypto library)
    char combined[512];
    snprintf(combined, sizeof(combined), "%s%s", password, salt);
    
    // For now, use a simple hash (replace with proper SHA-256)
    uint32_t hash = 0;
    for (size_t i = 0; i < strlen(combined); i++) {
        hash = hash * 31 + combined[i];
    }
    snprintf(hash_out, 64, "%08x%08x%08x%08x", hash, hash ^ 0xAAAAAAAA, 
             hash ^ 0x55555555, hash ^ 0xFFFFFFFF);
}

// Generate random salt
static void generate_salt(char *salt_out, size_t len) {
    const char charset[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    for (size_t i = 0; i < len - 1; i++) {
        salt_out[i] = charset[rand() % (sizeof(charset) - 1)];
    }
    salt_out[len - 1] = '\0';
}

db_error_t db_user_create(database_ctx_t *ctx, const char *email, const char *password, 
                         subscription_plan_t plan, user_account_t *user) {
    if (!ctx || !ctx->is_initialized || !email || !password || !user) {
        return DB_ERROR_INVALID_PARAM;
    }

    sqlite3_stmt *stmt;
    const char *sql = "INSERT INTO users (email, password_hash, salt, plan, created_at, "
                     "subscription_expires, max_connections, bytes_limit_daily) "
                     "VALUES (?, ?, ?, ?, ?, ?, ?, ?);";

    int rc = sqlite3_prepare_v2(ctx->db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        return DB_ERROR_QUERY;
    }

    // Generate salt and hash password
    char salt[32];
    char password_hash[64];
    generate_salt(salt, sizeof(salt));
    hash_password(password, salt, password_hash);

    time_t now = time(NULL);
    time_t subscription_expires = now + (plan == PLAN_FREE ? 0 : 30 * 24 * 3600); // 30 days for paid plans

    sqlite3_bind_text(stmt, 1, email, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 2, password_hash, -1, SQLITE_STATIC);
    sqlite3_bind_text(stmt, 3, salt, -1, SQLITE_STATIC);
    sqlite3_bind_int(stmt, 4, plan);
    sqlite3_bind_int64(stmt, 5, now);
    sqlite3_bind_int64(stmt, 6, subscription_expires);
    sqlite3_bind_int(stmt, 7, db_plan_get_max_connections(plan));
    sqlite3_bind_int64(stmt, 8, db_plan_get_daily_limit(plan));

    rc = sqlite3_step(stmt);
    if (rc == SQLITE_CONSTRAINT) {
        sqlite3_finalize(stmt);
        return DB_ERROR_DUPLICATE;
    } else if (rc != SQLITE_DONE) {
        sqlite3_finalize(stmt);
        return DB_ERROR_QUERY;
    }

    // Fill user structure
    memset(user, 0, sizeof(user_account_t));
    user->user_id = sqlite3_last_insert_rowid(ctx->db);
    strncpy(user->email, email, sizeof(user->email) - 1);
    strncpy(user->password_hash, password_hash, sizeof(user->password_hash) - 1);
    strncpy(user->salt, salt, sizeof(user->salt) - 1);
    user->plan = plan;
    user->created_at = now;
    user->subscription_expires = subscription_expires;
    user->is_active = true;
    user->max_connections = db_plan_get_max_connections(plan);
    user->bytes_used_today = 0;
    user->bytes_limit_daily = db_plan_get_daily_limit(plan);

    sqlite3_finalize(stmt);
    return DB_SUCCESS;
}

db_error_t db_user_authenticate(database_ctx_t *ctx, const char *email, const char *password,
                               user_account_t *user) {
    if (!ctx || !ctx->is_initialized || !email || !password || !user) {
        return DB_ERROR_INVALID_PARAM;
    }

    sqlite3_stmt *stmt;
    const char *sql = "SELECT user_id, email, password_hash, salt, plan, created_at, "
                     "subscription_expires, is_active, max_connections, bytes_used_today, "
                     "bytes_limit_daily FROM users WHERE email = ? AND is_active = 1;";

    int rc = sqlite3_prepare_v2(ctx->db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        return DB_ERROR_QUERY;
    }

    sqlite3_bind_text(stmt, 1, email, -1, SQLITE_STATIC);

    rc = sqlite3_step(stmt);
    if (rc != SQLITE_ROW) {
        sqlite3_finalize(stmt);
        return DB_ERROR_NOT_FOUND;
    }

    // Get stored salt and hash
    const char *stored_hash = (const char*)sqlite3_column_text(stmt, 2);
    const char *salt = (const char*)sqlite3_column_text(stmt, 3);

    // Hash provided password with stored salt
    char computed_hash[64];
    hash_password(password, salt, computed_hash);

    // Verify password
    if (strcmp(stored_hash, computed_hash) != 0) {
        sqlite3_finalize(stmt);
        return DB_ERROR_NOT_FOUND;
    }

    // Fill user structure
    memset(user, 0, sizeof(user_account_t));
    user->user_id = sqlite3_column_int64(stmt, 0);
    strncpy(user->email, (const char*)sqlite3_column_text(stmt, 1), sizeof(user->email) - 1);
    strncpy(user->password_hash, stored_hash, sizeof(user->password_hash) - 1);
    strncpy(user->salt, salt, sizeof(user->salt) - 1);
    user->plan = sqlite3_column_int(stmt, 4);
    user->created_at = sqlite3_column_int64(stmt, 5);
    user->subscription_expires = sqlite3_column_int64(stmt, 6);
    user->is_active = sqlite3_column_int(stmt, 7);
    user->max_connections = sqlite3_column_int(stmt, 8);
    user->bytes_used_today = sqlite3_column_int64(stmt, 9);
    user->bytes_limit_daily = sqlite3_column_int64(stmt, 10);

    sqlite3_finalize(stmt);
    return DB_SUCCESS;
}

db_error_t db_user_get_by_id(database_ctx_t *ctx, uint64_t user_id, user_account_t *user) {
    if (!ctx || !ctx->is_initialized || !user) {
        return DB_ERROR_INVALID_PARAM;
    }

    sqlite3_stmt *stmt;
    const char *sql = "SELECT user_id, email, password_hash, salt, plan, created_at, "
                     "subscription_expires, is_active, max_connections, bytes_used_today, "
                     "bytes_limit_daily FROM users WHERE user_id = ?;";

    int rc = sqlite3_prepare_v2(ctx->db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        return DB_ERROR_QUERY;
    }

    sqlite3_bind_int64(stmt, 1, user_id);

    rc = sqlite3_step(stmt);
    if (rc != SQLITE_ROW) {
        sqlite3_finalize(stmt);
        return DB_ERROR_NOT_FOUND;
    }

    // Fill user structure
    memset(user, 0, sizeof(user_account_t));
    user->user_id = sqlite3_column_int64(stmt, 0);
    strncpy(user->email, (const char*)sqlite3_column_text(stmt, 1), sizeof(user->email) - 1);
    strncpy(user->password_hash, (const char*)sqlite3_column_text(stmt, 2), sizeof(user->password_hash) - 1);
    strncpy(user->salt, (const char*)sqlite3_column_text(stmt, 3), sizeof(user->salt) - 1);
    user->plan = sqlite3_column_int(stmt, 4);
    user->created_at = sqlite3_column_int64(stmt, 5);
    user->subscription_expires = sqlite3_column_int64(stmt, 6);
    user->is_active = sqlite3_column_int(stmt, 7);
    user->max_connections = sqlite3_column_int(stmt, 8);
    user->bytes_used_today = sqlite3_column_int64(stmt, 9);
    user->bytes_limit_daily = sqlite3_column_int64(stmt, 10);

    sqlite3_finalize(stmt);
    return DB_SUCCESS;
}

db_error_t db_license_create(database_ctx_t *ctx, uint64_t user_id, const char *license_key,
                            time_t expires_at, const char *device_name, license_record_t *license) {
    if (!ctx || !ctx->is_initialized || !license_key || !device_name || !license) {
        return DB_ERROR_INVALID_PARAM;
    }

    sqlite3_stmt *stmt;
    const char *sql = "INSERT INTO licenses (user_id, license_key, issued_at, expires_at, device_name) "
                     "VALUES (?, ?, ?, ?, ?);";

    int rc = sqlite3_prepare_v2(ctx->db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        return DB_ERROR_QUERY;
    }

    time_t now = time(NULL);

    sqlite3_bind_int64(stmt, 1, user_id);
    sqlite3_bind_text(stmt, 2, license_key, -1, SQLITE_STATIC);
    sqlite3_bind_int64(stmt, 3, now);
    sqlite3_bind_int64(stmt, 4, expires_at);
    sqlite3_bind_text(stmt, 5, device_name, -1, SQLITE_STATIC);

    rc = sqlite3_step(stmt);
    if (rc == SQLITE_CONSTRAINT) {
        sqlite3_finalize(stmt);
        return DB_ERROR_DUPLICATE;
    } else if (rc != SQLITE_DONE) {
        sqlite3_finalize(stmt);
        return DB_ERROR_QUERY;
    }

    // Fill license structure
    memset(license, 0, sizeof(license_record_t));
    license->license_id = sqlite3_last_insert_rowid(ctx->db);
    license->user_id = user_id;
    strncpy(license->license_key, license_key, sizeof(license->license_key) - 1);
    license->issued_at = now;
    license->expires_at = expires_at;
    license->is_revoked = false;
    strncpy(license->device_name, device_name, sizeof(license->device_name) - 1);

    sqlite3_finalize(stmt);
    return DB_SUCCESS;
}
