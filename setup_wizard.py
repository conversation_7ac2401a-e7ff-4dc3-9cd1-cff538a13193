#!/usr/bin/env python3
"""
VPN-in-a-Box Setup Wizard
One-click deployment for your personal VPN
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import subprocess
import threading
import json
import os
from pathlib import Path

class SetupWizard:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("VPN-in-a-Box Setup Wizard")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        self.current_step = 0
        self.setup_data = {}
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the wizard interface"""
        # Header
        header = tk.Frame(self.root, bg="#2c3e50", height=80)
        header.pack(fill=tk.X)
        header.pack_propagate(False)
        
        title = tk.Label(header, text="🔒 VPN-in-a-Box Setup", 
                        font=("Arial", 18, "bold"), fg="white", bg="#2c3e50")
        title.pack(expand=True)
        
        # Progress bar
        self.progress = ttk.Progressbar(self.root, length=580, mode='determinate')
        self.progress.pack(pady=10)
        
        # Content area
        self.content_frame = tk.Frame(self.root)
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Navigation buttons
        nav_frame = tk.Frame(self.root)
        nav_frame.pack(fill=tk.X, padx=20, pady=10)
        
        self.back_btn = tk.Button(nav_frame, text="Back", command=self.previous_step)
        self.back_btn.pack(side=tk.LEFT)
        
        self.next_btn = tk.Button(nav_frame, text="Next", command=self.next_step,
                                 bg="#3498db", fg="white", font=("Arial", 10, "bold"))
        self.next_btn.pack(side=tk.RIGHT)
        
        # Start with welcome step
        self.show_welcome()
    
    def clear_content(self):
        """Clear content frame"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def show_welcome(self):
        """Show welcome screen"""
        self.clear_content()
        self.progress['value'] = 0
        
        welcome_text = """
Welcome to VPN-in-a-Box Setup!

This wizard will help you set up your personal VPN server in just a few minutes.

What you'll get:
✅ Your own VPN server (no third-party trust required)
✅ Professional client apps for all devices
✅ Enterprise-grade AES-256 encryption
✅ Complete privacy and control
✅ Zero monthly fees

Let's get started!
        """
        
        label = tk.Label(self.content_frame, text=welcome_text, 
                        font=("Arial", 12), justify=tk.LEFT)
        label.pack(pady=50)
        
        self.back_btn.config(state=tk.DISABLED)
        self.next_btn.config(text="Get Started")
    
    def show_deployment_choice(self):
        """Show deployment options"""
        self.clear_content()
        self.progress['value'] = 20
        
        title = tk.Label(self.content_frame, text="Choose Your Deployment", 
                        font=("Arial", 16, "bold"))
        title.pack(pady=20)
        
        self.deployment_var = tk.StringVar(value="local")
        
        # Local deployment
        local_frame = tk.Frame(self.content_frame, relief=tk.RAISED, bd=1)
        local_frame.pack(fill=tk.X, pady=10)
        
        tk.Radiobutton(local_frame, text="Local Server (This PC)", 
                      variable=self.deployment_var, value="local",
                      font=("Arial", 12, "bold")).pack(anchor=tk.W, padx=10, pady=5)
        
        tk.Label(local_frame, text="• Run VPN server on this computer\n• Free forever\n• Good for personal/family use",
                font=("Arial", 10), fg="#666").pack(anchor=tk.W, padx=30, pady=(0, 10))
        
        # Cloud deployment
        cloud_frame = tk.Frame(self.content_frame, relief=tk.RAISED, bd=1)
        cloud_frame.pack(fill=tk.X, pady=10)
        
        tk.Radiobutton(cloud_frame, text="Cloud Server (Recommended)", 
                      variable=self.deployment_var, value="cloud",
                      font=("Arial", 12, "bold")).pack(anchor=tk.W, padx=10, pady=5)
        
        tk.Label(cloud_frame, text="• Deploy to AWS/DigitalOcean/Vultr\n• Always online, better performance\n• ~$5-10/month hosting cost",
                font=("Arial", 10), fg="#666").pack(anchor=tk.W, padx=30, pady=(0, 10))
        
        self.back_btn.config(state=tk.NORMAL)
        self.next_btn.config(text="Next")
    
    def show_server_config(self):
        """Show server configuration"""
        self.clear_content()
        self.progress['value'] = 40
        
        title = tk.Label(self.content_frame, text="Server Configuration", 
                        font=("Arial", 16, "bold"))
        title.pack(pady=20)
        
        # Server name
        tk.Label(self.content_frame, text="Server Name:", font=("Arial", 12)).pack(anchor=tk.W)
        self.server_name = tk.Entry(self.content_frame, font=("Arial", 12), width=40)
        self.server_name.insert(0, "My-VPN-Server")
        self.server_name.pack(pady=(5, 15))
        
        # Admin email
        tk.Label(self.content_frame, text="Admin Email:", font=("Arial", 12)).pack(anchor=tk.W)
        self.admin_email = tk.Entry(self.content_frame, font=("Arial", 12), width=40)
        self.admin_email.insert(0, "<EMAIL>")
        self.admin_email.pack(pady=(5, 15))
        
        # Max users
        tk.Label(self.content_frame, text="Maximum Users:", font=("Arial", 12)).pack(anchor=tk.W)
        self.max_users = tk.Spinbox(self.content_frame, from_=1, to=1000, value=10, 
                                   font=("Arial", 12), width=10)
        self.max_users.pack(anchor=tk.W, pady=(5, 15))
        
        # Port
        tk.Label(self.content_frame, text="VPN Port:", font=("Arial", 12)).pack(anchor=tk.W)
        self.vpn_port = tk.Entry(self.content_frame, font=("Arial", 12), width=10)
        self.vpn_port.insert(0, "8443")
        self.vpn_port.pack(anchor=tk.W, pady=(5, 15))
    
    def show_installation(self):
        """Show installation progress"""
        self.clear_content()
        self.progress['value'] = 60
        
        title = tk.Label(self.content_frame, text="Installing Your VPN Server", 
                        font=("Arial", 16, "bold"))
        title.pack(pady=20)
        
        self.install_text = tk.Text(self.content_frame, height=15, width=70, 
                                   font=("Consolas", 10), bg="#1e1e1e", fg="#00ff00")
        self.install_text.pack(pady=20)
        
        scrollbar = tk.Scrollbar(self.content_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.install_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.install_text.yview)
        
        self.next_btn.config(state=tk.DISABLED, text="Installing...")
        
        # Start installation in background
        threading.Thread(target=self.run_installation, daemon=True).start()
    
    def run_installation(self):
        """Run the actual installation"""
        steps = [
            "🔧 Setting up directories...",
            "🔐 Generating certificates...",
            "📋 Creating server configuration...",
            "🔥 Configuring firewall...",
            "🚀 Starting VPN server...",
            "👤 Creating admin user...",
            "📱 Generating client profiles...",
            "✅ Installation complete!"
        ]
        
        for i, step in enumerate(steps):
            self.install_text.insert(tk.END, f"{step}\n")
            self.install_text.see(tk.END)
            self.root.update()
            
            # Simulate installation time
            import time
            time.sleep(2)
            
            # Update progress
            self.progress['value'] = 60 + (i + 1) * 5
        
        self.next_btn.config(state=tk.NORMAL, text="Finish")
    
    def show_completion(self):
        """Show completion screen"""
        self.clear_content()
        self.progress['value'] = 100
        
        title = tk.Label(self.content_frame, text="🎉 Setup Complete!", 
                        font=("Arial", 18, "bold"), fg="#27ae60")
        title.pack(pady=20)
        
        success_text = """
Your VPN server is now running and ready to use!

What's ready:
✅ VPN Server running on port 8443
✅ Admin dashboard at http://localhost:8090
✅ Client profiles generated
✅ Professional client app ready

Next steps:
1. Launch the VPN client app
2. Connect using your credentials
3. Share client profiles with family/team
4. Monitor usage via the dashboard

Your VPN credentials:
Username: <EMAIL>
Password: admin123
        """
        
        label = tk.Label(self.content_frame, text=success_text, 
                        font=("Arial", 11), justify=tk.LEFT)
        label.pack(pady=20)
        
        # Action buttons
        btn_frame = tk.Frame(self.content_frame)
        btn_frame.pack(pady=20)
        
        tk.Button(btn_frame, text="Launch VPN Client", 
                 command=self.launch_client, bg="#3498db", fg="white",
                 font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=10)
        
        tk.Button(btn_frame, text="Open Dashboard", 
                 command=self.open_dashboard, bg="#e74c3c", fg="white",
                 font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=10)
        
        self.next_btn.config(text="Finish", command=self.finish_setup)
    
    def launch_client(self):
        """Launch the VPN client"""
        try:
            subprocess.Popen([sys.executable, "professional_vpn_client.py"])
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch client: {e}")
    
    def open_dashboard(self):
        """Open the web dashboard"""
        import webbrowser
        webbrowser.open("http://localhost:8090")
    
    def finish_setup(self):
        """Finish the setup"""
        self.root.destroy()
    
    def next_step(self):
        """Go to next step"""
        if self.current_step == 0:
            self.show_deployment_choice()
        elif self.current_step == 1:
            self.setup_data['deployment'] = self.deployment_var.get()
            self.show_server_config()
        elif self.current_step == 2:
            self.setup_data.update({
                'server_name': self.server_name.get(),
                'admin_email': self.admin_email.get(),
                'max_users': self.max_users.get(),
                'vpn_port': self.vpn_port.get()
            })
            self.show_installation()
        elif self.current_step == 3:
            self.show_completion()
        
        self.current_step += 1
    
    def previous_step(self):
        """Go to previous step"""
        if self.current_step > 0:
            self.current_step -= 1
            
            if self.current_step == 0:
                self.show_welcome()
            elif self.current_step == 1:
                self.show_deployment_choice()
            elif self.current_step == 2:
                self.show_server_config()
    
    def run(self):
        """Run the wizard"""
        self.root.mainloop()

if __name__ == "__main__":
    wizard = SetupWizard()
    wizard.run()
