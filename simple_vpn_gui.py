#!/usr/bin/env python3
"""
Simple VPN GUI - Easy to use interface for your VPN
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import threading
import socket

class SimpleVPNGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Your VPN - Simple Connect")
        self.root.geometry("400x300")
        
        self.connected = False
        self.vpn_process = None
        
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the user interface"""
        # Title
        title = tk.Label(self.root, text="🔒 Your VPN", font=("Arial", 16, "bold"))
        title.pack(pady=20)
        
        # Status
        self.status_label = tk.Label(self.root, text="Status: Disconnected", font=("Arial", 12))
        self.status_label.pack(pady=10)
        
        # Server info
        server_frame = tk.Frame(self.root)
        server_frame.pack(pady=10)
        
        tk.Label(server_frame, text="Server:").pack(side=tk.LEFT)
        tk.Label(server_frame, text="192.168.10.22:8443", font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=10)
        
        # Connect button
        self.connect_btn = tk.Button(
            self.root, 
            text="Connect to VPN", 
            command=self.toggle_connection,
            font=("Arial", 12),
            bg="#4CAF50",
            fg="white",
            width=15,
            height=2
        )
        self.connect_btn.pack(pady=20)
        
        # Info
        info_text = """Your VPN is ready to use!
        
✅ Secure AES-256 encryption
✅ Hide your IP address  
✅ Bypass geo-restrictions
✅ Protect your privacy"""
        
        info_label = tk.Label(self.root, text=info_text, justify=tk.LEFT)
        info_label.pack(pady=20)
    
    def toggle_connection(self):
        """Connect or disconnect VPN"""
        if not self.connected:
            self.connect_vpn()
        else:
            self.disconnect_vpn()
    
    def connect_vpn(self):
        """Connect to VPN"""
        try:
            # Start VPN client in background
            self.vpn_process = subprocess.Popen([
                "python", "vpn_client.py"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # Update UI
            self.connected = True
            self.status_label.config(text="Status: Connected ✅", fg="green")
            self.connect_btn.config(text="Disconnect", bg="#f44336")
            
            messagebox.showinfo("VPN", "Connected to VPN successfully!")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to connect: {e}")
    
    def disconnect_vpn(self):
        """Disconnect from VPN"""
        try:
            if self.vpn_process:
                self.vpn_process.terminate()
                self.vpn_process = None
            
            # Update UI
            self.connected = False
            self.status_label.config(text="Status: Disconnected", fg="red")
            self.connect_btn.config(text="Connect to VPN", bg="#4CAF50")
            
            messagebox.showinfo("VPN", "Disconnected from VPN")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to disconnect: {e}")
    
    def run(self):
        """Run the GUI"""
        self.root.mainloop()

if __name__ == "__main__":
    app = SimpleVPNGUI()
    app.run()
