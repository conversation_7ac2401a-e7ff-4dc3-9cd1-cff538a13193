#!/usr/bin/env python3
"""
VPN Manager Tool - Commercial VPN Management
Manage users, licenses, and view analytics for the VPN service
"""

import sqlite3
import hashlib
import secrets
import time
import json
import argparse
from datetime import datetime, timedelta

class VPNManager:
    def __init__(self, db_file="vpn_production.db"):
        self.db_file = db_file
    
    def create_user(self, email, password, plan='free'):
        """Create a new user account"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # Check if user already exists
        cursor.execute('SELECT user_id FROM users WHERE email = ?', (email,))
        if cursor.fetchone():
            print(f"❌ User {email} already exists")
            conn.close()
            return False
        
        # Generate password hash
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000).hex()
        
        # Set plan limits
        plan_limits = {
            'free': {'max_conn': 1, 'bytes_limit': 1024*1024*1024},  # 1GB
            'basic': {'max_conn': 3, 'bytes_limit': 10*1024*1024*1024},  # 10GB
            'premium': {'max_conn': 5, 'bytes_limit': 100*1024*1024*1024},  # 100GB
            'enterprise': {'max_conn': 10, 'bytes_limit': 0}  # Unlimited
        }
        
        limits = plan_limits.get(plan, plan_limits['free'])
        
        # Insert user
        cursor.execute('''
            INSERT INTO users (email, password_hash, salt, plan, max_connections, bytes_limit_daily)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (email, password_hash, salt, plan, limits['max_conn'], limits['bytes_limit']))
        
        user_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        print(f"✅ Created user: {email}")
        print(f"   User ID: {user_id}")
        print(f"   Plan: {plan}")
        print(f"   Max Connections: {limits['max_conn']}")
        print(f"   Daily Limit: {limits['bytes_limit']} bytes")
        
        return user_id
    
    def generate_license(self, user_id, device_name, duration_days=30):
        """Generate a license for a user"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        # Check if user exists
        cursor.execute('SELECT email, plan FROM users WHERE user_id = ?', (user_id,))
        user_info = cursor.fetchone()
        
        if not user_info:
            print(f"❌ User ID {user_id} not found")
            conn.close()
            return None
        
        email, plan = user_info
        
        # Generate license key
        timestamp = int(time.time())
        license_key = f"SVPN_{user_id}_{device_name.replace(' ', '_')}_{timestamp}_{secrets.token_hex(8)}"
        
        # Calculate expiration
        expires_at = timestamp + (duration_days * 24 * 3600)
        
        # Insert license
        cursor.execute('''
            INSERT INTO licenses (user_id, license_key, device_name, expires_at)
            VALUES (?, ?, ?, ?)
        ''', (user_id, license_key, device_name, expires_at))
        
        license_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        print(f"🔑 Generated license for {email}")
        print(f"   License ID: {license_id}")
        print(f"   Device: {device_name}")
        print(f"   Duration: {duration_days} days")
        print(f"   Expires: {datetime.fromtimestamp(expires_at)}")
        print(f"   License Key: {license_key}")
        
        # Save to file
        filename = f"license_{user_id}_{device_name.replace(' ', '_')}.txt"
        with open(filename, 'w') as f:
            f.write(f"Secure VPN License\n")
            f.write(f"==================\n")
            f.write(f"User: {email}\n")
            f.write(f"Device: {device_name}\n")
            f.write(f"Plan: {plan}\n")
            f.write(f"Expires: {datetime.fromtimestamp(expires_at)}\n")
            f.write(f"License Key: {license_key}\n")
        
        print(f"   Saved to: {filename}")
        
        return license_key
    
    def list_users(self):
        """List all users"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT user_id, email, plan, created_at, is_active, max_connections
            FROM users ORDER BY created_at DESC
        ''')
        
        users = cursor.fetchall()
        conn.close()
        
        if not users:
            print("📋 No users found")
            return
        
        print("👥 User List")
        print("=" * 80)
        print(f"{'ID':<5} {'Email':<30} {'Plan':<12} {'Connections':<11} {'Created':<20} {'Status'}")
        print("-" * 80)
        
        for user in users:
            user_id, email, plan, created_at, is_active, max_conn = user
            created_date = datetime.fromtimestamp(created_at).strftime('%Y-%m-%d %H:%M')
            status = "Active" if is_active else "Inactive"
            
            print(f"{user_id:<5} {email:<30} {plan:<12} {max_conn:<11} {created_date:<20} {status}")
    
    def view_analytics(self, user_id=None):
        """View analytics for user or overall"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        if user_id:
            # User-specific analytics
            cursor.execute('SELECT email, plan FROM users WHERE user_id = ?', (user_id,))
            user_info = cursor.fetchone()
            
            if not user_info:
                print(f"❌ User ID {user_id} not found")
                conn.close()
                return
            
            email, plan = user_info
            
            # Get session stats
            cursor.execute('''
                SELECT COUNT(*), SUM(bytes_sent), SUM(bytes_received), 
                       AVG(last_activity - connected_at) as avg_duration
                FROM sessions WHERE user_id = ?
            ''', (user_id,))
            
            session_stats = cursor.fetchone()
            session_count, bytes_sent, bytes_received, avg_duration = session_stats
            
            # Get active licenses
            cursor.execute('''
                SELECT COUNT(*) FROM licenses 
                WHERE user_id = ? AND expires_at > ? AND is_revoked = 0
            ''', (user_id, int(time.time())))
            
            active_licenses = cursor.fetchone()[0]
            
            print(f"📊 Analytics for {email}")
            print("=" * 50)
            print(f"Plan: {plan}")
            print(f"Total Sessions: {session_count or 0}")
            print(f"Data Sent: {(bytes_sent or 0) / (1024*1024):.2f} MB")
            print(f"Data Received: {(bytes_received or 0) / (1024*1024):.2f} MB")
            print(f"Average Session Duration: {(avg_duration or 0) / 60:.1f} minutes")
            print(f"Active Licenses: {active_licenses}")
            
        else:
            # Overall analytics
            cursor.execute('SELECT COUNT(*) FROM users WHERE is_active = 1')
            total_users = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM sessions WHERE is_active = 1')
            active_sessions = cursor.fetchone()[0]
            
            cursor.execute('SELECT SUM(bytes_sent), SUM(bytes_received) FROM sessions')
            total_traffic = cursor.fetchone()
            total_sent, total_received = total_traffic
            
            cursor.execute('SELECT plan, COUNT(*) FROM users WHERE is_active = 1 GROUP BY plan')
            plan_distribution = cursor.fetchall()
            
            print("📊 Overall Analytics")
            print("=" * 40)
            print(f"Total Users: {total_users}")
            print(f"Active Sessions: {active_sessions}")
            print(f"Total Data Sent: {(total_sent or 0) / (1024*1024*1024):.2f} GB")
            print(f"Total Data Received: {(total_received or 0) / (1024*1024*1024):.2f} GB")
            
            print("\nPlan Distribution:")
            for plan, count in plan_distribution:
                print(f"  {plan}: {count} users")
        
        conn.close()
    
    def revoke_license(self, license_key):
        """Revoke a license"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        cursor.execute('UPDATE licenses SET is_revoked = 1 WHERE license_key = ?', (license_key,))
        
        if cursor.rowcount > 0:
            conn.commit()
            print(f"✅ License revoked: {license_key}")
        else:
            print(f"❌ License not found: {license_key}")
        
        conn.close()
    
    def cleanup_expired(self):
        """Clean up expired licenses and inactive sessions"""
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()
        
        current_time = int(time.time())
        
        # Mark expired licenses as revoked
        cursor.execute('''
            UPDATE licenses SET is_revoked = 1 
            WHERE expires_at < ? AND is_revoked = 0
        ''', (current_time,))
        
        expired_licenses = cursor.rowcount
        
        # Clean up old inactive sessions (older than 7 days)
        week_ago = current_time - (7 * 24 * 3600)
        cursor.execute('''
            DELETE FROM sessions 
            WHERE is_active = 0 AND last_activity < ?
        ''', (week_ago,))
        
        cleaned_sessions = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        print(f"🧹 Cleanup completed:")
        print(f"   Expired licenses: {expired_licenses}")
        print(f"   Old sessions removed: {cleaned_sessions}")

    def generate_vpn_profile(self, email, profile_type='both', server_endpoint='your-vpn-server.com'):
        """Generate VPN profiles for user"""
        # Check if user exists
        conn = sqlite3.connect(self.db_file)
        cursor = conn.cursor()

        cursor.execute('SELECT user_id, plan FROM users WHERE email = ? AND is_active = 1', (email,))
        user_info = cursor.fetchone()
        conn.close()

        if not user_info:
            print(f"❌ User {email} not found or inactive")
            return

        user_id, plan = user_info
        print(f"📱 Generating VPN profiles for {email} ({plan} plan)")

        profiles_created = []

        if profile_type in ['openvpn', 'both']:
            try:
                from openvpn_integration import OpenVPNManager
                ovpn_manager = OpenVPNManager()

                # Setup server if needed
                if not (ovpn_manager.ca_dir / "ca.crt").exists():
                    print("🔐 Setting up OpenVPN server...")
                    ovpn_manager.setup_complete_server(server_endpoint)

                config_file, instructions = ovpn_manager.create_user_profile(email, server_endpoint)
                profiles_created.append(('OpenVPN', config_file, instructions))

            except Exception as e:
                print(f"❌ OpenVPN profile generation failed: {e}")

        if profile_type in ['wireguard', 'both']:
            try:
                from wireguard_integration import WireGuardManager
                wg_manager = WireGuardManager()

                # Setup server if needed
                if not (wg_manager.server_dir / "wg0.conf").exists():
                    print("⚡ Setting up WireGuard server...")
                    wg_manager.setup_complete_server(server_endpoint)

                result = wg_manager.create_user_profile(email, server_endpoint)
                if result:
                    config_file, qr_file, instructions = result
                    profiles_created.append(('WireGuard', config_file, instructions))

            except Exception as e:
                print(f"❌ WireGuard profile generation failed: {e}")

        if profiles_created:
            print(f"\n✅ VPN profiles created for {email}:")
            for profile_type, config_file, instructions in profiles_created:
                print(f"   {profile_type}: {config_file}")
                print(f"   Instructions: {instructions}")

            print(f"\n📧 Send these files to {email} for VPN setup")
        else:
            print(f"❌ No profiles could be generated for {email}")

def main():
    parser = argparse.ArgumentParser(description='VPN Manager Tool')
    parser.add_argument('--db', default='vpn_production.db', help='Database file')
    
    subparsers = parser.add_subparsers(dest='command', help='Commands')
    
    # Create user command
    create_parser = subparsers.add_parser('create-user', help='Create new user')
    create_parser.add_argument('email', help='User email')
    create_parser.add_argument('password', help='User password')
    create_parser.add_argument('plan', choices=['free', 'basic', 'premium', 'enterprise'], help='Subscription plan')
    
    # Generate license command
    license_parser = subparsers.add_parser('generate-license', help='Generate license')
    license_parser.add_argument('user_id', type=int, help='User ID')
    license_parser.add_argument('device_name', help='Device name')
    license_parser.add_argument('--duration', type=int, default=30, help='Duration in days')
    
    # List users command
    subparsers.add_parser('list-users', help='List all users')
    
    # Analytics command
    analytics_parser = subparsers.add_parser('analytics', help='View analytics')
    analytics_parser.add_argument('--user-id', type=int, help='Specific user ID')
    
    # Revoke license command
    revoke_parser = subparsers.add_parser('revoke-license', help='Revoke license')
    revoke_parser.add_argument('license_key', help='License key to revoke')
    
    # Cleanup command
    subparsers.add_parser('cleanup', help='Clean up expired data')

    # Generate VPN profile command
    profile_parser = subparsers.add_parser('generate-profile', help='Generate VPN profile')
    profile_parser.add_argument('email', help='User email')
    profile_parser.add_argument('--type', choices=['openvpn', 'wireguard', 'both'], default='both', help='Profile type')
    profile_parser.add_argument('--server', default='your-vpn-server.com', help='Server endpoint')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    manager = VPNManager(args.db)
    
    if args.command == 'create-user':
        manager.create_user(args.email, args.password, args.plan)
    elif args.command == 'generate-license':
        manager.generate_license(args.user_id, args.device_name, args.duration)
    elif args.command == 'list-users':
        manager.list_users()
    elif args.command == 'analytics':
        manager.view_analytics(args.user_id)
    elif args.command == 'revoke-license':
        manager.revoke_license(args.license_key)
    elif args.command == 'cleanup':
        manager.cleanup_expired()
    elif args.command == 'generate-profile':
        manager.generate_vpn_profile(args.email, args.type, args.server)

if __name__ == "__main__":
    main()
