#ifndef SECUREVPN_H
#define SECUREVPN_H

#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <time.h>

// Include database functionality
#include "../src/database/database.h"

// Version information
#define SVPN_VERSION_MAJOR 1
#define SVPN_VERSION_MINOR 0
#define SVPN_VERSION_PATCH 0

// Cryptographic constants
#define CHACHA20_KEY_SIZE 32
#define CHACHA20_NONCE_SIZE 12
#define POLY1305_TAG_SIZE 16
#define X25519_KEY_SIZE 32
#define RSA_KEY_SIZE 512  // 4096 bits

// Network constants
#define MAX_PACKET_SIZE 1500
#define VPN_HEADER_SIZE 32
#define MAX_PAYLOAD_SIZE (MAX_PACKET_SIZE - VPN_HEADER_SIZE)

// License constants
#define LICENSE_KEY_SIZE 256
#define LICENSE_SIGNATURE_SIZE RSA_KEY_SIZE

// Error codes
typedef enum {
    SVPN_SUCCESS = 0,
    SVPN_ERROR_INVALID_PARAM = -1,
    SVPN_ERROR_CRYPTO_FAIL = -2,
    SVPN_ERROR_NETWORK_FAIL = -3,
    SVPN_ERROR_AUTH_FAIL = -4,
    SVPN_ERROR_LICENSE_INVALID = -5,
    SVPN_ERROR_LICENSE_EXPIRED = -6,
    SVPN_ERROR_MEMORY = -7,
    SVPN_ERROR_SYSTEM = -8
} svpn_error_t;

// Cryptographic structures
typedef struct {
    uint8_t key[CHACHA20_KEY_SIZE];
    uint8_t nonce[CHACHA20_NONCE_SIZE];
    uint32_t counter;
} chacha20_ctx_t;

typedef struct {
    uint8_t key[CHACHA20_KEY_SIZE];
} poly1305_ctx_t;

typedef struct {
    uint8_t private_key[X25519_KEY_SIZE];
    uint8_t public_key[X25519_KEY_SIZE];
} x25519_keypair_t;

// License structure
typedef struct {
    char user_id[64];
    time_t issued_at;
    time_t expires_at;
    uint32_t max_connections;
    uint8_t signature[LICENSE_SIGNATURE_SIZE];
} license_t;

// Network packet structure
typedef struct {
    uint32_t magic;           // Magic number for packet identification
    uint16_t version;         // Protocol version
    uint16_t type;           // Packet type
    uint32_t sequence;       // Sequence number
    uint32_t timestamp;      // Timestamp
    uint16_t payload_len;    // Payload length
    uint8_t reserved[14];    // Reserved for future use
    uint8_t payload[MAX_PAYLOAD_SIZE];
} __attribute__((packed)) vpn_packet_t;

// Packet types
#define PKT_TYPE_HANDSHAKE    0x0001
#define PKT_TYPE_AUTH         0x0002
#define PKT_TYPE_DATA         0x0003
#define PKT_TYPE_KEEPALIVE    0x0004
#define PKT_TYPE_DISCONNECT   0x0005

// Function declarations

// Crypto functions
svpn_error_t chacha20_init(chacha20_ctx_t *ctx, const uint8_t *key, const uint8_t *nonce);
svpn_error_t chacha20_encrypt(chacha20_ctx_t *ctx, const uint8_t *plaintext, 
                             uint8_t *ciphertext, size_t len);
svpn_error_t chacha20_decrypt(chacha20_ctx_t *ctx, const uint8_t *ciphertext, 
                             uint8_t *plaintext, size_t len);

svpn_error_t poly1305_auth(const uint8_t *key, const uint8_t *data, size_t len, 
                          uint8_t *tag);
svpn_error_t poly1305_verify(const uint8_t *key, const uint8_t *data, size_t len, 
                            const uint8_t *tag);

svpn_error_t x25519_generate_keypair(x25519_keypair_t *keypair);
svpn_error_t x25519_shared_secret(const uint8_t *private_key, const uint8_t *public_key, 
                                 uint8_t *shared_secret);

// Authentication functions
svpn_error_t license_validate(const license_t *license, const uint8_t *public_key);
svpn_error_t license_generate(license_t *license, const uint8_t *private_key, 
                             const char *user_id, time_t duration);

// Network functions
svpn_error_t packet_create(vpn_packet_t *packet, uint16_t type, const uint8_t *payload, 
                          uint16_t payload_len);
svpn_error_t packet_encrypt(vpn_packet_t *packet, const uint8_t *key);
svpn_error_t packet_decrypt(vpn_packet_t *packet, const uint8_t *key);

// Utility functions
void secure_random(uint8_t *buffer, size_t len);
void secure_zero(void *ptr, size_t len);
uint64_t get_timestamp_ms(void);

#endif // SECUREVPN_H
