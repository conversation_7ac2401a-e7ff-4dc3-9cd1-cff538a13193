#include "prometheus_metrics.h"
#include "securevpn.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <getopt.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <pthread.h>

// Metrics exporter for Secure VPN
static bool g_running = true;
static metrics_registry_t g_registry;
static int g_server_socket = -1;

// Signal handler
static void signal_handler(int sig) {
    printf("Received signal %d, shutting down...\n", sig);
    g_running = false;
    if (g_server_socket >= 0) {
        close(g_server_socket);
    }
}

// HTTP response helper
static void send_http_response(int client_socket, int status_code, const char *content_type, 
                              const char *body, size_t body_length) {
    char header[512];
    snprintf(header, sizeof(header),
             "HTTP/1.1 %d %s\r\n"
             "Content-Type: %s\r\n"
             "Content-Length: %zu\r\n"
             "Connection: close\r\n"
             "\r\n",
             status_code, 
             (status_code == 200) ? "OK" : "Error",
             content_type, 
             body_length);

    send(client_socket, header, strlen(header), 0);
    if (body && body_length > 0) {
        send(client_socket, body, body_length, 0);
    }
}

// Handle HTTP request
static void handle_http_request(int client_socket) {
    char buffer[4096];
    ssize_t bytes_received = recv(client_socket, buffer, sizeof(buffer) - 1, 0);
    
    if (bytes_received <= 0) {
        close(client_socket);
        return;
    }
    
    buffer[bytes_received] = '\0';
    
    // Parse request line
    char method[16], path[256], version[16];
    if (sscanf(buffer, "%15s %255s %15s", method, path, version) != 3) {
        const char *error_msg = "Bad Request";
        send_http_response(client_socket, 400, "text/plain", error_msg, strlen(error_msg));
        close(client_socket);
        return;
    }
    
    if (strcmp(method, "GET") != 0) {
        const char *error_msg = "Method Not Allowed";
        send_http_response(client_socket, 405, "text/plain", error_msg, strlen(error_msg));
        close(client_socket);
        return;
    }
    
    if (strcmp(path, "/metrics") == 0) {
        // Export Prometheus metrics
        char metrics_output[65536];
        metrics_error_t result = metrics_export_prometheus(&g_registry, metrics_output, 
                                                          sizeof(metrics_output));
        
        if (result == METRICS_SUCCESS) {
            send_http_response(client_socket, 200, "text/plain; version=0.0.4", 
                             metrics_output, strlen(metrics_output));
        } else {
            const char *error_msg = "Internal Server Error";
            send_http_response(client_socket, 500, "text/plain", error_msg, strlen(error_msg));
        }
    } else if (strcmp(path, "/health") == 0) {
        const char *health_msg = "OK";
        send_http_response(client_socket, 200, "text/plain", health_msg, strlen(health_msg));
    } else {
        const char *error_msg = "Not Found";
        send_http_response(client_socket, 404, "text/plain", error_msg, strlen(error_msg));
    }
    
    close(client_socket);
}

// HTTP server thread
static void* http_server_thread(void *arg) {
    uint16_t port = *(uint16_t*)arg;
    
    g_server_socket = socket(AF_INET, SOCK_STREAM, 0);
    if (g_server_socket < 0) {
        perror("socket");
        return NULL;
    }
    
    int opt = 1;
    setsockopt(g_server_socket, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));
    
    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = INADDR_ANY;
    server_addr.sin_port = htons(port);
    
    if (bind(g_server_socket, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        perror("bind");
        close(g_server_socket);
        return NULL;
    }
    
    if (listen(g_server_socket, 10) < 0) {
        perror("listen");
        close(g_server_socket);
        return NULL;
    }
    
    printf("Metrics server listening on port %d\n", port);
    printf("Metrics available at: http://localhost:%d/metrics\n", port);
    printf("Health check at: http://localhost:%d/health\n", port);
    
    while (g_running) {
        struct sockaddr_in client_addr;
        socklen_t client_addr_len = sizeof(client_addr);
        
        int client_socket = accept(g_server_socket, (struct sockaddr*)&client_addr, &client_addr_len);
        if (client_socket < 0) {
            if (g_running) {
                perror("accept");
            }
            continue;
        }
        
        handle_http_request(client_socket);
    }
    
    close(g_server_socket);
    return NULL;
}

// Update metrics periodically
static void update_metrics(const char *database_path) {
    // Update system metrics
    double cpu_percent, memory_percent, disk_percent;
    if (metrics_get_system_stats(&cpu_percent, &memory_percent, &disk_percent) == METRICS_SUCCESS) {
        metrics_gauge_set(&g_registry, "system_cpu_usage_percent", "", cpu_percent);
        metrics_gauge_set(&g_registry, "system_memory_usage_percent", "", memory_percent);
        metrics_gauge_set(&g_registry, "system_disk_usage_percent", "", disk_percent);
    }
    
    // Simulate VPN metrics (in production, these would come from the actual VPN server)
    static uint64_t total_connections = 0;
    static uint64_t bytes_sent = 0;
    static uint64_t bytes_received = 0;
    
    total_connections += rand() % 5;
    bytes_sent += 1024 + (rand() % 10240);
    bytes_received += 2048 + (rand() % 20480);
    
    metrics_counter_add(&g_registry, "vpn_connections_total", "", total_connections);
    metrics_counter_add(&g_registry, "vpn_bytes_sent_total", "", bytes_sent);
    metrics_counter_add(&g_registry, "vpn_bytes_received_total", "", bytes_received);
    
    // Set some gauge values
    metrics_gauge_set(&g_registry, "vpn_connections_active", "", 10 + (rand() % 50));
    metrics_gauge_set(&g_registry, "vpn_users_total", "", 100 + (rand() % 900));
    metrics_gauge_set(&g_registry, "vpn_revenue_monthly", "", 5000.0 + (rand() % 10000));
}

static void print_usage(const char *program_name) {
    printf("Usage: %s [OPTIONS]\n", program_name);
    printf("Secure VPN Metrics Exporter\n\n");
    printf("Options:\n");
    printf("  -p, --port PORT        HTTP server port (default: 9090)\n");
    printf("  -d, --database PATH    Database file path\n");
    printf("  -i, --interval SEC     Update interval in seconds (default: 30)\n");
    printf("  -h, --help            Show this help message\n");
    printf("\nExamples:\n");
    printf("  %s --port 9090 --database /var/lib/securevpn/vpn.db\n", program_name);
    printf("  %s -p 8080 -i 60\n", program_name);
}

int main(int argc, char *argv[]) {
    uint16_t port = 9090;
    char database_path[512] = "";
    uint32_t update_interval = 30;
    
    // Parse command line arguments
    static struct option long_options[] = {
        {"port", required_argument, 0, 'p'},
        {"database", required_argument, 0, 'd'},
        {"interval", required_argument, 0, 'i'},
        {"help", no_argument, 0, 'h'},
        {0, 0, 0, 0}
    };
    
    int opt;
    while ((opt = getopt_long(argc, argv, "p:d:i:h", long_options, NULL)) != -1) {
        switch (opt) {
            case 'p':
                port = (uint16_t)atoi(optarg);
                if (port == 0) {
                    fprintf(stderr, "Invalid port number: %s\n", optarg);
                    return 1;
                }
                break;
            case 'd':
                strncpy(database_path, optarg, sizeof(database_path) - 1);
                break;
            case 'i':
                update_interval = (uint32_t)atoi(optarg);
                if (update_interval == 0) {
                    fprintf(stderr, "Invalid interval: %s\n", optarg);
                    return 1;
                }
                break;
            case 'h':
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    // Set up signal handlers
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // Initialize metrics registry
    metrics_error_t result = metrics_init(&g_registry, "securevpn-metrics", "1.0.0");
    if (result != METRICS_SUCCESS) {
        fprintf(stderr, "Failed to initialize metrics registry: %s\n", 
                metrics_error_string(result));
        return 1;
    }
    
    // Initialize VPN metrics
    result = metrics_init_vpn_metrics(&g_registry);
    if (result != METRICS_SUCCESS) {
        fprintf(stderr, "Failed to initialize VPN metrics: %s\n", 
                metrics_error_string(result));
        metrics_cleanup(&g_registry);
        return 1;
    }
    
    printf("Starting Secure VPN Metrics Exporter\n");
    printf("Port: %d\n", port);
    printf("Database: %s\n", strlen(database_path) > 0 ? database_path : "Not specified");
    printf("Update interval: %d seconds\n", update_interval);
    
    // Start HTTP server thread
    pthread_t server_thread;
    if (pthread_create(&server_thread, NULL, http_server_thread, &port) != 0) {
        fprintf(stderr, "Failed to create HTTP server thread\n");
        metrics_cleanup(&g_registry);
        return 1;
    }
    
    // Main metrics update loop
    while (g_running) {
        update_metrics(database_path);
        
        for (uint32_t i = 0; i < update_interval && g_running; i++) {
            sleep(1);
        }
    }
    
    // Cleanup
    printf("Shutting down...\n");
    pthread_join(server_thread, NULL);
    metrics_cleanup(&g_registry);
    
    printf("Metrics exporter stopped\n");
    return 0;
}
