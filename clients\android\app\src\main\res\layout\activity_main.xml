<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@color/background"
    tools:context=".MainActivity">

    <!-- App Logo and Title -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="32dp">

        <ImageView
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_vpn_shield"
            android:layout_marginEnd="16dp"
            android:contentDescription="@string/app_logo" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/primary_text" />

    </LinearLayout>

    <!-- Server Selection -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="24dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/select_server"
            android:textSize="16sp"
            android:textColor="@color/secondary_text"
            android:layout_marginBottom="8dp" />

        <Spinner
            android:id="@+id/serverSpinner"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:background="@drawable/spinner_background"
            android:padding="12dp" />

    </LinearLayout>

    <!-- Connection Status Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/card_background">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Status Icon and Text -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <ImageView
                    android:id="@+id/statusIcon"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_disconnected"
                    android:layout_marginEnd="12dp"
                    android:contentDescription="@string/connection_status" />

                <TextView
                    android:id="@+id/statusText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/disconnected"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/disconnected" />

            </LinearLayout>

            <!-- Progress Bar -->
            <ProgressBar
                android:id="@+id/progressBar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                android:progressTint="@color/primary" />

            <!-- Connect Button -->
            <Button
                android:id="@+id/connectButton"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="@string/connect"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/button_primary"
                android:textColor="@color/white"
                android:elevation="2dp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Statistics Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/card_background">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/connection_stats"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/primary_text"
                android:layout_marginBottom="12dp" />

            <TextView
                android:id="@+id/statsText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/no_connection"
                android:textSize="14sp"
                android:textColor="@color/secondary_text"
                android:lineSpacingExtra="4dp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Quick Actions -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginTop="auto">

        <ImageButton
            android:id="@+id/settingsButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_settings"
            android:background="@drawable/button_icon"
            android:layout_marginEnd="16dp"
            android:contentDescription="@string/settings" />

        <ImageButton
            android:id="@+id/helpButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_help"
            android:background="@drawable/button_icon"
            android:layout_marginEnd="16dp"
            android:contentDescription="@string/help" />

        <ImageButton
            android:id="@+id/aboutButton"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_info"
            android:background="@drawable/button_icon"
            android:contentDescription="@string/about" />

    </LinearLayout>

</LinearLayout>
