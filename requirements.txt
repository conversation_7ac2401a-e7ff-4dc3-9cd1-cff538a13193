# VPN-in-a-Box Requirements
# Core dependencies for the VPN server and client

# Cryptography and Security
cryptography>=41.0.0
pycryptodome>=3.18.0
bcrypt>=4.0.0

# Networking
requests>=2.31.0
websockets>=11.0.0
aiohttp>=3.8.0

# Database
sqlite3  # Built into Python
sqlalchemy>=2.0.0

# GUI Framework
tkinter  # Built into Python (Windows/Linux)
pillow>=10.0.0  # For image handling in GUI

# System Integration
psutil>=5.9.0  # System monitoring
pywin32>=306; sys_platform == "win32"  # Windows integration

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.7.0
flake8>=6.0.0

# Optional: Advanced Features
# Uncomment if you want these features
# scapy>=2.5.0  # Advanced packet manipulation
# paramiko>=3.3.0  # SSH tunneling
# dnspython>=2.4.0  # DNS manipulation
# netifaces>=0.11.0  # Network interface detection

# Web Dashboard (optional)
flask>=2.3.0
flask-cors>=4.0.0

# Monitoring (optional)
prometheus-client>=0.17.0

# Cloud Deployment (optional)
boto3>=1.28.0  # AWS
digitalocean>=1.17.0  # DigitalOcean
python-dotenv>=1.0.0  # Environment variables
