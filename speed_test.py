#!/usr/bin/env python3
"""
VPN Speed Test - Show users their connection performance
"""

import time
import requests
import threading
import json
from urllib.parse import urlparse

class VPNSpeedTest:
    def __init__(self):
        self.results = {}
        
    def test_download_speed(self, callback=None):
        """Test download speed"""
        print("📥 Testing download speed...")
        
        # Test files of different sizes
        test_urls = [
            "http://speedtest.ftp.otenet.gr/files/test1Mb.db",  # 1MB
            "http://speedtest.ftp.otenet.gr/files/test10Mb.db", # 10MB
        ]
        
        speeds = []
        
        for url in test_urls:
            try:
                start_time = time.time()
                response = requests.get(url, stream=True, timeout=30)
                
                total_size = 0
                for chunk in response.iter_content(chunk_size=8192):
                    total_size += len(chunk)
                
                end_time = time.time()
                duration = end_time - start_time
                
                if duration > 0:
                    speed_mbps = (total_size * 8) / (duration * 1000000)  # Convert to Mbps
                    speeds.append(speed_mbps)
                    
                    if callback:
                        callback(f"Downloaded {total_size/1024/1024:.1f}MB in {duration:.1f}s = {speed_mbps:.1f} Mbps")
                
            except Exception as e:
                if callback:
                    callback(f"Download test failed: {e}")
        
        avg_speed = sum(speeds) / len(speeds) if speeds else 0
        self.results['download'] = avg_speed
        
        if callback:
            callback(f"Average download speed: {avg_speed:.1f} Mbps")
        
        return avg_speed
    
    def test_upload_speed(self, callback=None):
        """Test upload speed"""
        print("📤 Testing upload speed...")
        
        # Create test data
        test_data = b'0' * (1024 * 1024)  # 1MB of data
        
        try:
            start_time = time.time()
            
            # Upload to httpbin (echo service)
            response = requests.post(
                'https://httpbin.org/post',
                data=test_data,
                timeout=30
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if duration > 0:
                speed_mbps = (len(test_data) * 8) / (duration * 1000000)
                self.results['upload'] = speed_mbps
                
                if callback:
                    callback(f"Uploaded {len(test_data)/1024/1024:.1f}MB in {duration:.1f}s = {speed_mbps:.1f} Mbps")
                
                return speed_mbps
            
        except Exception as e:
            if callback:
                callback(f"Upload test failed: {e}")
            
        self.results['upload'] = 0
        return 0
    
    def test_latency(self, callback=None):
        """Test latency/ping"""
        print("🏓 Testing latency...")
        
        test_hosts = [
            'google.com',
            'cloudflare.com',
            'github.com'
        ]
        
        latencies = []
        
        for host in test_hosts:
            try:
                start_time = time.time()
                response = requests.get(f'https://{host}', timeout=10)
                end_time = time.time()
                
                latency_ms = (end_time - start_time) * 1000
                latencies.append(latency_ms)
                
                if callback:
                    callback(f"Ping to {host}: {latency_ms:.0f}ms")
                
            except Exception as e:
                if callback:
                    callback(f"Ping to {host} failed: {e}")
        
        avg_latency = sum(latencies) / len(latencies) if latencies else 0
        self.results['latency'] = avg_latency
        
        if callback:
            callback(f"Average latency: {avg_latency:.0f}ms")
        
        return avg_latency
    
    def get_ip_info(self, callback=None):
        """Get current IP and location info"""
        print("🌍 Getting IP information...")
        
        try:
            # Get IP info
            response = requests.get('https://ipapi.co/json/', timeout=10)
            ip_data = response.json()
            
            self.results['ip_info'] = ip_data
            
            if callback:
                callback(f"Public IP: {ip_data.get('ip', 'Unknown')}")
                callback(f"Location: {ip_data.get('city', 'Unknown')}, {ip_data.get('country_name', 'Unknown')}")
                callback(f"ISP: {ip_data.get('org', 'Unknown')}")
            
            return ip_data
            
        except Exception as e:
            if callback:
                callback(f"IP info failed: {e}")
            return {}
    
    def run_full_test(self, callback=None):
        """Run complete speed test"""
        if callback:
            callback("🚀 Starting VPN speed test...")
            callback("=" * 40)
        
        # Get IP info first
        self.get_ip_info(callback)
        
        if callback:
            callback("")
        
        # Test latency
        self.test_latency(callback)
        
        if callback:
            callback("")
        
        # Test download speed
        self.test_download_speed(callback)
        
        if callback:
            callback("")
        
        # Test upload speed
        self.test_upload_speed(callback)
        
        if callback:
            callback("")
            callback("=" * 40)
            callback("📊 Speed Test Complete!")
            callback(f"Download: {self.results.get('download', 0):.1f} Mbps")
            callback(f"Upload: {self.results.get('upload', 0):.1f} Mbps")
            callback(f"Latency: {self.results.get('latency', 0):.0f} ms")
        
        return self.results

# GUI Integration
def create_speed_test_window():
    """Create speed test window for VPN client"""
    import tkinter as tk
    from tkinter import scrolledtext
    
    def run_test():
        test_btn.config(state=tk.DISABLED, text="Testing...")
        results_text.delete(1.0, tk.END)
        
        def update_results(message):
            results_text.insert(tk.END, message + "\n")
            results_text.see(tk.END)
            root.update()
        
        def test_thread():
            speed_test = VPNSpeedTest()
            speed_test.run_full_test(update_results)
            test_btn.config(state=tk.NORMAL, text="Run Speed Test")
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    # Create window
    root = tk.Tk()
    root.title("VPN Speed Test")
    root.geometry("500x400")
    root.configure(bg="#1e1e1e")
    
    # Title
    title = tk.Label(root, text="🚀 VPN Speed Test", 
                    font=("Arial", 16, "bold"), fg="white", bg="#1e1e1e")
    title.pack(pady=20)
    
    # Test button
    test_btn = tk.Button(root, text="Run Speed Test", command=run_test,
                        font=("Arial", 12, "bold"), bg="#3498db", fg="white",
                        width=15, height=2)
    test_btn.pack(pady=10)
    
    # Results area
    results_text = scrolledtext.ScrolledText(root, width=60, height=20,
                                           font=("Consolas", 10), bg="#2d2d2d", fg="#ffffff")
    results_text.pack(padx=20, pady=20, fill=tk.BOTH, expand=True)
    
    return root

if __name__ == "__main__":
    # Command line test
    print("🚀 VPN Speed Test")
    print("Testing your VPN connection performance...")
    print()
    
    speed_test = VPNSpeedTest()
    results = speed_test.run_full_test(print)
    
    print("\n📊 Final Results:")
    print(f"Download Speed: {results.get('download', 0):.1f} Mbps")
    print(f"Upload Speed: {results.get('upload', 0):.1f} Mbps") 
    print(f"Latency: {results.get('latency', 0):.0f} ms")
    
    # Also show GUI version
    print("\nOpening GUI speed test...")
    gui = create_speed_test_window()
    gui.mainloop()
