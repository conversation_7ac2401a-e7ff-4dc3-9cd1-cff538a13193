#include "securevpn.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <signal.h>
#include <errno.h>
#include <pthread.h>

typedef struct {
    uint16_t port;
    char license_public_key_file[256];
    char config_file[256];
    bool verbose;
    int max_clients;
} server_config_t;

typedef struct {
    int socket_fd;
    struct sockaddr_in client_addr;
    socklen_t client_addr_len;
    uint8_t shared_secret[X25519_KEY_SIZE];
    bool authenticated;
    time_t last_activity;
    pthread_t thread;
} client_session_t;

static volatile bool running = true;
static int server_socket = -1;
static uint8_t server_public_key[RSA_KEY_SIZE];
static client_session_t *client_sessions = NULL;
static int max_clients = 100;
static pthread_mutex_t sessions_mutex = PTHREAD_MUTEX_INITIALIZER;

static void signal_handler(int sig) {
    (void)sig;
    running = false;
    if (server_socket >= 0) {
        close(server_socket);
    }
}

static void print_usage(const char *program_name) {
    printf("Secure VPN Server v%d.%d.%d\n", 
           SVPN_VERSION_MAJOR, SVPN_VERSION_MINOR, SVPN_VERSION_PATCH);
    printf("Usage: %s [options]\n", program_name);
    printf("Options:\n");
    printf("  -p <port>         Server port (default: 8443)\n");
    printf("  -k <key_file>     License public key file (default: server.pub)\n");
    printf("  -c <config_file>  Configuration file path\n");
    printf("  -m <max_clients>  Maximum clients (default: 100)\n");
    printf("  -v                Verbose output\n");
    printf("  -h                Show this help\n");
}

static svpn_error_t parse_arguments(int argc, char *argv[], server_config_t *config) {
    int opt;
    
    // Set defaults
    config->port = 8443;
    strcpy(config->license_public_key_file, "server.pub");
    config->config_file[0] = '\0';
    config->verbose = false;
    config->max_clients = 100;
    
    while ((opt = getopt(argc, argv, "p:k:c:m:vh")) != -1) {
        switch (opt) {
            case 'p':
                config->port = (uint16_t)atoi(optarg);
                break;
            case 'k':
                strncpy(config->license_public_key_file, optarg, 
                       sizeof(config->license_public_key_file) - 1);
                break;
            case 'c':
                strncpy(config->config_file, optarg, sizeof(config->config_file) - 1);
                break;
            case 'm':
                config->max_clients = atoi(optarg);
                break;
            case 'v':
                config->verbose = true;
                break;
            case 'h':
                print_usage(argv[0]);
                exit(0);
            default:
                return SVPN_ERROR_INVALID_PARAM;
        }
    }
    
    return SVPN_SUCCESS;
}

static svpn_error_t load_public_key(const char *filename) {
    FILE *file = fopen(filename, "rb");
    if (!file) {
        perror("fopen public key");
        return SVPN_ERROR_SYSTEM;
    }
    
    size_t read_bytes = fread(server_public_key, 1, RSA_KEY_SIZE, file);
    fclose(file);
    
    if (read_bytes != RSA_KEY_SIZE) {
        return SVPN_ERROR_SYSTEM;
    }
    
    return SVPN_SUCCESS;
}

static client_session_t* find_free_session(void) {
    pthread_mutex_lock(&sessions_mutex);
    
    for (int i = 0; i < max_clients; i++) {
        if (client_sessions[i].socket_fd == -1) {
            pthread_mutex_unlock(&sessions_mutex);
            return &client_sessions[i];
        }
    }
    
    pthread_mutex_unlock(&sessions_mutex);
    return NULL;
}

static void cleanup_session(client_session_t *session) {
    if (session->socket_fd >= 0) {
        close(session->socket_fd);
        session->socket_fd = -1;
    }
    session->authenticated = false;
    session->last_activity = 0;
    secure_zero(session->shared_secret, sizeof(session->shared_secret));
}

static void* handle_client(void *arg) {
    client_session_t *session = (client_session_t*)arg;
    vpn_packet_t packet;
    x25519_keypair_t server_keypair;
    svpn_error_t result;
    ssize_t bytes_received;
    
    printf("New client connected from %s:%d\n", 
           inet_ntoa(session->client_addr.sin_addr),
           ntohs(session->client_addr.sin_port));
    
    // Generate server keypair for this session
    result = x25519_generate_keypair(&server_keypair);
    if (result != SVPN_SUCCESS) {
        printf("Failed to generate server keypair\n");
        goto cleanup;
    }
    
    // Wait for client handshake
    bytes_received = recv(session->socket_fd, &packet, sizeof(packet), 0);
    if (bytes_received < 0) {
        perror("recv handshake");
        goto cleanup;
    }
    
    // Verify handshake packet
    if (ntohs(packet.type) != PKT_TYPE_HANDSHAKE || 
        ntohs(packet.payload_len) != X25519_KEY_SIZE) {
        printf("Invalid handshake packet\n");
        goto cleanup;
    }
    
    // Compute shared secret
    result = x25519_shared_secret(server_keypair.private_key, 
                                 packet.payload, session->shared_secret);
    if (result != SVPN_SUCCESS) {
        printf("Failed to compute shared secret\n");
        goto cleanup;
    }
    
    // Send handshake response with server public key
    result = packet_create(&packet, PKT_TYPE_HANDSHAKE, 
                          server_keypair.public_key, X25519_KEY_SIZE);
    if (result != SVPN_SUCCESS) {
        goto cleanup;
    }
    
    if (send(session->socket_fd, &packet, VPN_HEADER_SIZE + X25519_KEY_SIZE, 0) < 0) {
        perror("send handshake response");
        goto cleanup;
    }
    
    printf("Handshake completed with client\n");
    
    // Wait for authentication packet
    bytes_received = recv(session->socket_fd, &packet, sizeof(packet), 0);
    if (bytes_received < 0) {
        perror("recv auth");
        goto cleanup;
    }
    
    // Decrypt authentication packet
    result = packet_decrypt(&packet, session->shared_secret);
    if (result != SVPN_SUCCESS) {
        printf("Failed to decrypt auth packet\n");
        goto cleanup;
    }
    
    // Verify packet type and size
    if (ntohs(packet.type) != PKT_TYPE_AUTH || 
        ntohs(packet.payload_len) != sizeof(license_t)) {
        printf("Invalid auth packet\n");
        goto cleanup;
    }
    
    // Validate license
    license_t *license = (license_t*)packet.payload;
    result = license_validate(license, server_public_key);
    
    uint32_t auth_result = (result == SVPN_SUCCESS) ? 0 : 1;
    
    // Send authentication response
    result = packet_create(&packet, PKT_TYPE_AUTH, 
                          (uint8_t*)&auth_result, sizeof(auth_result));
    if (result != SVPN_SUCCESS) {
        goto cleanup;
    }
    
    result = packet_encrypt(&packet, session->shared_secret);
    if (result != SVPN_SUCCESS) {
        goto cleanup;
    }
    
    if (send(session->socket_fd, &packet, VPN_HEADER_SIZE + sizeof(auth_result), 0) < 0) {
        perror("send auth response");
        goto cleanup;
    }
    
    if (auth_result == 0) {
        session->authenticated = true;
        session->last_activity = time(NULL);
        printf("Client authenticated successfully (user: %s)\n", license->user_id);
        
        // Main client handling loop
        while (running && session->authenticated) {
            fd_set read_fds;
            struct timeval timeout;
            
            FD_ZERO(&read_fds);
            FD_SET(session->socket_fd, &read_fds);
            
            timeout.tv_sec = 60;  // 60 second timeout
            timeout.tv_usec = 0;
            
            int select_result = select(session->socket_fd + 1, &read_fds, NULL, NULL, &timeout);
            
            if (select_result < 0) {
                if (errno != EINTR) {
                    perror("select");
                    break;
                }
                continue;
            }
            
            if (select_result == 0) {
                // Timeout - check if client is still alive
                time_t current_time = time(NULL);
                if (current_time - session->last_activity > 120) {  // 2 minutes
                    printf("Client timeout\n");
                    break;
                }
                continue;
            }
            
            if (FD_ISSET(session->socket_fd, &read_fds)) {
                bytes_received = recv(session->socket_fd, &packet, sizeof(packet), 0);
                if (bytes_received <= 0) {
                    if (bytes_received < 0) {
                        perror("recv");
                    }
                    break;
                }
                
                session->last_activity = time(NULL);
                
                // Handle different packet types
                uint16_t packet_type = ntohs(packet.type);
                switch (packet_type) {
                    case PKT_TYPE_KEEPALIVE:
                        // Just update last activity time
                        break;
                    case PKT_TYPE_DATA:
                        // Handle VPN data packet (not implemented in this demo)
                        break;
                    case PKT_TYPE_DISCONNECT:
                        printf("Client requested disconnect\n");
                        goto cleanup;
                    default:
                        printf("Unknown packet type: %d\n", packet_type);
                        break;
                }
            }
        }
    } else {
        printf("Client authentication failed\n");
    }
    
cleanup:
    printf("Client disconnected\n");
    cleanup_session(session);
    secure_zero(&server_keypair, sizeof(server_keypair));
    return NULL;
}

static svpn_error_t start_server(uint16_t port) {
    struct sockaddr_in server_addr;
    int opt = 1;
    
    // Create socket
    server_socket = socket(AF_INET, SOCK_STREAM, 0);
    if (server_socket < 0) {
        perror("socket");
        return SVPN_ERROR_NETWORK_FAIL;
    }
    
    // Set socket options
    if (setsockopt(server_socket, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) < 0) {
        perror("setsockopt");
        close(server_socket);
        return SVPN_ERROR_NETWORK_FAIL;
    }
    
    // Setup server address
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = INADDR_ANY;
    server_addr.sin_port = htons(port);
    
    // Bind socket
    if (bind(server_socket, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        perror("bind");
        close(server_socket);
        return SVPN_ERROR_NETWORK_FAIL;
    }
    
    // Listen for connections
    if (listen(server_socket, 10) < 0) {
        perror("listen");
        close(server_socket);
        return SVPN_ERROR_NETWORK_FAIL;
    }
    
    printf("Server listening on port %d\n", port);
    
    return SVPN_SUCCESS;
}

int main(int argc, char *argv[]) {
    server_config_t config;
    svpn_error_t result;
    
    printf("Secure VPN Server v%d.%d.%d\n", 
           SVPN_VERSION_MAJOR, SVPN_VERSION_MINOR, SVPN_VERSION_PATCH);
    printf("Starting server...\n");
    
    // Setup signal handlers
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGPIPE, SIG_IGN);  // Ignore broken pipe signals
    
    // Parse command line arguments
    result = parse_arguments(argc, argv, &config);
    if (result != SVPN_SUCCESS) {
        print_usage(argv[0]);
        return 1;
    }
    
    max_clients = config.max_clients;
    
    // Load license public key
    result = load_public_key(config.license_public_key_file);
    if (result != SVPN_SUCCESS) {
        fprintf(stderr, "Error: Failed to load public key file '%s'\n", 
                config.license_public_key_file);
        return 1;
    }
    
    printf("License public key loaded\n");
    
    // Initialize client sessions array
    client_sessions = calloc(max_clients, sizeof(client_session_t));
    if (!client_sessions) {
        fprintf(stderr, "Error: Failed to allocate memory for client sessions\n");
        return 1;
    }
    
    // Initialize all sessions
    for (int i = 0; i < max_clients; i++) {
        client_sessions[i].socket_fd = -1;
    }
    
    // Start server
    result = start_server(config.port);
    if (result != SVPN_SUCCESS) {
        fprintf(stderr, "Error: Failed to start server on port %d\n", config.port);
        free(client_sessions);
        return 1;
    }
    
    // Main server loop
    while (running) {
        struct sockaddr_in client_addr;
        socklen_t client_addr_len = sizeof(client_addr);
        int client_socket;
        client_session_t *session;
        
        // Accept new connection
        client_socket = accept(server_socket, (struct sockaddr*)&client_addr, &client_addr_len);
        if (client_socket < 0) {
            if (errno != EINTR) {
                perror("accept");
            }
            continue;
        }
        
        // Find free session slot
        session = find_free_session();
        if (!session) {
            printf("Maximum clients reached, rejecting connection\n");
            close(client_socket);
            continue;
        }
        
        // Initialize session
        session->socket_fd = client_socket;
        session->client_addr = client_addr;
        session->client_addr_len = client_addr_len;
        session->authenticated = false;
        session->last_activity = time(NULL);
        
        // Create thread to handle client
        if (pthread_create(&session->thread, NULL, handle_client, session) != 0) {
            perror("pthread_create");
            cleanup_session(session);
            continue;
        }
        
        // Detach thread so it cleans up automatically
        pthread_detach(session->thread);
    }
    
    printf("\nShutting down server...\n");
    
    // Cleanup
    if (server_socket >= 0) {
        close(server_socket);
    }
    
    // Close all client sessions
    for (int i = 0; i < max_clients; i++) {
        cleanup_session(&client_sessions[i]);
    }
    
    free(client_sessions);
    pthread_mutex_destroy(&sessions_mutex);
    
    return 0;
}
