#ifndef DATABASE_H
#define DATABASE_H

#include <stdint.h>
#include <stdbool.h>
#include <time.h>
#include <sqlite3.h>

// Database error codes
typedef enum {
    DB_SUCCESS = 0,
    DB_ERROR_INIT = -1,
    DB_ERROR_QUERY = -2,
    DB_ERROR_NOT_FOUND = -3,
    DB_ERROR_DUPLICATE = -4,
    DB_ERROR_INVALID_PARAM = -5,
    DB_ERROR_MEMORY = -6
} db_error_t;

// User subscription plans
typedef enum {
    PLAN_FREE = 0,
    PLAN_BASIC = 1,
    PLAN_PREMIUM = 2,
    PLAN_ENTERPRISE = 3
} subscription_plan_t;

// User account structure
typedef struct {
    uint64_t user_id;
    char email[256];
    char password_hash[64];  // SHA-256 hash
    char salt[32];           // Random salt for password
    subscription_plan_t plan;
    time_t created_at;
    time_t subscription_expires;
    bool is_active;
    uint32_t max_connections;
    uint64_t bytes_used_today;
    uint64_t bytes_limit_daily;
} user_account_t;

// License record structure
typedef struct {
    uint64_t license_id;
    uint64_t user_id;
    char license_key[512];   // Base64 encoded license
    time_t issued_at;
    time_t expires_at;
    bool is_revoked;
    char device_name[128];
} license_record_t;

// Session tracking structure
typedef struct {
    uint64_t session_id;
    uint64_t user_id;
    char client_ip[46];      // IPv6 compatible
    time_t connected_at;
    time_t last_activity;
    uint64_t bytes_sent;
    uint64_t bytes_received;
    bool is_active;
} session_record_t;

// Payment record structure
typedef struct {
    uint64_t payment_id;
    uint64_t user_id;
    char transaction_id[128];
    subscription_plan_t plan;
    double amount;
    char currency[4];
    time_t payment_date;
    time_t subscription_start;
    time_t subscription_end;
    char payment_method[32];
    bool is_successful;
} payment_record_t;

// Database context
typedef struct {
    sqlite3 *db;
    char db_path[512];
    bool is_initialized;
} database_ctx_t;

// Database initialization and cleanup
db_error_t db_init(database_ctx_t *ctx, const char *db_path);
db_error_t db_close(database_ctx_t *ctx);
db_error_t db_create_tables(database_ctx_t *ctx);

// User management functions
db_error_t db_user_create(database_ctx_t *ctx, const char *email, const char *password, 
                         subscription_plan_t plan, user_account_t *user);
db_error_t db_user_authenticate(database_ctx_t *ctx, const char *email, const char *password, 
                               user_account_t *user);
db_error_t db_user_get_by_id(database_ctx_t *ctx, uint64_t user_id, user_account_t *user);
db_error_t db_user_get_by_email(database_ctx_t *ctx, const char *email, user_account_t *user);
db_error_t db_user_update_subscription(database_ctx_t *ctx, uint64_t user_id, 
                                      subscription_plan_t plan, time_t expires_at);
db_error_t db_user_update_usage(database_ctx_t *ctx, uint64_t user_id, uint64_t bytes_used);
db_error_t db_user_reset_daily_usage(database_ctx_t *ctx);

// License management functions
db_error_t db_license_create(database_ctx_t *ctx, uint64_t user_id, const char *license_key,
                            time_t expires_at, const char *device_name, license_record_t *license);
db_error_t db_license_validate(database_ctx_t *ctx, const char *license_key, 
                              license_record_t *license, user_account_t *user);
db_error_t db_license_revoke(database_ctx_t *ctx, uint64_t license_id);
db_error_t db_license_get_by_user(database_ctx_t *ctx, uint64_t user_id, 
                                 license_record_t *licenses, int max_licenses, int *count);

// Session management functions
db_error_t db_session_create(database_ctx_t *ctx, uint64_t user_id, const char *client_ip,
                            session_record_t *session);
db_error_t db_session_update_activity(database_ctx_t *ctx, uint64_t session_id, 
                                     uint64_t bytes_sent, uint64_t bytes_received);
db_error_t db_session_end(database_ctx_t *ctx, uint64_t session_id);
db_error_t db_session_get_active_count(database_ctx_t *ctx, uint64_t user_id, int *count);
db_error_t db_session_cleanup_inactive(database_ctx_t *ctx, time_t timeout_seconds);

// Payment management functions
db_error_t db_payment_record(database_ctx_t *ctx, uint64_t user_id, const char *transaction_id,
                            subscription_plan_t plan, double amount, const char *currency,
                            const char *payment_method, time_t subscription_start,
                            time_t subscription_end, payment_record_t *payment);
db_error_t db_payment_get_by_user(database_ctx_t *ctx, uint64_t user_id,
                                 payment_record_t *payments, int max_payments, int *count);

// Analytics functions
db_error_t db_analytics_get_user_stats(database_ctx_t *ctx, uint64_t user_id,
                                      uint64_t *total_sessions, uint64_t *total_bytes,
                                      time_t *last_connection);
db_error_t db_analytics_get_server_stats(database_ctx_t *ctx, int *active_users,
                                        int *total_sessions, uint64_t *total_bytes);

// Utility functions
const char* db_error_string(db_error_t error);
subscription_plan_t db_plan_from_string(const char *plan_str);
const char* db_plan_to_string(subscription_plan_t plan);
uint32_t db_plan_get_max_connections(subscription_plan_t plan);
uint64_t db_plan_get_daily_limit(subscription_plan_t plan);

#endif // DATABASE_H
