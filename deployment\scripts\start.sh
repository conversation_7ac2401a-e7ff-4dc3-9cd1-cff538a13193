#!/bin/bash

# Secure VPN Production Startup Script
set -euo pipefail

# Configuration
VPN_HOME="/opt/securevpn"
CONFIG_DIR="$VPN_HOME/config"
DATA_DIR="$VPN_HOME/data"
LOGS_DIR="$VPN_HOME/logs"
BIN_DIR="$VPN_HOME/bin"

# Environment variables with defaults
VPN_PORT="${VPN_PORT:-8443}"
API_PORT="${API_PORT:-8080}"
METRICS_PORT="${METRICS_PORT:-9090}"
DATABASE_PATH="${DATABASE_PATH:-$DATA_DIR/vpn.db}"
PRIVATE_KEY_FILE="${PRIVATE_KEY_FILE:-$CONFIG_DIR/server.key}"
PUBLIC_KEY_FILE="${PUBLIC_KEY_FILE:-$CONFIG_DIR/server.pub}"
LOG_LEVEL="${LOG_LEVEL:-INFO}"
MAX_CLIENTS="${MAX_CLIENTS:-100}"

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOGS_DIR/startup.log"
}

# Error handling
error_exit() {
    log "ERROR: $1"
    exit 1
}

# Signal handlers
cleanup() {
    log "Received shutdown signal, cleaning up..."
    
    # Kill background processes
    if [[ -n "${VPN_SERVER_PID:-}" ]]; then
        kill "$VPN_SERVER_PID" 2>/dev/null || true
    fi
    if [[ -n "${API_SERVER_PID:-}" ]]; then
        kill "$API_SERVER_PID" 2>/dev/null || true
    fi
    
    log "Cleanup completed"
    exit 0
}

trap cleanup SIGTERM SIGINT

# Pre-flight checks
preflight_checks() {
    log "Starting pre-flight checks..."
    
    # Check if running as correct user
    if [[ "$(id -u)" == "0" ]]; then
        error_exit "Should not run as root user"
    fi
    
    # Check required directories
    for dir in "$CONFIG_DIR" "$DATA_DIR" "$LOGS_DIR" "$BIN_DIR"; do
        if [[ ! -d "$dir" ]]; then
            error_exit "Required directory not found: $dir"
        fi
    done
    
    # Check required binaries
    for binary in "svpn_enhanced_server" "svpn_web_api" "vpn_manager"; do
        if [[ ! -x "$BIN_DIR/$binary" ]]; then
            error_exit "Required binary not found or not executable: $BIN_DIR/$binary"
        fi
    done
    
    # Check SSL certificates
    if [[ ! -f "$PRIVATE_KEY_FILE" ]] || [[ ! -f "$PUBLIC_KEY_FILE" ]]; then
        log "SSL certificates not found, generating self-signed certificates..."
        generate_certificates
    fi
    
    # Check database
    if [[ ! -f "$DATABASE_PATH" ]]; then
        log "Database not found, initializing..."
        initialize_database
    fi
    
    # Check network capabilities
    if ! ip tuntap add mode tun name test-tun 2>/dev/null; then
        error_exit "Cannot create TUN interface - missing NET_ADMIN capability"
    else
        ip tuntap del mode tun name test-tun 2>/dev/null || true
    fi
    
    log "Pre-flight checks completed successfully"
}

# Generate self-signed certificates
generate_certificates() {
    log "Generating self-signed SSL certificates..."
    
    # Generate RSA key pair for VPN
    "$BIN_DIR/keygen" -g -k "$PRIVATE_KEY_FILE" -p "$PUBLIC_KEY_FILE" || \
        error_exit "Failed to generate VPN certificates"
    
    # Generate TLS certificates for web API
    openssl req -x509 -newkey rsa:4096 -keyout "$CONFIG_DIR/tls.key" \
        -out "$CONFIG_DIR/tls.crt" -days 365 -nodes \
        -subj "/C=US/ST=State/L=City/O=SecureVPN/CN=localhost" || \
        error_exit "Failed to generate TLS certificates"
    
    log "SSL certificates generated successfully"
}

# Initialize database
initialize_database() {
    log "Initializing database..."
    
    # Create initial admin user
    "$BIN_DIR/vpn_manager" -d "$DATABASE_PATH" -k "$PRIVATE_KEY_FILE" -p "$PUBLIC_KEY_FILE" \
        create-user "<EMAIL>" "admin123" "enterprise" || \
        error_exit "Failed to create admin user"
    
    log "Database initialized successfully"
}

# Configure system settings
configure_system() {
    log "Configuring system settings..."
    
    # Enable IP forwarding (if we have permission)
    if [[ -w /proc/sys/net/ipv4/ip_forward ]]; then
        echo 1 > /proc/sys/net/ipv4/ip_forward
        log "IP forwarding enabled"
    else
        log "WARNING: Cannot enable IP forwarding - may need to be set at host level"
    fi
    
    # Set up iptables rules (if we have permission)
    if command -v iptables >/dev/null 2>&1; then
        # Allow VPN traffic
        iptables -A INPUT -p udp --dport "$VPN_PORT" -j ACCEPT 2>/dev/null || \
            log "WARNING: Cannot configure iptables rules"
        iptables -A INPUT -p tcp --dport "$API_PORT" -j ACCEPT 2>/dev/null || true
    fi
    
    log "System configuration completed"
}

# Start VPN server
start_vpn_server() {
    log "Starting VPN server on port $VPN_PORT..."
    
    "$BIN_DIR/svpn_enhanced_server" \
        --database "$DATABASE_PATH" \
        --private-key "$PRIVATE_KEY_FILE" \
        --public-key "$PUBLIC_KEY_FILE" \
        --vpn-port "$VPN_PORT" \
        --api-port "$API_PORT" \
        --max-clients "$MAX_CLIENTS" \
        --log-level "$LOG_LEVEL" \
        --daemon \
        --pid-file "$DATA_DIR/vpn_server.pid" \
        --log-file "$LOGS_DIR/vpn_server.log" &
    
    VPN_SERVER_PID=$!
    echo "$VPN_SERVER_PID" > "$DATA_DIR/vpn_server.pid"
    
    log "VPN server started with PID $VPN_SERVER_PID"
}

# Start web API server
start_api_server() {
    log "Starting web API server on port $API_PORT..."
    
    "$BIN_DIR/svpn_web_api" \
        --database "$DATABASE_PATH" \
        --port "$API_PORT" \
        --tls-cert "$CONFIG_DIR/tls.crt" \
        --tls-key "$CONFIG_DIR/tls.key" \
        --stripe-secret-key "${STRIPE_SECRET_KEY:-}" \
        --stripe-webhook-secret "${STRIPE_WEBHOOK_SECRET:-}" \
        --log-level "$LOG_LEVEL" \
        --daemon \
        --pid-file "$DATA_DIR/api_server.pid" \
        --log-file "$LOGS_DIR/api_server.log" &
    
    API_SERVER_PID=$!
    echo "$API_SERVER_PID" > "$DATA_DIR/api_server.pid"
    
    log "Web API server started with PID $API_SERVER_PID"
}

# Start metrics exporter
start_metrics_exporter() {
    log "Starting metrics exporter on port $METRICS_PORT..."
    
    # Start Prometheus metrics endpoint
    "$BIN_DIR/metrics_exporter" \
        --database "$DATABASE_PATH" \
        --port "$METRICS_PORT" \
        --interval 30 \
        --log-file "$LOGS_DIR/metrics.log" &
    
    METRICS_PID=$!
    echo "$METRICS_PID" > "$DATA_DIR/metrics.pid"
    
    log "Metrics exporter started with PID $METRICS_PID"
}

# Health check
health_check() {
    local max_attempts=30
    local attempt=1
    
    log "Performing health check..."
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f "http://localhost:$API_PORT/health" >/dev/null 2>&1; then
            log "Health check passed"
            return 0
        fi
        
        log "Health check attempt $attempt/$max_attempts failed, retrying..."
        sleep 2
        ((attempt++))
    done
    
    error_exit "Health check failed after $max_attempts attempts"
}

# Monitor processes
monitor_processes() {
    log "Starting process monitoring..."
    
    while true; do
        # Check VPN server
        if [[ -n "${VPN_SERVER_PID:-}" ]] && ! kill -0 "$VPN_SERVER_PID" 2>/dev/null; then
            log "VPN server process died, restarting..."
            start_vpn_server
        fi
        
        # Check API server
        if [[ -n "${API_SERVER_PID:-}" ]] && ! kill -0 "$API_SERVER_PID" 2>/dev/null; then
            log "API server process died, restarting..."
            start_api_server
        fi
        
        sleep 30
    done
}

# Main execution
main() {
    log "Starting Secure VPN Server..."
    log "Configuration: VPN_PORT=$VPN_PORT, API_PORT=$API_PORT, MAX_CLIENTS=$MAX_CLIENTS"
    
    # Run startup sequence
    preflight_checks
    configure_system
    start_vpn_server
    start_api_server
    start_metrics_exporter
    health_check
    
    log "All services started successfully"
    log "VPN server listening on port $VPN_PORT"
    log "Web API server listening on port $API_PORT"
    log "Metrics available on port $METRICS_PORT"
    
    # Keep the script running and monitor processes
    monitor_processes
}

# Execute main function
main "$@"
