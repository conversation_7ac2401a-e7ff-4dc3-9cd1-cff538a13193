# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/d/secure-vpn

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/d/secure-vpn/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/d/secure-vpn/build/CMakeFiles /mnt/d/secure-vpn/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/d/secure-vpn/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named securevpn_core

# Build rule for target.
securevpn_core: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 securevpn_core
.PHONY : securevpn_core

# fast build rule for target.
securevpn_core/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/build
.PHONY : securevpn_core/fast

#=============================================================================
# Target rules for targets named svpn_client

# Build rule for target.
svpn_client: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 svpn_client
.PHONY : svpn_client

# fast build rule for target.
svpn_client/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/svpn_client.dir/build.make CMakeFiles/svpn_client.dir/build
.PHONY : svpn_client/fast

#=============================================================================
# Target rules for targets named svpn_server

# Build rule for target.
svpn_server: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 svpn_server
.PHONY : svpn_server

# fast build rule for target.
svpn_server/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/svpn_server.dir/build.make CMakeFiles/svpn_server.dir/build
.PHONY : svpn_server/fast

#=============================================================================
# Target rules for targets named keygen

# Build rule for target.
keygen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 keygen
.PHONY : keygen

# fast build rule for target.
keygen/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/keygen.dir/build.make CMakeFiles/keygen.dir/build
.PHONY : keygen/fast

src/auth/license.o: src/auth/license.c.o
.PHONY : src/auth/license.o

# target to build an object file
src/auth/license.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/src/auth/license.c.o
.PHONY : src/auth/license.c.o

src/auth/license.i: src/auth/license.c.i
.PHONY : src/auth/license.i

# target to preprocess a source file
src/auth/license.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/src/auth/license.c.i
.PHONY : src/auth/license.c.i

src/auth/license.s: src/auth/license.c.s
.PHONY : src/auth/license.s

# target to generate assembly for a file
src/auth/license.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/src/auth/license.c.s
.PHONY : src/auth/license.c.s

src/client/main.o: src/client/main.c.o
.PHONY : src/client/main.o

# target to build an object file
src/client/main.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/svpn_client.dir/build.make CMakeFiles/svpn_client.dir/src/client/main.c.o
.PHONY : src/client/main.c.o

src/client/main.i: src/client/main.c.i
.PHONY : src/client/main.i

# target to preprocess a source file
src/client/main.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/svpn_client.dir/build.make CMakeFiles/svpn_client.dir/src/client/main.c.i
.PHONY : src/client/main.c.i

src/client/main.s: src/client/main.c.s
.PHONY : src/client/main.s

# target to generate assembly for a file
src/client/main.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/svpn_client.dir/build.make CMakeFiles/svpn_client.dir/src/client/main.c.s
.PHONY : src/client/main.c.s

src/crypto/chacha20.o: src/crypto/chacha20.c.o
.PHONY : src/crypto/chacha20.o

# target to build an object file
src/crypto/chacha20.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.o
.PHONY : src/crypto/chacha20.c.o

src/crypto/chacha20.i: src/crypto/chacha20.c.i
.PHONY : src/crypto/chacha20.i

# target to preprocess a source file
src/crypto/chacha20.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.i
.PHONY : src/crypto/chacha20.c.i

src/crypto/chacha20.s: src/crypto/chacha20.c.s
.PHONY : src/crypto/chacha20.s

# target to generate assembly for a file
src/crypto/chacha20.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/src/crypto/chacha20.c.s
.PHONY : src/crypto/chacha20.c.s

src/crypto/poly1305.o: src/crypto/poly1305.c.o
.PHONY : src/crypto/poly1305.o

# target to build an object file
src/crypto/poly1305.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.o
.PHONY : src/crypto/poly1305.c.o

src/crypto/poly1305.i: src/crypto/poly1305.c.i
.PHONY : src/crypto/poly1305.i

# target to preprocess a source file
src/crypto/poly1305.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.i
.PHONY : src/crypto/poly1305.c.i

src/crypto/poly1305.s: src/crypto/poly1305.c.s
.PHONY : src/crypto/poly1305.s

# target to generate assembly for a file
src/crypto/poly1305.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/src/crypto/poly1305.c.s
.PHONY : src/crypto/poly1305.c.s

src/crypto/utils.o: src/crypto/utils.c.o
.PHONY : src/crypto/utils.o

# target to build an object file
src/crypto/utils.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/src/crypto/utils.c.o
.PHONY : src/crypto/utils.c.o

src/crypto/utils.i: src/crypto/utils.c.i
.PHONY : src/crypto/utils.i

# target to preprocess a source file
src/crypto/utils.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/src/crypto/utils.c.i
.PHONY : src/crypto/utils.c.i

src/crypto/utils.s: src/crypto/utils.c.s
.PHONY : src/crypto/utils.s

# target to generate assembly for a file
src/crypto/utils.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/src/crypto/utils.c.s
.PHONY : src/crypto/utils.c.s

src/crypto/x25519.o: src/crypto/x25519.c.o
.PHONY : src/crypto/x25519.o

# target to build an object file
src/crypto/x25519.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.o
.PHONY : src/crypto/x25519.c.o

src/crypto/x25519.i: src/crypto/x25519.c.i
.PHONY : src/crypto/x25519.i

# target to preprocess a source file
src/crypto/x25519.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.i
.PHONY : src/crypto/x25519.c.i

src/crypto/x25519.s: src/crypto/x25519.c.s
.PHONY : src/crypto/x25519.s

# target to generate assembly for a file
src/crypto/x25519.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/src/crypto/x25519.c.s
.PHONY : src/crypto/x25519.c.s

src/network/packet.o: src/network/packet.c.o
.PHONY : src/network/packet.o

# target to build an object file
src/network/packet.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/src/network/packet.c.o
.PHONY : src/network/packet.c.o

src/network/packet.i: src/network/packet.c.i
.PHONY : src/network/packet.i

# target to preprocess a source file
src/network/packet.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/src/network/packet.c.i
.PHONY : src/network/packet.c.i

src/network/packet.s: src/network/packet.c.s
.PHONY : src/network/packet.s

# target to generate assembly for a file
src/network/packet.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/src/network/packet.c.s
.PHONY : src/network/packet.c.s

src/server/main.o: src/server/main.c.o
.PHONY : src/server/main.o

# target to build an object file
src/server/main.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/svpn_server.dir/build.make CMakeFiles/svpn_server.dir/src/server/main.c.o
.PHONY : src/server/main.c.o

src/server/main.i: src/server/main.c.i
.PHONY : src/server/main.i

# target to preprocess a source file
src/server/main.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/svpn_server.dir/build.make CMakeFiles/svpn_server.dir/src/server/main.c.i
.PHONY : src/server/main.c.i

src/server/main.s: src/server/main.c.s
.PHONY : src/server/main.s

# target to generate assembly for a file
src/server/main.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/svpn_server.dir/build.make CMakeFiles/svpn_server.dir/src/server/main.c.s
.PHONY : src/server/main.c.s

tools/keygen.o: tools/keygen.c.o
.PHONY : tools/keygen.o

# target to build an object file
tools/keygen.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/keygen.dir/build.make CMakeFiles/keygen.dir/tools/keygen.c.o
.PHONY : tools/keygen.c.o

tools/keygen.i: tools/keygen.c.i
.PHONY : tools/keygen.i

# target to preprocess a source file
tools/keygen.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/keygen.dir/build.make CMakeFiles/keygen.dir/tools/keygen.c.i
.PHONY : tools/keygen.c.i

tools/keygen.s: tools/keygen.c.s
.PHONY : tools/keygen.s

# target to generate assembly for a file
tools/keygen.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/keygen.dir/build.make CMakeFiles/keygen.dir/tools/keygen.c.s
.PHONY : tools/keygen.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... keygen"
	@echo "... securevpn_core"
	@echo "... svpn_client"
	@echo "... svpn_server"
	@echo "... src/auth/license.o"
	@echo "... src/auth/license.i"
	@echo "... src/auth/license.s"
	@echo "... src/client/main.o"
	@echo "... src/client/main.i"
	@echo "... src/client/main.s"
	@echo "... src/crypto/chacha20.o"
	@echo "... src/crypto/chacha20.i"
	@echo "... src/crypto/chacha20.s"
	@echo "... src/crypto/poly1305.o"
	@echo "... src/crypto/poly1305.i"
	@echo "... src/crypto/poly1305.s"
	@echo "... src/crypto/utils.o"
	@echo "... src/crypto/utils.i"
	@echo "... src/crypto/utils.s"
	@echo "... src/crypto/x25519.o"
	@echo "... src/crypto/x25519.i"
	@echo "... src/crypto/x25519.s"
	@echo "... src/network/packet.o"
	@echo "... src/network/packet.i"
	@echo "... src/network/packet.s"
	@echo "... src/server/main.o"
	@echo "... src/server/main.i"
	@echo "... src/server/main.s"
	@echo "... tools/keygen.o"
	@echo "... tools/keygen.i"
	@echo "... tools/keygen.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

