package com.securevpn;

import android.app.Activity;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.VpnService;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Spinner;
import android.widget.ArrayAdapter;
import android.widget.ProgressBar;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Main activity for Secure VPN Android client
 */
public class MainActivity extends AppCompatActivity {
    
    private static final int VPN_REQUEST_CODE = 1001;
    private static final String PREFS_NAME = "SecureVPNPrefs";
    
    // UI Components
    private Button connectButton;
    private TextView statusText;
    private TextView statsText;
    private Spinner serverSpinner;
    private ProgressBar progressBar;
    
    // VPN State
    private VpnState currentState = VpnState.DISCONNECTED;
    private VpnConfig config;
    private VpnStats stats;
    private Handler uiHandler;
    private ExecutorService executor;
    
    // Server list
    private List<VpnServer> servers;
    
    public enum VpnState {
        DISCONNECTED,
        CONNECTING,
        CONNECTED,
        DISCONNECTING,
        ERROR
    }
    
    public static class VpnServer {
        public String name;
        public String address;
        public int port;
        public String country;
        public String city;
        public boolean premiumOnly;
        
        public VpnServer(String name, String address, int port, String country, String city, boolean premiumOnly) {
            this.name = name;
            this.address = address;
            this.port = port;
            this.country = country;
            this.city = city;
            this.premiumOnly = premiumOnly;
        }
        
        @Override
        public String toString() {
            return name + " (" + country + ")";
        }
    }
    
    public static class VpnConfig {
        public String serverAddress = "demo.securevpn.com";
        public int serverPort = 8443;
        public String licenseKey = "";
        public String username = "";
        public String password = "";
        public boolean autoConnect = false;
        public boolean killSwitch = true;
        public String preferredProtocol = "auto";
    }
    
    public static class VpnStats {
        public long bytesSent = 0;
        public long bytesReceived = 0;
        public long connectedSince = 0;
        public String virtualIp = "";
        public String publicIp = "";
        public double connectionSpeedMbps = 0.0;
    }
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        // Initialize components
        initializeComponents();
        initializeServers();
        loadConfiguration();
        setupUI();
        
        // Initialize handlers
        uiHandler = new Handler(Looper.getMainLooper());
        executor = Executors.newSingleThreadExecutor();
        
        // Initialize state
        config = new VpnConfig();
        stats = new VpnStats();
        
        updateUI();
    }
    
    private void initializeComponents() {
        connectButton = findViewById(R.id.connectButton);
        statusText = findViewById(R.id.statusText);
        statsText = findViewById(R.id.statsText);
        serverSpinner = findViewById(R.id.serverSpinner);
        progressBar = findViewById(R.id.progressBar);
    }
    
    private void initializeServers() {
        servers = new ArrayList<>();
        servers.add(new VpnServer("US East (New York)", "us-east.securevpn.com", 8443, "United States", "New York", false));
        servers.add(new VpnServer("US West (Los Angeles)", "us-west.securevpn.com", 8443, "United States", "Los Angeles", false));
        servers.add(new VpnServer("UK (London)", "uk.securevpn.com", 8443, "United Kingdom", "London", false));
        servers.add(new VpnServer("Germany (Frankfurt)", "de.securevpn.com", 8443, "Germany", "Frankfurt", true));
        servers.add(new VpnServer("Japan (Tokyo)", "jp.securevpn.com", 8443, "Japan", "Tokyo", true));
        servers.add(new VpnServer("Australia (Sydney)", "au.securevpn.com", 8443, "Australia", "Sydney", true));
        servers.add(new VpnServer("Canada (Toronto)", "ca.securevpn.com", 8443, "Canada", "Toronto", false));
        servers.add(new VpnServer("Netherlands (Amsterdam)", "nl.securevpn.com", 8443, "Netherlands", "Amsterdam", true));
        servers.add(new VpnServer("Singapore", "sg.securevpn.com", 8443, "Singapore", "Singapore", true));
        servers.add(new VpnServer("Brazil (São Paulo)", "br.securevpn.com", 8443, "Brazil", "São Paulo", true));
    }
    
    private void setupUI() {
        // Setup server spinner
        ArrayAdapter<VpnServer> adapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, servers);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        serverSpinner.setAdapter(adapter);
        
        // Setup connect button
        connectButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                onConnectButtonClicked();
            }
        });
        
        // Setup server selection
        serverSpinner.setOnItemSelectedListener(new android.widget.AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(android.widget.AdapterView<?> parent, View view, int position, long id) {
                if (position < servers.size()) {
                    VpnServer selected = servers.get(position);
                    config.serverAddress = selected.address;
                    config.serverPort = selected.port;
                    saveConfiguration();
                }
            }
            
            @Override
            public void onNothingSelected(android.widget.AdapterView<?> parent) {}
        });
    }
    
    private void onConnectButtonClicked() {
        switch (currentState) {
            case DISCONNECTED:
            case ERROR:
                requestVpnPermission();
                break;
            case CONNECTED:
                disconnectVpn();
                break;
            case CONNECTING:
                cancelConnection();
                break;
            case DISCONNECTING:
                // Do nothing, already disconnecting
                break;
        }
    }
    
    private void requestVpnPermission() {
        Intent intent = VpnService.prepare(this);
        if (intent != null) {
            startActivityForResult(intent, VPN_REQUEST_CODE);
        } else {
            // Permission already granted
            connectVpn();
        }
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        
        if (requestCode == VPN_REQUEST_CODE) {
            if (resultCode == Activity.RESULT_OK) {
                connectVpn();
            } else {
                Toast.makeText(this, "VPN permission denied", Toast.LENGTH_SHORT).show();
            }
        }
    }
    
    private void connectVpn() {
        currentState = VpnState.CONNECTING;
        updateUI();
        
        // Start VPN connection in background thread
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    // Simulate connection process
                    for (int i = 0; i < 10; i++) {
                        Thread.sleep(1000);
                        
                        // Update progress on UI thread
                        final int progress = (i + 1) * 10;
                        uiHandler.post(new Runnable() {
                            @Override
                            public void run() {
                                progressBar.setProgress(progress);
                            }
                        });
                    }
                    
                    // Connection successful
                    stats.connectedSince = System.currentTimeMillis();
                    stats.virtualIp = "********";
                    stats.publicIp = "***********";
                    
                    uiHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            currentState = VpnState.CONNECTED;
                            updateUI();
                            startStatsUpdater();
                        }
                    });
                    
                } catch (InterruptedException e) {
                    // Connection cancelled
                    uiHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            currentState = VpnState.DISCONNECTED;
                            updateUI();
                        }
                    });
                } catch (Exception e) {
                    // Connection failed
                    uiHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            currentState = VpnState.ERROR;
                            updateUI();
                            Toast.makeText(MainActivity.this, "Connection failed: " + e.getMessage(), Toast.LENGTH_LONG).show();
                        }
                    });
                }
            }
        });
    }
    
    private void disconnectVpn() {
        currentState = VpnState.DISCONNECTING;
        updateUI();
        
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    Thread.sleep(2000); // Simulate disconnection time
                    
                    uiHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            currentState = VpnState.DISCONNECTED;
                            stats = new VpnStats(); // Reset stats
                            updateUI();
                        }
                    });
                } catch (InterruptedException e) {
                    // Handle interruption
                }
            }
        });
    }
    
    private void cancelConnection() {
        // Cancel ongoing connection
        currentState = VpnState.DISCONNECTED;
        updateUI();
    }
    
    private void updateUI() {
        String statusMessage;
        String buttonText;
        boolean buttonEnabled = true;
        int progressVisibility = View.GONE;
        
        switch (currentState) {
            case DISCONNECTED:
                statusMessage = "Disconnected";
                buttonText = "Connect";
                break;
            case CONNECTING:
                statusMessage = "Connecting...";
                buttonText = "Cancel";
                progressVisibility = View.VISIBLE;
                break;
            case CONNECTED:
                statusMessage = "Connected to " + config.serverAddress;
                buttonText = "Disconnect";
                break;
            case DISCONNECTING:
                statusMessage = "Disconnecting...";
                buttonText = "Disconnect";
                buttonEnabled = false;
                progressVisibility = View.VISIBLE;
                break;
            case ERROR:
                statusMessage = "Connection Error";
                buttonText = "Retry";
                break;
            default:
                statusMessage = "Unknown State";
                buttonText = "Connect";
                break;
        }
        
        statusText.setText(statusMessage);
        connectButton.setText(buttonText);
        connectButton.setEnabled(buttonEnabled);
        progressBar.setVisibility(progressVisibility);
        
        // Update connection color
        int color = ContextCompat.getColor(this, 
            currentState == VpnState.CONNECTED ? R.color.connected : R.color.disconnected);
        statusText.setTextColor(color);
        
        updateStatsDisplay();
    }
    
    private void updateStatsDisplay() {
        if (currentState == VpnState.CONNECTED) {
            long duration = (System.currentTimeMillis() - stats.connectedSince) / 1000;
            long hours = duration / 3600;
            long minutes = (duration % 3600) / 60;
            long seconds = duration % 60;
            
            double mbSent = stats.bytesSent / (1024.0 * 1024.0);
            double mbReceived = stats.bytesReceived / (1024.0 * 1024.0);
            
            String statsMessage = String.format(
                "Connected for: %02d:%02d:%02d\n" +
                "Data sent: %.2f MB\n" +
                "Data received: %.2f MB\n" +
                "Virtual IP: %s",
                hours, minutes, seconds,
                mbSent, mbReceived,
                stats.virtualIp
            );
            
            statsText.setText(statsMessage);
            statsText.setVisibility(View.VISIBLE);
        } else {
            statsText.setVisibility(View.GONE);
        }
    }
    
    private void startStatsUpdater() {
        Handler statsHandler = new Handler(Looper.getMainLooper());
        Runnable statsUpdater = new Runnable() {
            @Override
            public void run() {
                if (currentState == VpnState.CONNECTED) {
                    // Simulate traffic
                    stats.bytesSent += 1024 + (long)(Math.random() * 10240);
                    stats.bytesReceived += 2048 + (long)(Math.random() * 20480);
                    
                    updateStatsDisplay();
                    
                    // Schedule next update
                    statsHandler.postDelayed(this, 1000);
                }
            }
        };
        statsHandler.post(statsUpdater);
    }
    
    private void loadConfiguration() {
        SharedPreferences prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        config.serverAddress = prefs.getString("server_address", "demo.securevpn.com");
        config.serverPort = prefs.getInt("server_port", 8443);
        config.licenseKey = prefs.getString("license_key", "");
        config.username = prefs.getString("username", "");
        config.autoConnect = prefs.getBoolean("auto_connect", false);
        config.killSwitch = prefs.getBoolean("kill_switch", true);
        config.preferredProtocol = prefs.getString("preferred_protocol", "auto");
    }
    
    private void saveConfiguration() {
        SharedPreferences prefs = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString("server_address", config.serverAddress);
        editor.putInt("server_port", config.serverPort);
        editor.putString("license_key", config.licenseKey);
        editor.putString("username", config.username);
        editor.putBoolean("auto_connect", config.autoConnect);
        editor.putBoolean("kill_switch", config.killSwitch);
        editor.putString("preferred_protocol", config.preferredProtocol);
        editor.apply();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executor != null) {
            executor.shutdown();
        }
    }
}
