#ifndef ENHANCED_SECURITY_H
#define ENHANCED_SECURITY_H

#include <stdint.h>
#include <stdbool.h>
#include <time.h>
#include <netinet/in.h>

// Enhanced security features for commercial VPN
typedef enum {
    SECURITY_SUCCESS = 0,
    SECURITY_ERROR_INVALID_PARAM = -1,
    SECURITY_ERROR_RATE_LIMITED = -2,
    SECURITY_ERROR_BLOCKED = -3,
    SECURITY_ERROR_CRYPTO = -4,
    SECURITY_ERROR_MEMORY = -5
} security_error_t;

typedef enum {
    THREAT_LEVEL_LOW = 0,
    THREAT_LEVEL_MEDIUM = 1,
    THREAT_LEVEL_HIGH = 2,
    THREAT_LEVEL_CRITICAL = 3
} threat_level_t;

typedef enum {
    ATTACK_TYPE_BRUTE_FORCE = 0,
    ATTACK_TYPE_DOS = 1,
    ATTACK_TYPE_DDOS = 2,
    ATTACK_TYPE_PORT_SCAN = 3,
    ATTACK_TYPE_INVALID_AUTH = 4,
    ATTACK_TYPE_MALFORMED_PACKET = 5
} attack_type_t;

// Rate limiting configuration
typedef struct {
    uint32_t max_requests_per_minute;
    uint32_t max_requests_per_hour;
    uint32_t max_failed_auth_attempts;
    uint32_t ban_duration_seconds;
    bool enable_progressive_delays;
} rate_limit_config_t;

// IP-based rate limiting entry
typedef struct {
    struct sockaddr_in client_addr;
    uint32_t requests_this_minute;
    uint32_t requests_this_hour;
    uint32_t failed_auth_attempts;
    time_t last_request_time;
    time_t minute_window_start;
    time_t hour_window_start;
    time_t ban_until;
    bool is_banned;
    threat_level_t threat_level;
} rate_limit_entry_t;

// DDoS protection configuration
typedef struct {
    uint32_t max_connections_per_ip;
    uint32_t max_new_connections_per_second;
    uint32_t packet_rate_threshold;
    uint32_t bandwidth_threshold_mbps;
    bool enable_syn_flood_protection;
    bool enable_packet_inspection;
} ddos_config_t;

// Intrusion detection configuration
typedef struct {
    bool enable_port_scan_detection;
    bool enable_brute_force_detection;
    bool enable_anomaly_detection;
    uint32_t port_scan_threshold;
    uint32_t brute_force_threshold;
    uint32_t anomaly_score_threshold;
} ids_config_t;

// Security event log entry
typedef struct {
    uint64_t event_id;
    time_t timestamp;
    struct sockaddr_in source_addr;
    attack_type_t attack_type;
    threat_level_t threat_level;
    char description[256];
    uint32_t severity_score;
    bool action_taken;
    char action_description[128];
} security_event_t;

// Enhanced password security
typedef struct {
    uint32_t min_length;
    bool require_uppercase;
    bool require_lowercase;
    bool require_numbers;
    bool require_special_chars;
    uint32_t max_age_days;
    uint32_t history_count;
    uint32_t lockout_threshold;
    uint32_t lockout_duration_minutes;
} password_policy_t;

// Two-factor authentication
typedef struct {
    char secret[32];            // Base32 encoded secret
    uint32_t window_size;       // Time window for TOTP
    uint32_t time_step;         // Time step in seconds (usually 30)
    bool backup_codes_enabled;
    char backup_codes[10][16];  // 10 backup codes
    uint32_t used_backup_codes; // Bitmask of used codes
} totp_config_t;

// Security context for the entire system
typedef struct {
    rate_limit_config_t rate_limit_config;
    ddos_config_t ddos_config;
    ids_config_t ids_config;
    password_policy_t password_policy;
    
    // Runtime state
    rate_limit_entry_t *rate_limit_table;
    uint32_t rate_limit_table_size;
    security_event_t *event_log;
    uint32_t event_log_size;
    uint32_t event_log_count;
    
    // Statistics
    uint64_t total_blocked_requests;
    uint64_t total_security_events;
    uint64_t total_banned_ips;
    
    bool is_initialized;
} security_context_t;

// Initialize security system
security_error_t security_init(security_context_t *ctx, const rate_limit_config_t *rate_config,
                              const ddos_config_t *ddos_config, const ids_config_t *ids_config);
void security_cleanup(security_context_t *ctx);

// Rate limiting functions
security_error_t security_check_rate_limit(security_context_t *ctx, 
                                          const struct sockaddr_in *client_addr);
security_error_t security_record_failed_auth(security_context_t *ctx,
                                            const struct sockaddr_in *client_addr);
security_error_t security_ban_ip(security_context_t *ctx, const struct sockaddr_in *client_addr,
                                uint32_t duration_seconds, const char *reason);
bool security_is_ip_banned(security_context_t *ctx, const struct sockaddr_in *client_addr);

// DDoS protection
security_error_t security_check_ddos(security_context_t *ctx, const struct sockaddr_in *client_addr,
                                    uint32_t packet_size);
security_error_t security_update_connection_count(security_context_t *ctx,
                                                 const struct sockaddr_in *client_addr,
                                                 int delta);

// Intrusion detection
security_error_t security_detect_port_scan(security_context_t *ctx,
                                          const struct sockaddr_in *client_addr,
                                          uint16_t target_port);
security_error_t security_detect_brute_force(security_context_t *ctx,
                                            const struct sockaddr_in *client_addr);
security_error_t security_analyze_packet_anomaly(security_context_t *ctx,
                                                const struct sockaddr_in *client_addr,
                                                const uint8_t *packet_data, size_t packet_size);

// Event logging
security_error_t security_log_event(security_context_t *ctx, const struct sockaddr_in *source_addr,
                                   attack_type_t attack_type, threat_level_t threat_level,
                                   const char *description);
security_error_t security_get_recent_events(security_context_t *ctx, security_event_t *events,
                                           uint32_t max_events, uint32_t *count);

// Enhanced password functions
security_error_t security_validate_password(const char *password, const password_policy_t *policy);
security_error_t security_hash_password_bcrypt(const char *password, const char *salt,
                                              char *hash_out, size_t hash_size);
security_error_t security_verify_password_bcrypt(const char *password, const char *hash);
security_error_t security_generate_salt(char *salt_out, size_t salt_size);

// Two-factor authentication
security_error_t security_generate_totp_secret(totp_config_t *totp);
security_error_t security_generate_totp_code(const totp_config_t *totp, uint32_t *code);
security_error_t security_verify_totp_code(const totp_config_t *totp, uint32_t code);
security_error_t security_generate_backup_codes(totp_config_t *totp);
security_error_t security_use_backup_code(totp_config_t *totp, const char *code);

// Certificate and TLS security
security_error_t security_generate_self_signed_cert(const char *hostname, const char *cert_file,
                                                   const char *key_file);
security_error_t security_validate_certificate_chain(const char *cert_file, const char *ca_file);
security_error_t security_setup_tls_context(void **tls_ctx, const char *cert_file,
                                           const char *key_file);

// Secure memory functions
void* security_malloc_secure(size_t size);
void security_free_secure(void *ptr, size_t size);
security_error_t security_lock_memory(void *ptr, size_t size);
security_error_t security_unlock_memory(void *ptr, size_t size);

// Utility functions
const char* security_error_string(security_error_t error);
const char* threat_level_string(threat_level_t level);
const char* attack_type_string(attack_type_t type);
void security_get_client_ip_string(const struct sockaddr_in *addr, char *ip_str, size_t size);
uint32_t security_calculate_threat_score(const rate_limit_entry_t *entry);

// Statistics and monitoring
security_error_t security_get_statistics(security_context_t *ctx, char *stats_json, size_t size);
security_error_t security_export_threat_intelligence(security_context_t *ctx, const char *filename);

#endif // ENHANCED_SECURITY_H
