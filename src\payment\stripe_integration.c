#include "stripe_integration.h"
#include "securevpn.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <curl/curl.h>
#include <cjson/cJSON.h>
#include <openssl/hmac.h>
#include <openssl/sha.h>

// Global Stripe configuration
static stripe_config_t g_stripe_config = {0};
static bool g_stripe_initialized = false;

// Price configuration for different plans
static const stripe_price_config_t g_price_configs[] = {
    {"price_free", PLAN_FREE, 0.0, "usd", "month", 1},
    {"price_basic", PLAN_BASIC, 5.0, "usd", "month", 1},
    {"price_premium", PLAN_PREMIUM, 10.0, "usd", "month", 1},
    {"price_enterprise", PLAN_ENTERPRISE, 25.0, "usd", "month", 1}
};

// HTTP response structure for curl
typedef struct {
    char *data;
    size_t size;
} http_response_t;

// Callback function for curl to write response data
static size_t write_callback(void *contents, size_t size, size_t nmemb, http_response_t *response) {
    size_t total_size = size * nmemb;
    char *new_data = realloc(response->data, response->size + total_size + 1);
    
    if (!new_data) {
        return 0;
    }
    
    response->data = new_data;
    memcpy(&(response->data[response->size]), contents, total_size);
    response->size += total_size;
    response->data[response->size] = '\0';
    
    return total_size;
}

// Make HTTP request to Stripe API
static payment_error_t stripe_api_request(const char *endpoint, const char *method, 
                                         const char *data, http_response_t *response) {
    if (!g_stripe_initialized) {
        return PAYMENT_ERROR_AUTH;
    }
    
    CURL *curl;
    CURLcode res;
    struct curl_slist *headers = NULL;
    char auth_header[256];
    char url[512];
    
    curl = curl_easy_init();
    if (!curl) {
        return PAYMENT_ERROR_NETWORK;
    }
    
    // Build URL
    snprintf(url, sizeof(url), "https://api.stripe.com/v1/%s", endpoint);
    
    // Set authorization header
    snprintf(auth_header, sizeof(auth_header), "Authorization: Bearer %s", g_stripe_config.secret_key);
    headers = curl_slist_append(headers, auth_header);
    headers = curl_slist_append(headers, "Content-Type: application/x-www-form-urlencoded");
    headers = curl_slist_append(headers, "Stripe-Version: 2023-10-16");
    
    // Configure curl
    curl_easy_setopt(curl, CURLOPT_URL, url);
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, write_callback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, response);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 1L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 2L);
    
    if (strcmp(method, "POST") == 0) {
        curl_easy_setopt(curl, CURLOPT_POST, 1L);
        if (data) {
            curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data);
        }
    } else if (strcmp(method, "DELETE") == 0) {
        curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "DELETE");
    }
    
    // Initialize response
    response->data = malloc(1);
    response->size = 0;
    response->data[0] = '\0';
    
    // Perform request
    res = curl_easy_perform(curl);
    
    // Cleanup
    curl_slist_free_all(headers);
    curl_easy_cleanup(curl);
    
    if (res != CURLE_OK) {
        free(response->data);
        response->data = NULL;
        return PAYMENT_ERROR_NETWORK;
    }
    
    return PAYMENT_SUCCESS;
}

payment_error_t stripe_init(const stripe_config_t *config) {
    if (!config) {
        return PAYMENT_ERROR_INVALID_PARAM;
    }
    
    // Initialize curl globally
    curl_global_init(CURL_GLOBAL_DEFAULT);
    
    // Copy configuration
    memcpy(&g_stripe_config, config, sizeof(stripe_config_t));
    g_stripe_initialized = true;
    
    return PAYMENT_SUCCESS;
}

void stripe_cleanup(void) {
    if (g_stripe_initialized) {
        curl_global_cleanup();
        memset(&g_stripe_config, 0, sizeof(stripe_config_t));
        g_stripe_initialized = false;
    }
}

payment_error_t stripe_create_customer(const char *email, const char *name, 
                                      stripe_customer_t *customer) {
    if (!email || !customer) {
        return PAYMENT_ERROR_INVALID_PARAM;
    }
    
    char data[512];
    http_response_t response = {0};
    payment_error_t result;
    
    // Build request data
    snprintf(data, sizeof(data), "email=%s", email);
    if (name) {
        char encoded_name[256];
        // URL encode name (simplified)
        strncpy(encoded_name, name, sizeof(encoded_name) - 1);
        strncat(data, "&name=", sizeof(data) - strlen(data) - 1);
        strncat(data, encoded_name, sizeof(data) - strlen(data) - 1);
    }
    
    result = stripe_api_request("customers", "POST", data, &response);
    if (result != PAYMENT_SUCCESS) {
        return result;
    }
    
    // Parse JSON response
    cJSON *json = cJSON_Parse(response.data);
    if (!json) {
        free(response.data);
        return PAYMENT_ERROR_PROCESSING;
    }
    
    // Extract customer information
    cJSON *id = cJSON_GetObjectItem(json, "id");
    cJSON *email_json = cJSON_GetObjectItem(json, "email");
    cJSON *created = cJSON_GetObjectItem(json, "created");
    
    if (id && cJSON_IsString(id)) {
        strncpy(customer->customer_id, cJSON_GetStringValue(id), sizeof(customer->customer_id) - 1);
    }
    if (email_json && cJSON_IsString(email_json)) {
        strncpy(customer->email, cJSON_GetStringValue(email_json), sizeof(customer->email) - 1);
    }
    if (name) {
        strncpy(customer->name, name, sizeof(customer->name) - 1);
    }
    if (created && cJSON_IsNumber(created)) {
        customer->created_at = (time_t)cJSON_GetNumberValue(created);
    }
    customer->is_active = true;
    
    cJSON_Delete(json);
    free(response.data);
    
    return PAYMENT_SUCCESS;
}

payment_error_t stripe_create_subscription(const char *customer_id, const char *price_id,
                                          stripe_subscription_t *subscription) {
    if (!customer_id || !price_id || !subscription) {
        return PAYMENT_ERROR_INVALID_PARAM;
    }
    
    char data[512];
    http_response_t response = {0};
    payment_error_t result;
    
    // Build request data
    snprintf(data, sizeof(data), "customer=%s&items[0][price]=%s", customer_id, price_id);
    
    result = stripe_api_request("subscriptions", "POST", data, &response);
    if (result != PAYMENT_SUCCESS) {
        return result;
    }
    
    // Parse JSON response
    cJSON *json = cJSON_Parse(response.data);
    if (!json) {
        free(response.data);
        return PAYMENT_ERROR_PROCESSING;
    }
    
    // Extract subscription information
    cJSON *id = cJSON_GetObjectItem(json, "id");
    cJSON *customer = cJSON_GetObjectItem(json, "customer");
    cJSON *status = cJSON_GetObjectItem(json, "status");
    cJSON *current_period_start = cJSON_GetObjectItem(json, "current_period_start");
    cJSON *current_period_end = cJSON_GetObjectItem(json, "current_period_end");
    
    if (id && cJSON_IsString(id)) {
        strncpy(subscription->subscription_id, cJSON_GetStringValue(id), 
                sizeof(subscription->subscription_id) - 1);
    }
    if (customer && cJSON_IsString(customer)) {
        strncpy(subscription->customer_id, cJSON_GetStringValue(customer), 
                sizeof(subscription->customer_id) - 1);
    }
    strncpy(subscription->price_id, price_id, sizeof(subscription->price_id) - 1);
    
    if (status && cJSON_IsString(status)) {
        const char *status_str = cJSON_GetStringValue(status);
        if (strcmp(status_str, "active") == 0) {
            subscription->status = SUBSCRIPTION_STATUS_ACTIVE;
        } else if (strcmp(status_str, "past_due") == 0) {
            subscription->status = SUBSCRIPTION_STATUS_PAST_DUE;
        } else if (strcmp(status_str, "canceled") == 0) {
            subscription->status = SUBSCRIPTION_STATUS_CANCELED;
        } else {
            subscription->status = SUBSCRIPTION_STATUS_INCOMPLETE;
        }
    }
    
    if (current_period_start && cJSON_IsNumber(current_period_start)) {
        subscription->current_period_start = (time_t)cJSON_GetNumberValue(current_period_start);
    }
    if (current_period_end && cJSON_IsNumber(current_period_end)) {
        subscription->current_period_end = (time_t)cJSON_GetNumberValue(current_period_end);
    }
    
    // Get amount from price configuration
    for (size_t i = 0; i < sizeof(g_price_configs) / sizeof(g_price_configs[0]); i++) {
        if (strcmp(g_price_configs[i].price_id, price_id) == 0) {
            subscription->amount = g_price_configs[i].amount;
            strncpy(subscription->currency, g_price_configs[i].currency, 
                    sizeof(subscription->currency) - 1);
            break;
        }
    }
    
    cJSON_Delete(json);
    free(response.data);
    
    return PAYMENT_SUCCESS;
}

payment_error_t stripe_verify_webhook_signature(const char *payload, const char *signature,
                                               const char *webhook_secret) {
    if (!payload || !signature || !webhook_secret) {
        return PAYMENT_ERROR_INVALID_PARAM;
    }
    
    // Extract timestamp and signature from header
    const char *timestamp_prefix = "t=";
    const char *signature_prefix = "v1=";
    
    char *timestamp_str = strstr(signature, timestamp_prefix);
    char *signature_str = strstr(signature, signature_prefix);
    
    if (!timestamp_str || !signature_str) {
        return PAYMENT_ERROR_WEBHOOK;
    }
    
    timestamp_str += strlen(timestamp_prefix);
    signature_str += strlen(signature_prefix);
    
    // Extract timestamp
    char timestamp_buf[32];
    char *comma = strchr(timestamp_str, ',');
    if (comma) {
        size_t len = comma - timestamp_str;
        if (len >= sizeof(timestamp_buf)) len = sizeof(timestamp_buf) - 1;
        strncpy(timestamp_buf, timestamp_str, len);
        timestamp_buf[len] = '\0';
    } else {
        strncpy(timestamp_buf, timestamp_str, sizeof(timestamp_buf) - 1);
    }
    
    // Extract signature
    char signature_buf[128];
    comma = strchr(signature_str, ',');
    if (comma) {
        size_t len = comma - signature_str;
        if (len >= sizeof(signature_buf)) len = sizeof(signature_buf) - 1;
        strncpy(signature_buf, signature_str, len);
        signature_buf[len] = '\0';
    } else {
        strncpy(signature_buf, signature_str, sizeof(signature_buf) - 1);
    }
    
    // Create signed payload
    char signed_payload[2048];
    snprintf(signed_payload, sizeof(signed_payload), "%s.%s", timestamp_buf, payload);
    
    // Compute HMAC-SHA256
    unsigned char computed_signature[SHA256_DIGEST_LENGTH];
    unsigned int signature_len;
    
    HMAC(EVP_sha256(), webhook_secret, strlen(webhook_secret),
         (unsigned char*)signed_payload, strlen(signed_payload),
         computed_signature, &signature_len);
    
    // Convert to hex string
    char computed_hex[SHA256_DIGEST_LENGTH * 2 + 1];
    for (int i = 0; i < SHA256_DIGEST_LENGTH; i++) {
        sprintf(&computed_hex[i * 2], "%02x", computed_signature[i]);
    }
    
    // Compare signatures
    if (strcmp(computed_hex, signature_buf) != 0) {
        return PAYMENT_ERROR_WEBHOOK;
    }
    
    return PAYMENT_SUCCESS;
}

const char* payment_error_string(payment_error_t error) {
    switch (error) {
        case PAYMENT_SUCCESS:
            return "Success";
        case PAYMENT_ERROR_INVALID_PARAM:
            return "Invalid parameter";
        case PAYMENT_ERROR_NETWORK:
            return "Network error";
        case PAYMENT_ERROR_AUTH:
            return "Authentication error";
        case PAYMENT_ERROR_DECLINED:
            return "Payment declined";
        case PAYMENT_ERROR_INSUFFICIENT_FUNDS:
            return "Insufficient funds";
        case PAYMENT_ERROR_EXPIRED_CARD:
            return "Expired card";
        case PAYMENT_ERROR_INVALID_CARD:
            return "Invalid card";
        case PAYMENT_ERROR_PROCESSING:
            return "Processing error";
        case PAYMENT_ERROR_WEBHOOK:
            return "Webhook verification failed";
        default:
            return "Unknown error";
    }
}

double stripe_get_plan_price(subscription_plan_t plan) {
    for (size_t i = 0; i < sizeof(g_price_configs) / sizeof(g_price_configs[0]); i++) {
        if (g_price_configs[i].plan == plan) {
            return g_price_configs[i].amount;
        }
    }
    return 0.0;
}

const char* stripe_get_plan_price_id(subscription_plan_t plan) {
    for (size_t i = 0; i < sizeof(g_price_configs) / sizeof(g_price_configs[0]); i++) {
        if (g_price_configs[i].plan == plan) {
            return g_price_configs[i].price_id;
        }
    }
    return NULL;
}
