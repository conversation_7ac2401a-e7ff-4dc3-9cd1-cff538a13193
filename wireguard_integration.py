#!/usr/bin/env python3
"""
WireGuard Integration for Secure VPN
Generates WireGuard server configs and client profiles
"""

import os
import subprocess
import sqlite3
import secrets
import time
import ipaddress
from pathlib import Path

class WireGuardManager:
    def __init__(self, base_dir="wireguard"):
        self.base_dir = Path(base_dir)
        self.server_dir = self.base_dir / "server"
        self.client_dir = self.base_dir / "clients"
        self.db_file = "vpn_production.db"
        
        # Create directories
        for dir_path in [self.server_dir, self.client_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # WireGuard network configuration
        self.server_network = ipaddress.IPv4Network('**********/24')
        self.server_ip = self.server_network.network_address + 1  # **********
        self.client_ip_start = 2  # Start client IPs from **********
    
    def generate_keypair(self):
        """Generate WireGuard private/public key pair"""
        try:
            # Generate private key
            private_key = subprocess.run(
                ["wg", "genkey"], 
                capture_output=True, text=True, check=True
            ).stdout.strip()
            
            # Generate public key from private key
            public_key = subprocess.run(
                ["wg", "pubkey"], 
                input=private_key, capture_output=True, text=True, check=True
            ).stdout.strip()
            
            return private_key, public_key
            
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ WireGuard tools not found. Please install WireGuard first.")
            raise
    
    def setup_server_keys(self):
        """Set up server keys"""
        server_private_key_file = self.server_dir / "private.key"
        server_public_key_file = self.server_dir / "public.key"
        
        if server_private_key_file.exists():
            # Load existing keys
            with open(server_private_key_file, 'r') as f:
                private_key = f.read().strip()
            with open(server_public_key_file, 'r') as f:
                public_key = f.read().strip()
            print("✅ Server keys already exist")
        else:
            # Generate new keys
            private_key, public_key = self.generate_keypair()
            
            with open(server_private_key_file, 'w') as f:
                f.write(private_key)
            with open(server_public_key_file, 'w') as f:
                f.write(public_key)
            
            # Set secure permissions
            os.chmod(server_private_key_file, 0o600)
            os.chmod(server_public_key_file, 0o644)
            
            print("✅ Server keys generated")
        
        return private_key, public_key
    
    def create_server_config(self, server_port=51820, server_endpoint="your-vpn-server.com"):
        """Create WireGuard server configuration"""
        private_key, public_key = self.setup_server_keys()
        
        config_file = self.server_dir / "wg0.conf"
        
        config = f"""# Secure VPN WireGuard Server Configuration
# Generated on: {time.strftime('%Y-%m-%d %H:%M:%S')}

[Interface]
PrivateKey = {private_key}
Address = {self.server_ip}/24
ListenPort = {server_port}
SaveConfig = false

# Enable IP forwarding and NAT
PostUp = iptables -A FORWARD -i wg0 -j ACCEPT; iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE
PostDown = iptables -D FORWARD -i wg0 -j ACCEPT; iptables -t nat -D POSTROUTING -o eth0 -j MASQUERADE

# DNS
DNS = *******, *******

# Clients will be added here automatically
"""
        
        with open(config_file, 'w') as f:
            f.write(config)
        
        # Set secure permissions
        os.chmod(config_file, 0o600)
        
        print(f"✅ Server config created: {config_file}")
        return config_file, public_key
    
    def get_next_client_ip(self):
        """Get next available client IP"""
        # Check database for existing client IPs
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            # Create wireguard_clients table if it doesn't exist
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS wireguard_clients (
                    client_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    email TEXT,
                    client_ip TEXT UNIQUE,
                    private_key TEXT,
                    public_key TEXT,
                    created_at INTEGER DEFAULT (strftime('%s', 'now')),
                    is_active INTEGER DEFAULT 1,
                    FOREIGN KEY (user_id) REFERENCES users (user_id)
                )
            ''')
            
            # Get highest used IP
            cursor.execute('SELECT MAX(CAST(SUBSTR(client_ip, 10, 3) AS INTEGER)) FROM wireguard_clients')
            result = cursor.fetchone()[0]
            
            if result:
                next_ip_num = result + 1
            else:
                next_ip_num = self.client_ip_start
            
            conn.close()
            
            # Generate IP address
            client_ip = self.server_network.network_address + next_ip_num
            return str(client_ip)
            
        except Exception as e:
            print(f"Warning: Could not check database for IPs: {e}")
            # Fallback to simple increment
            return str(self.server_network.network_address + self.client_ip_start)
    
    def create_client_config(self, email, server_endpoint="your-vpn-server.com", server_port=51820):
        """Create WireGuard client configuration"""
        # Get user info from database
        try:
            conn = sqlite3.connect(self.db_file)
            cursor = conn.cursor()
            
            cursor.execute('SELECT user_id, plan FROM users WHERE email = ? AND is_active = 1', (email,))
            user_info = cursor.fetchone()
            
            if not user_info:
                print(f"❌ User {email} not found or inactive")
                return None
            
            user_id, plan = user_info
            
            # Check if client already exists
            cursor.execute('SELECT client_ip, private_key, public_key FROM wireguard_clients WHERE user_id = ? AND is_active = 1', (user_id,))
            existing_client = cursor.fetchone()
            
            if existing_client:
                client_ip, private_key, public_key = existing_client
                print(f"✅ Using existing WireGuard config for {email}")
            else:
                # Generate new client keys
                private_key, public_key = self.generate_keypair()
                client_ip = self.get_next_client_ip()
                
                # Store in database
                cursor.execute('''
                    INSERT INTO wireguard_clients (user_id, email, client_ip, private_key, public_key)
                    VALUES (?, ?, ?, ?, ?)
                ''', (user_id, email, client_ip, private_key, public_key))
                
                conn.commit()
                print(f"✅ New WireGuard config created for {email}")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ Database error: {e}")
            return None
        
        # Get server public key
        server_public_key_file = self.server_dir / "public.key"
        if not server_public_key_file.exists():
            print("❌ Server not set up. Run setup_server() first.")
            return None
        
        with open(server_public_key_file, 'r') as f:
            server_public_key = f.read().strip()
        
        # Create client config
        username = email.split('@')[0]
        config_file = self.client_dir / f"{username}.conf"
        
        config = f"""# Secure VPN WireGuard Client Configuration
# User: {email}
# Plan: {plan}
# Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}

[Interface]
PrivateKey = {private_key}
Address = {client_ip}/24
DNS = *******, *******

[Peer]
PublicKey = {server_public_key}
Endpoint = {server_endpoint}:{server_port}
AllowedIPs = 0.0.0.0/0
PersistentKeepalive = 25
"""
        
        with open(config_file, 'w') as f:
            f.write(config)
        
        # Set secure permissions
        os.chmod(config_file, 0o600)
        
        # Update server config to include this client
        self.add_client_to_server(email, public_key, client_ip)
        
        print(f"✅ Client config created: {config_file}")
        return config_file
    
    def add_client_to_server(self, email, client_public_key, client_ip):
        """Add client to server configuration"""
        server_config_file = self.server_dir / "wg0.conf"
        
        if not server_config_file.exists():
            print("❌ Server config not found")
            return
        
        # Read current config
        with open(server_config_file, 'r') as f:
            config = f.read()
        
        # Check if client already exists
        if client_public_key in config:
            print(f"✅ Client {email} already in server config")
            return
        
        # Add client peer
        client_section = f"""
# Client: {email}
[Peer]
PublicKey = {client_public_key}
AllowedIPs = {client_ip}/32
"""
        
        config += client_section
        
        # Write updated config
        with open(server_config_file, 'w') as f:
            f.write(config)
        
        print(f"✅ Added {email} to server config")
    
    def create_qr_code(self, config_file):
        """Create QR code for mobile clients"""
        try:
            import qrcode
            
            with open(config_file, 'r') as f:
                config_content = f.read()
            
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(config_content)
            qr.make(fit=True)
            
            qr_file = config_file.with_suffix('.png')
            img = qr.make_image(fill_color="black", back_color="white")
            img.save(qr_file)
            
            print(f"✅ QR code created: {qr_file}")
            return qr_file
            
        except ImportError:
            print("⚠️  QR code generation requires 'qrcode' package: pip install qrcode[pil]")
            return None
    
    def setup_complete_server(self, server_endpoint="localhost", server_port=51820):
        """Set up complete WireGuard server"""
        print("🚀 Setting up WireGuard server...")
        
        # Check if WireGuard is installed
        try:
            subprocess.run(["wg", "--version"], check=True, capture_output=True)
            print("✅ WireGuard found")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ WireGuard not found. Please install WireGuard first.")
            print("Windows: https://www.wireguard.com/install/")
            print("Linux: sudo apt install wireguard")
            return None
        
        config_file, server_public_key = self.create_server_config(server_port, server_endpoint)
        
        # Create startup script
        startup_script = self.server_dir / "start_server.sh"
        script_content = f"""#!/bin/bash
# WireGuard Server Startup Script

echo "Starting WireGuard server..."

# Enable IP forwarding
echo 1 > /proc/sys/net/ipv4/ip_forward

# Start WireGuard interface
wg-quick up {config_file}

echo "WireGuard server started on port {server_port}"
echo "Server public key: {server_public_key}"
"""
        
        with open(startup_script, 'w') as f:
            f.write(script_content)
        
        os.chmod(startup_script, 0o755)
        
        print("\n✅ WireGuard server setup complete!")
        print(f"📁 Server config: {config_file}")
        print(f"🚀 Startup script: {startup_script}")
        print(f"🔑 Server public key: {server_public_key}")
        
        return config_file
    
    def create_user_profile(self, email, server_endpoint="your-vpn-server.com"):
        """Create complete user profile with WireGuard config"""
        config_file = self.create_client_config(email, server_endpoint)
        
        if not config_file:
            return None
        
        # Create QR code for mobile
        qr_file = self.create_qr_code(config_file)
        
        # Create instructions
        username = email.split('@')[0]
        instructions_file = self.client_dir / f"{username}_instructions.txt"
        
        instructions = f"""
Secure VPN - WireGuard Setup Instructions
=========================================

User: {email}
Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}

STEP 1: Install WireGuard Client
- Windows: https://www.wireguard.com/install/
- macOS: App Store or https://www.wireguard.com/install/
- Linux: sudo apt install wireguard (Ubuntu/Debian)
- Android: WireGuard (Google Play)
- iOS: WireGuard (App Store)

STEP 2: Import Configuration

OPTION A - Desktop (Windows/macOS/Linux):
1. Open WireGuard application
2. Click "Import tunnel(s) from file"
3. Select the file: {username}.conf
4. Click "Activate"

OPTION B - Mobile (Android/iOS):
1. Open WireGuard app
2. Tap the "+" button
3. Choose "Scan from QR code"
4. Scan the QR code: {username}.png
5. Tap "Save"

STEP 3: Connect
- Toggle the tunnel ON in WireGuard app
- You should see data transfer statistics

FEATURES:
- Modern cryptography (ChaCha20, Poly1305)
- Better performance than OpenVPN
- Lower battery usage on mobile
- Automatic reconnection
- No username/password needed (key-based)

TROUBLESHOOTING:
- Ensure port {51820}/UDP is not blocked
- Check server endpoint is correct
- Contact support: <EMAIL>

Your WireGuard profile is ready to use!
"""
        
        with open(instructions_file, 'w') as f:
            f.write(instructions)
        
        print(f"✅ User profile created for {email}")
        print(f"📄 Config file: {config_file}")
        print(f"📱 QR code: {qr_file}")
        print(f"📋 Instructions: {instructions_file}")
        
        return config_file, qr_file, instructions_file

def main():
    """Main setup function"""
    print("⚡ Secure VPN - WireGuard Integration Setup")
    print("=" * 50)
    
    manager = WireGuardManager()
    
    try:
        # Setup server
        server_config = manager.setup_complete_server()
        
        if server_config:
            print("\n🎯 Next Steps:")
            print("1. Start WireGuard server:")
            print(f"   sudo wg-quick up {server_config}")
            print("   # Or use the startup script")
            
            print("\n2. Configure firewall:")
            print("   sudo ufw allow 51820/udp")
            
            print("\n3. Create client profiles:")
            print("   python wireguard_integration.py --create-profile <EMAIL>")
            
            # Create sample client profile
            print("\n📱 Creating sample client profile...")
            result = manager.create_user_profile("<EMAIL>", "localhost")
            
            if result:
                config_file, qr_file, instructions = result
                print(f"\n✅ Sample profile created!")
                print(f"📄 Config: {config_file}")
                print(f"📱 QR Code: {qr_file}")
                print(f"📋 Instructions: {instructions}")
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--create-profile":
        if len(sys.argv) < 3:
            print("Usage: python wireguard_integration.py --create-profile <EMAIL>")
            sys.exit(1)
        
        email = sys.argv[2]
        manager = WireGuardManager()
        manager.create_user_profile(email)
    else:
        main()
