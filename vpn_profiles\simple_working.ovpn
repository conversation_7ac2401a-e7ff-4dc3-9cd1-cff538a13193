# Simple OpenVPN Config - Should Work!
# Username: <EMAIL>
# Password: password

client
dev tun
proto tcp
remote ************* 8090
port 8090
resolv-retry infinite
nobind
persist-key
persist-tun

# Authentication
auth-user-pass

# Minimal settings to avoid errors
cipher AES-256-CBC
auth SHA1

# Disable certificate verification
verify-x509-name none
remote-cert-tls none

# Logging
verb 3

# Compatibility
route-method exe
route-delay 2
