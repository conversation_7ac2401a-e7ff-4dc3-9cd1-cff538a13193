#include <stdio.h>
#include "securevpn.h"
#include <string.h>
#include <stdio.h>

// Simple RSA implementation for license verification
// Note: This is a simplified version for demonstration

typedef struct {
    uint8_t n[RSA_KEY_SIZE];    // Modulus
    uint8_t e[4];               // Public exponent (usually 65537)
    uint8_t d[RSA_KEY_SIZE];    // Private exponent (for signing)
} rsa_key_t;

// Modular exponentiation using binary method
static void mod_exp(uint8_t *result, const uint8_t *base, const uint8_t *exp, 
                   const uint8_t *mod, size_t key_size) {
    // Simplified implementation - in production use a proper big integer library
    // This is just a placeholder for the concept
    memset(result, 0, key_size);
    result[key_size - 1] = 1;  // result = 1
}

// Hash function for license data (simplified SHA-256 equivalent)
static void hash_license_data(const license_t *license, uint8_t *hash) {
    // In production, use a proper cryptographic hash function like SHA-256
    // This is a simplified version for demonstration
    uint8_t data[256];
    size_t len = 0;
    
    // Serialize license data (excluding signature)
    memcpy(data + len, license->user_id, sizeof(license->user_id));
    len += sizeof(license->user_id);
    
    memcpy(data + len, &license->issued_at, sizeof(license->issued_at));
    len += sizeof(license->issued_at);
    
    memcpy(data + len, &license->expires_at, sizeof(license->expires_at));
    len += sizeof(license->expires_at);
    
    memcpy(data + len, &license->max_connections, sizeof(license->max_connections));
    len += sizeof(license->max_connections);
    
    // Simple hash (in production use SHA-256)
    memset(hash, 0, 32);
    for (size_t i = 0; i < len; i++) {
        hash[i % 32] ^= data[i];
    }
}

svpn_error_t license_validate(const license_t *license, const uint8_t *public_key) {
    if (!license || !public_key) {
        return SVPN_ERROR_INVALID_PARAM;
    }
    
    time_t current_time = time(NULL);
    uint8_t hash[32];
    uint8_t decrypted_hash[RSA_KEY_SIZE];
    rsa_key_t key;
    
    // Check if license has expired
    if (current_time > license->expires_at) {
        return SVPN_ERROR_LICENSE_EXPIRED;
    }
    
    // Check if license is not yet valid
    if (current_time < license->issued_at) {
        return SVPN_ERROR_LICENSE_INVALID;
    }
    
    // Parse public key (simplified)
    memcpy(key.n, public_key, RSA_KEY_SIZE);
    key.e[0] = 0x01; key.e[1] = 0x00; key.e[2] = 0x01; key.e[3] = 0x00; // 65537
    
    // Hash the license data
    hash_license_data(license, hash);
    
    // Verify signature using RSA public key
    mod_exp(decrypted_hash, license->signature, key.e, key.n, RSA_KEY_SIZE);
    
    // Compare hashes (simplified - in production use proper PKCS#1 padding)
    if (memcmp(hash, decrypted_hash + RSA_KEY_SIZE - 32, 32) != 0) {
        return SVPN_ERROR_AUTH_FAIL;
    }
    
    return SVPN_SUCCESS;
}

svpn_error_t license_generate(license_t *license, const uint8_t *private_key, 
                             const char *user_id, time_t duration) {
    if (!license || !private_key || !user_id) {
        return SVPN_ERROR_INVALID_PARAM;
    }
    
    time_t current_time = time(NULL);
    uint8_t hash[32];
    uint8_t padded_hash[RSA_KEY_SIZE];
    rsa_key_t key;
    
    // Clear license structure
    memset(license, 0, sizeof(license_t));
    
    // Set license fields
    strncpy(license->user_id, user_id, sizeof(license->user_id) - 1);
    license->issued_at = current_time;
    license->expires_at = current_time + duration;
    license->max_connections = 5;  // Default value
    
    // Parse private key (simplified)
    memcpy(key.d, private_key, RSA_KEY_SIZE);
    memcpy(key.n, private_key + RSA_KEY_SIZE, RSA_KEY_SIZE);  // Assume n follows d
    
    // Hash the license data
    hash_license_data(license, hash);
    
    // Prepare padded hash for signing (simplified PKCS#1 v1.5 padding)
    memset(padded_hash, 0xFF, RSA_KEY_SIZE);
    padded_hash[0] = 0x00;
    padded_hash[1] = 0x01;
    padded_hash[RSA_KEY_SIZE - 33] = 0x00;
    memcpy(padded_hash + RSA_KEY_SIZE - 32, hash, 32);
    
    // Sign using RSA private key
    mod_exp(license->signature, padded_hash, key.d, key.n, RSA_KEY_SIZE);
    
    return SVPN_SUCCESS;
}

// Helper function to load license from file
svpn_error_t license_load_from_file(license_t *license, const char *filename) {
    if (!license || !filename) {
        return SVPN_ERROR_INVALID_PARAM;
    }
    
    FILE *file = fopen(filename, "rb");
    if (!file) {
        return SVPN_ERROR_SYSTEM;
    }
    
    size_t read_bytes = fread(license, 1, sizeof(license_t), file);
    fclose(file);
    
    if (read_bytes != sizeof(license_t)) {
        return SVPN_ERROR_SYSTEM;
    }
    
    return SVPN_SUCCESS;
}

// Helper function to save license to file
svpn_error_t license_save_to_file(const license_t *license, const char *filename) {
    if (!license || !filename) {
        return SVPN_ERROR_INVALID_PARAM;
    }
    
    FILE *file = fopen(filename, "wb");
    if (!file) {
        return SVPN_ERROR_SYSTEM;
    }
    
    size_t written_bytes = fwrite(license, 1, sizeof(license_t), file);
    fclose(file);
    
    if (written_bytes != sizeof(license_t)) {
        return SVPN_ERROR_SYSTEM;
    }
    
    return SVPN_SUCCESS;
}
