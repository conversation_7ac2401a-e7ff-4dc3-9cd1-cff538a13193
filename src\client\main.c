#include "securevpn.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <signal.h>
#include <errno.h>

typedef struct {
    char server_ip[16];
    uint16_t server_port;
    char license_file[256];
    char config_file[256];
    bool verbose;
} client_config_t;

static volatile bool running = true;
static int client_socket = -1;

static void signal_handler(int sig) {
    (void)sig;
    running = false;
    if (client_socket >= 0) {
        close(client_socket);
    }
}

static void print_usage(const char *program_name) {
    printf("Secure VPN Client v%d.%d.%d\n", 
           SVPN_VERSION_MAJOR, SVPN_VERSION_MINOR, SVPN_VERSION_PATCH);
    printf("Usage: %s [options]\n", program_name);
    printf("Options:\n");
    printf("  -s <server_ip>    Server IP address (default: 127.0.0.1)\n");
    printf("  -p <port>         Server port (default: 8443)\n");
    printf("  -l <license_file> License file path (default: client.lic)\n");
    printf("  -c <config_file>  Configuration file path\n");
    printf("  -v                Verbose output\n");
    printf("  -h                Show this help\n");
}

static svpn_error_t parse_arguments(int argc, char *argv[], client_config_t *config) {
    int opt;
    
    // Set defaults
    strcpy(config->server_ip, "127.0.0.1");
    config->server_port = 8443;
    strcpy(config->license_file, "client.lic");
    config->config_file[0] = '\0';
    config->verbose = false;
    
    while ((opt = getopt(argc, argv, "s:p:l:c:vh")) != -1) {
        switch (opt) {
            case 's':
                strncpy(config->server_ip, optarg, sizeof(config->server_ip) - 1);
                break;
            case 'p':
                config->server_port = (uint16_t)atoi(optarg);
                break;
            case 'l':
                strncpy(config->license_file, optarg, sizeof(config->license_file) - 1);
                break;
            case 'c':
                strncpy(config->config_file, optarg, sizeof(config->config_file) - 1);
                break;
            case 'v':
                config->verbose = true;
                break;
            case 'h':
                print_usage(argv[0]);
                exit(0);
            default:
                return SVPN_ERROR_INVALID_PARAM;
        }
    }
    
    return SVPN_SUCCESS;
}

static svpn_error_t connect_to_server(const client_config_t *config, int *sock) {
    struct sockaddr_in server_addr;
    int socket_fd;
    
    // Create socket
    socket_fd = socket(AF_INET, SOCK_DGRAM, 0);
    if (socket_fd < 0) {
        perror("socket");
        return SVPN_ERROR_NETWORK_FAIL;
    }
    
    // Setup server address
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(config->server_port);
    
    if (inet_pton(AF_INET, config->server_ip, &server_addr.sin_addr) <= 0) {
        close(socket_fd);
        return SVPN_ERROR_INVALID_PARAM;
    }
    
    // Connect to server
    if (connect(socket_fd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        perror("connect");
        close(socket_fd);
        return SVPN_ERROR_NETWORK_FAIL;
    }
    
    *sock = socket_fd;
    return SVPN_SUCCESS;
}

static svpn_error_t perform_handshake(int sock, const license_t *license) {
    vpn_packet_t packet;
    x25519_keypair_t client_keypair;
    uint8_t shared_secret[X25519_KEY_SIZE];
    svpn_error_t result;
    ssize_t bytes_sent, bytes_received;
    
    // Generate client keypair
    result = x25519_generate_keypair(&client_keypair);
    if (result != SVPN_SUCCESS) {
        return result;
    }
    
    // Create handshake packet with client public key
    result = packet_create(&packet, PKT_TYPE_HANDSHAKE, 
                          client_keypair.public_key, X25519_KEY_SIZE);
    if (result != SVPN_SUCCESS) {
        return result;
    }
    
    // Send handshake packet
    bytes_sent = send(sock, &packet, VPN_HEADER_SIZE + X25519_KEY_SIZE, 0);
    if (bytes_sent < 0) {
        perror("send handshake");
        return SVPN_ERROR_NETWORK_FAIL;
    }
    
    printf("Sent handshake packet (%zd bytes)\n", bytes_sent);
    
    // Receive server response
    bytes_received = recv(sock, &packet, sizeof(packet), 0);
    if (bytes_received < 0) {
        perror("recv handshake response");
        return SVPN_ERROR_NETWORK_FAIL;
    }
    
    printf("Received handshake response (%zd bytes)\n", bytes_received);
    
    // Verify response packet type
    if (ntohs(packet.type) != PKT_TYPE_HANDSHAKE) {
        return SVPN_ERROR_NETWORK_FAIL;
    }
    
    // Extract server public key and compute shared secret
    if (ntohs(packet.payload_len) != X25519_KEY_SIZE) {
        return SVPN_ERROR_NETWORK_FAIL;
    }
    
    result = x25519_shared_secret(client_keypair.private_key, 
                                 packet.payload, shared_secret);
    if (result != SVPN_SUCCESS) {
        return result;
    }
    
    printf("Handshake completed successfully\n");
    
    // Now send authentication packet with license
    result = packet_create(&packet, PKT_TYPE_AUTH, 
                          (uint8_t*)license, sizeof(license_t));
    if (result != SVPN_SUCCESS) {
        return result;
    }
    
    // Encrypt authentication packet
    result = packet_encrypt(&packet, shared_secret);
    if (result != SVPN_SUCCESS) {
        return result;
    }
    
    // Send authentication packet
    bytes_sent = send(sock, &packet, VPN_HEADER_SIZE + sizeof(license_t), 0);
    if (bytes_sent < 0) {
        perror("send auth");
        return SVPN_ERROR_NETWORK_FAIL;
    }
    
    printf("Sent authentication packet (%zd bytes)\n", bytes_sent);
    
    // Receive auth response
    bytes_received = recv(sock, &packet, sizeof(packet), 0);
    if (bytes_received < 0) {
        perror("recv auth response");
        return SVPN_ERROR_NETWORK_FAIL;
    }
    
    // Decrypt response
    result = packet_decrypt(&packet, shared_secret);
    if (result != SVPN_SUCCESS) {
        return SVPN_ERROR_AUTH_FAIL;
    }
    
    // Check auth result
    if (ntohs(packet.type) != PKT_TYPE_AUTH || ntohs(packet.payload_len) != 4) {
        return SVPN_ERROR_AUTH_FAIL;
    }
    
    uint32_t auth_result = ntohl(*(uint32_t*)packet.payload);
    if (auth_result != 0) {
        return SVPN_ERROR_AUTH_FAIL;
    }
    
    printf("Authentication successful\n");
    
    // Clear sensitive data
    secure_zero(&client_keypair, sizeof(client_keypair));
    secure_zero(shared_secret, sizeof(shared_secret));
    
    return SVPN_SUCCESS;
}

int main(int argc, char *argv[]) {
    client_config_t config;
    license_t license;
    svpn_error_t result;
    
    printf("Secure VPN Client v%d.%d.%d\n", 
           SVPN_VERSION_MAJOR, SVPN_VERSION_MINOR, SVPN_VERSION_PATCH);
    printf("Starting client...\n");
    
    // Setup signal handlers
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // Parse command line arguments
    result = parse_arguments(argc, argv, &config);
    if (result != SVPN_SUCCESS) {
        print_usage(argv[0]);
        return 1;
    }
    
    // Load license file
    result = license_load_from_file(&license, config.license_file);
    if (result != SVPN_SUCCESS) {
        fprintf(stderr, "Error: Failed to load license file '%s'\n", config.license_file);
        return 1;
    }
    
    printf("License loaded for user: %s\n", license.user_id);
    printf("License expires: %s", ctime(&license.expires_at));
    
    // Connect to server
    result = connect_to_server(&config, &client_socket);
    if (result != SVPN_SUCCESS) {
        fprintf(stderr, "Error: Failed to connect to server %s:%d\n", 
                config.server_ip, config.server_port);
        return 1;
    }
    
    printf("Connected to server %s:%d\n", config.server_ip, config.server_port);
    
    // Perform handshake and authentication
    result = perform_handshake(client_socket, &license);
    if (result != SVPN_SUCCESS) {
        fprintf(stderr, "Error: Handshake/authentication failed\n");
        close(client_socket);
        return 1;
    }
    
    printf("VPN connection established\n");
    
    // Main client loop (simplified - just keep connection alive)
    while (running) {
        vpn_packet_t keepalive;
        
        // Send keepalive packet every 30 seconds
        result = packet_create(&keepalive, PKT_TYPE_KEEPALIVE, NULL, 0);
        if (result == SVPN_SUCCESS) {
            send(client_socket, &keepalive, VPN_HEADER_SIZE, 0);
        }
        
        sleep(30);
    }
    
    printf("\nShutting down client...\n");
    
    if (client_socket >= 0) {
        close(client_socket);
    }
    
    return 0;
}
