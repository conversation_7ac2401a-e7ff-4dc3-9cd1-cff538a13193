#!/usr/bin/env python3
"""
Add Kill Switch Feature - Essential for any VPN
Blocks internet if VPN connection drops
"""

import subprocess
import threading
import time
import psutil
import socket

class VPNKillSwitch:
    def __init__(self):
        self.enabled = False
        self.monitoring = False
        self.vpn_interface = None
        self.original_routes = []
        
    def enable_kill_switch(self):
        """Enable kill switch protection"""
        print("🛡️ Enabling VPN Kill Switch...")
        
        # Save original routes
        self.save_original_routes()
        
        # Block all traffic except VPN
        self.block_non_vpn_traffic()
        
        # Start monitoring VPN connection
        self.start_monitoring()
        
        self.enabled = True
        print("✅ Kill switch enabled - you're protected!")
    
    def disable_kill_switch(self):
        """Disable kill switch and restore normal internet"""
        print("🔓 Disabling kill switch...")
        
        self.enabled = False
        self.monitoring = False
        
        # Restore original routes
        self.restore_original_routes()
        
        print("✅ Kill switch disabled - normal internet restored")
    
    def save_original_routes(self):
        """Save current routing table"""
        try:
            if os.name == 'nt':  # Windows
                result = subprocess.run(['route', 'print'], 
                                      capture_output=True, text=True)
                self.original_routes = result.stdout
            else:  # Linux/Mac
                result = subprocess.run(['ip', 'route', 'show'], 
                                      capture_output=True, text=True)
                self.original_routes = result.stdout.split('\n')
        except Exception as e:
            print(f"⚠️ Could not save routes: {e}")
    
    def block_non_vpn_traffic(self):
        """Block all internet traffic except through VPN"""
        try:
            if os.name == 'nt':  # Windows
                # Block all outbound traffic except VPN server
                subprocess.run([
                    'netsh', 'advfirewall', 'firewall', 'add', 'rule',
                    'name=VPN_Kill_Switch_Block_All', 'dir=out', 'action=block',
                    'enable=yes'
                ], check=True)
                
                # Allow VPN server connection
                subprocess.run([
                    'netsh', 'advfirewall', 'firewall', 'add', 'rule',
                    'name=VPN_Kill_Switch_Allow_VPN', 'dir=out', 'action=allow',
                    'remoteip=*************', 'enable=yes'
                ], check=True)
                
            else:  # Linux
                # Block all traffic except VPN
                subprocess.run(['iptables', '-P', 'OUTPUT', 'DROP'], check=True)
                subprocess.run(['iptables', '-A', 'OUTPUT', '-d', '*************', '-j', 'ACCEPT'], check=True)
                subprocess.run(['iptables', '-A', 'OUTPUT', '-o', 'tun+', '-j', 'ACCEPT'], check=True)
                
        except Exception as e:
            print(f"⚠️ Could not set firewall rules: {e}")
    
    def restore_original_routes(self):
        """Restore original internet access"""
        try:
            if os.name == 'nt':  # Windows
                # Remove kill switch rules
                subprocess.run([
                    'netsh', 'advfirewall', 'firewall', 'delete', 'rule',
                    'name=VPN_Kill_Switch_Block_All'
                ], check=True)
                subprocess.run([
                    'netsh', 'advfirewall', 'firewall', 'delete', 'rule',
                    'name=VPN_Kill_Switch_Allow_VPN'
                ], check=True)
                
            else:  # Linux
                # Reset iptables
                subprocess.run(['iptables', '-P', 'OUTPUT', 'ACCEPT'], check=True)
                subprocess.run(['iptables', '-F', 'OUTPUT'], check=True)
                
        except Exception as e:
            print(f"⚠️ Could not restore routes: {e}")
    
    def check_vpn_connection(self):
        """Check if VPN is still connected"""
        try:
            # Check if VPN process is running
            vpn_running = any('vpn_client.py' in p.name() for p in psutil.process_iter(['name']))
            
            if not vpn_running:
                return False
            
            # Check if we can reach VPN server
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('*************', 8443))
            sock.close()
            
            return result == 0
            
        except Exception:
            return False
    
    def start_monitoring(self):
        """Start monitoring VPN connection"""
        def monitor():
            self.monitoring = True
            consecutive_failures = 0
            
            while self.monitoring and self.enabled:
                if not self.check_vpn_connection():
                    consecutive_failures += 1
                    print(f"⚠️ VPN connection check failed ({consecutive_failures}/3)")
                    
                    if consecutive_failures >= 3:
                        print("🚨 VPN CONNECTION LOST - KILL SWITCH ACTIVATED!")
                        print("🛡️ Internet blocked to protect your privacy")
                        print("🔄 Reconnect to VPN to restore internet access")
                        break
                else:
                    consecutive_failures = 0
                
                time.sleep(5)  # Check every 5 seconds
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()

# Integration with existing VPN client
def add_kill_switch_to_client():
    """Add kill switch to the professional VPN client"""
    kill_switch_code = '''
    def setup_kill_switch_ui(self):
        """Add kill switch toggle to UI"""
        # Kill switch frame
        ks_frame = tk.Frame(self.root, bg="#2d2d2d", relief=tk.RAISED, bd=1)
        ks_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ks_title = tk.Label(ks_frame, text="Kill Switch", 
                           font=("Arial", 12, "bold"), fg="#ffffff", bg="#2d2d2d")
        ks_title.pack(pady=(10, 5))
        
        self.kill_switch_var = tk.BooleanVar()
        self.kill_switch_cb = tk.Checkbutton(
            ks_frame, text="Block internet if VPN disconnects",
            variable=self.kill_switch_var, command=self.toggle_kill_switch,
            font=("Arial", 10), fg="#cccccc", bg="#2d2d2d",
            selectcolor="#2d2d2d", activebackground="#2d2d2d"
        )
        self.kill_switch_cb.pack(pady=(0, 15))
        
        self.kill_switch = VPNKillSwitch()
    
    def toggle_kill_switch(self):
        """Toggle kill switch on/off"""
        if self.kill_switch_var.get():
            self.kill_switch.enable_kill_switch()
        else:
            self.kill_switch.disable_kill_switch()
    '''
    
    print("💡 Kill switch code ready to integrate!")
    print("Add this to your professional_vpn_client.py")

if __name__ == "__main__":
    # Demo the kill switch
    ks = VPNKillSwitch()
    
    print("🛡️ VPN Kill Switch Demo")
    print("This will protect you if VPN disconnects")
    print()
    
    choice = input("Enable kill switch? (y/n): ")
    if choice.lower() == 'y':
        ks.enable_kill_switch()
        
        try:
            print("Kill switch is now active. Press Ctrl+C to disable.")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            ks.disable_kill_switch()
    
    add_kill_switch_to_client()
