#!/bin/bash

# FREE VPN Production Deployment Script
# Works with Oracle Cloud Always Free, AWS Free Tier, Google Cloud Free Credits
# Requires: Ubuntu 20.04+ with 1GB+ RAM

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN_NAME="${DOMAIN_NAME:-}"
ADMIN_EMAIL="${ADMIN_EMAIL:-<EMAIL>}"
VPN_PORT="${VPN_PORT:-8443}"
API_PORT="${API_PORT:-8080}"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root for security reasons"
        log_info "Please run as a regular user with sudo privileges"
        exit 1
    fi
}

# Check system requirements
check_requirements() {
    log_info "Checking system requirements..."
    
    # Check OS
    if ! grep -q "Ubuntu" /etc/os-release; then
        log_warning "This script is optimized for Ubuntu. Other distributions may work but are untested."
    fi
    
    # Check RAM (minimum 1GB)
    RAM_MB=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    if [[ $RAM_MB -lt 900 ]]; then
        log_error "Insufficient RAM: ${RAM_MB}MB. Minimum 1GB required."
        exit 1
    fi
    
    # Check disk space (minimum 5GB)
    DISK_GB=$(df / | awk 'NR==2{printf "%.0f", $4/1024/1024}')
    if [[ $DISK_GB -lt 5 ]]; then
        log_error "Insufficient disk space: ${DISK_GB}GB. Minimum 5GB required."
        exit 1
    fi
    
    log_success "System requirements met: ${RAM_MB}MB RAM, ${DISK_GB}GB disk"
}

# Install Docker and Docker Compose
install_docker() {
    log_info "Installing Docker and Docker Compose..."
    
    if command -v docker &> /dev/null; then
        log_info "Docker already installed"
    else
        # Install Docker
        curl -fsSL https://get.docker.com -o get-docker.sh
        sudo sh get-docker.sh
        sudo usermod -aG docker $USER
        rm get-docker.sh
        
        log_success "Docker installed successfully"
    fi
    
    if command -v docker-compose &> /dev/null; then
        log_info "Docker Compose already installed"
    else
        # Install Docker Compose
        sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        sudo chmod +x /usr/local/bin/docker-compose
        
        log_success "Docker Compose installed successfully"
    fi
}

# Configure firewall
configure_firewall() {
    log_info "Configuring firewall..."
    
    # Install ufw if not present
    if ! command -v ufw &> /dev/null; then
        sudo apt-get update
        sudo apt-get install -y ufw
    fi
    
    # Configure firewall rules
    sudo ufw --force reset
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    
    # Allow SSH
    sudo ufw allow ssh
    
    # Allow VPN ports
    sudo ufw allow $VPN_PORT/udp comment "VPN Server"
    sudo ufw allow 80/tcp comment "HTTP (Let's Encrypt)"
    sudo ufw allow 443/tcp comment "HTTPS"
    
    # Enable firewall
    sudo ufw --force enable
    
    log_success "Firewall configured successfully"
}

# Setup domain and SSL
setup_domain() {
    if [[ -z "$DOMAIN_NAME" ]]; then
        log_warning "No domain name provided. Using self-signed certificates."
        log_info "To use a custom domain, set DOMAIN_NAME environment variable"
        log_info "Free domains available at: freenom.com, duckdns.org"
        return
    fi
    
    log_info "Setting up domain: $DOMAIN_NAME"
    
    # Create nginx configuration for Let's Encrypt
    sudo mkdir -p /etc/nginx/sites-available
    sudo tee /etc/nginx/sites-available/vpn > /dev/null <<EOF
server {
    listen 80;
    server_name $DOMAIN_NAME;
    
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }
    
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}
EOF
    
    # Install nginx if not present
    if ! command -v nginx &> /dev/null; then
        sudo apt-get update
        sudo apt-get install -y nginx
    fi
    
    # Enable site
    sudo ln -sf /etc/nginx/sites-available/vpn /etc/nginx/sites-enabled/
    sudo nginx -t && sudo systemctl reload nginx
    
    log_success "Domain configuration completed"
}

# Generate environment file
generate_env() {
    log_info "Generating environment configuration..."
    
    cat > .env <<EOF
# Production Environment Configuration
DOMAIN_NAME=${DOMAIN_NAME:-localhost}
ADMIN_EMAIL=${ADMIN_EMAIL}

# Database passwords (change these!)
POSTGRES_PASSWORD=$(openssl rand -base64 32)
REDIS_PASSWORD=$(openssl rand -base64 32)
GRAFANA_PASSWORD=$(openssl rand -base64 32)

# Stripe configuration (add your keys)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Server configuration
VPN_PORT=${VPN_PORT}
API_PORT=${API_PORT}
MAX_CLIENTS=100
LOG_LEVEL=INFO
EOF
    
    chmod 600 .env
    log_success "Environment file created: .env"
    log_warning "Please update Stripe keys in .env file for payment processing"
}

# Create minimal docker-compose for free tier
create_minimal_compose() {
    log_info "Creating minimal Docker Compose configuration for free tier..."
    
    cat > docker-compose.free.yml <<EOF
version: '3.8'

services:
  # VPN Server (minimal resources)
  vpn-server:
    build:
      context: .
      dockerfile: deployment/docker/Dockerfile
      target: production
    container_name: securevpn-server
    restart: unless-stopped
    ports:
      - "${VPN_PORT}:8443/udp"  # VPN port
      - "127.0.0.1:${API_PORT}:8080"  # API (localhost only)
    volumes:
      - vpn-data:/opt/securevpn/data
      - vpn-logs:/opt/securevpn/logs
      - ./ssl:/opt/securevpn/ssl:ro
    environment:
      - VPN_PORT=8443
      - API_PORT=8080
      - DATABASE_PATH=/opt/securevpn/data/vpn.db
      - PRIVATE_KEY_FILE=/opt/securevpn/ssl/server.key
      - PUBLIC_KEY_FILE=/opt/securevpn/ssl/server.pub
      - LOG_LEVEL=INFO
      - MAX_CLIENTS=50
      - DOMAIN_NAME=${DOMAIN_NAME:-localhost}
    cap_add:
      - NET_ADMIN
    devices:
      - /dev/net/tun
    sysctls:
      - net.ipv4.ip_forward=1
    mem_limit: 512m
    cpus: 0.5
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 60s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: securevpn-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - /var/www/html:/var/www/html:ro
    depends_on:
      - vpn-server
    mem_limit: 128m
    cpus: 0.2

volumes:
  vpn-data:
    driver: local
  vpn-logs:
    driver: local

networks:
  default:
    driver: bridge
EOF
    
    log_success "Minimal Docker Compose configuration created"
}

# Create nginx configuration
create_nginx_config() {
    log_info "Creating Nginx configuration..."
    
    cat > nginx.conf <<EOF
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/m;
    
    # Upstream for VPN API
    upstream vpn_api {
        server vpn-server:8080;
    }
    
    # HTTP server (redirect to HTTPS)
    server {
        listen 80;
        server_name ${DOMAIN_NAME:-localhost};
        
        location /.well-known/acme-challenge/ {
            root /var/www/html;
        }
        
        location / {
            return 301 https://\$server_name\$request_uri;
        }
    }
    
    # HTTPS server
    server {
        listen 443 ssl http2;
        server_name ${DOMAIN_NAME:-localhost};
        
        # SSL configuration
        ssl_certificate /etc/nginx/ssl/fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/privkey.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        
        # Security headers
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;
        
        # API proxy
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://vpn_api/;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
        
        # Static files
        location / {
            root /var/www/html;
            index index.html;
            try_files \$uri \$uri/ =404;
        }
    }
}
EOF
    
    log_success "Nginx configuration created"
}

# Main deployment function
main() {
    log_info "Starting FREE VPN Production Deployment"
    log_info "Optimized for free tier cloud servers"
    
    check_root
    check_requirements
    install_docker
    configure_firewall
    setup_domain
    generate_env
    create_minimal_compose
    create_nginx_config
    
    log_success "Deployment preparation completed!"
    echo
    log_info "Next steps:"
    echo "1. If using a domain, point DNS A record to this server's IP"
    echo "2. Run: docker-compose -f docker-compose.free.yml up -d"
    echo "3. Get SSL certificate: sudo certbot --nginx -d $DOMAIN_NAME"
    echo "4. Update Stripe keys in .env file for payments"
    echo
    log_info "Free resources used:"
    echo "• Oracle Cloud Always Free (recommended)"
    echo "• Let's Encrypt SSL certificates"
    echo "• Freenom free domain (optional)"
    echo "• Cloudflare free DNS (optional)"
    echo
    log_warning "Remember: Free tiers have limitations. Monitor usage!"
}

# Run main function
main "$@"
