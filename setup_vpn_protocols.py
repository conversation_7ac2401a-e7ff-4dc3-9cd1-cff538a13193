#!/usr/bin/env python3
"""
VPN Protocol Setup - Complete Setup Guide
Sets up OpenVPN and WireGuard for production use
"""

import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """Check if required tools are installed"""
    print("🔍 Checking requirements...")
    
    requirements = {
        'openssl': 'OpenSSL for certificate generation',
        'python': 'Python 3.6+ for management scripts'
    }
    
    optional = {
        'openvpn': 'OpenVPN server (for OpenVPN protocol)',
        'wg': 'WireGuard tools (for WireGuard protocol)'
    }
    
    missing_required = []
    missing_optional = []
    
    # Check required tools
    for tool, description in requirements.items():
        try:
            subprocess.run([tool, '--version'], capture_output=True, check=True)
            print(f"✅ {tool} - {description}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"❌ {tool} - {description}")
            missing_required.append(tool)
    
    # Check optional tools
    for tool, description in optional.items():
        try:
            subprocess.run([tool, '--version'], capture_output=True, check=True)
            print(f"✅ {tool} - {description}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"⚠️  {tool} - {description} (optional)")
            missing_optional.append(tool)
    
    return missing_required, missing_optional

def install_instructions():
    """Show installation instructions"""
    print("\n📦 Installation Instructions:")
    print("=" * 50)
    
    print("\n🪟 Windows:")
    print("1. OpenSSL: Download from https://slproweb.com/products/Win32OpenSSL.html")
    print("2. OpenVPN: Download from https://openvpn.net/community-downloads/")
    print("3. WireGuard: Download from https://www.wireguard.com/install/")
    
    print("\n🐧 Linux (Ubuntu/Debian):")
    print("sudo apt update")
    print("sudo apt install openssl openvpn wireguard")
    
    print("\n🍎 macOS:")
    print("brew install openssl openvpn wireguard-tools")
    
    print("\n🐳 Docker Alternative:")
    print("Use our Docker containers for easy deployment")

def setup_demo():
    """Set up demo VPN service"""
    print("\n🚀 Setting up Demo VPN Service...")
    print("=" * 40)
    
    # Create demo users
    demo_users = [
        ('<EMAIL>', 'demo123', 'basic'),
        ('<EMAIL>', 'premium123', 'premium'),
        ('<EMAIL>', 'enterprise123', 'enterprise')
    ]
    
    print("👥 Creating demo users...")
    for email, password, plan in demo_users:
        try:
            result = subprocess.run([
                sys.executable, 'vpn_manager_tool.py',
                'create-user', email, password, plan
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ Created: {email} ({plan})")
            else:
                print(f"⚠️  User might exist: {email}")
        except Exception as e:
            print(f"❌ Error creating {email}: {e}")
    
    print("\n📱 Generating VPN profiles...")
    
    # Generate profiles for demo users
    for email, _, plan in demo_users:
        try:
            print(f"\n🔐 Generating profiles for {email}...")
            
            # Try to generate profiles (will show what's missing)
            result = subprocess.run([
                sys.executable, 'vpn_manager_tool.py',
                'generate-profile', email, '--server', 'demo.securevpn.com'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✅ Profiles generated for {email}")
            else:
                print(f"⚠️  Profile generation needs setup: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Error generating profile for {email}: {e}")

def create_docker_setup():
    """Create Docker setup files"""
    print("\n🐳 Creating Docker setup...")
    
    # Create Dockerfile for VPN server
    dockerfile_content = """FROM ubuntu:22.04

# Install dependencies
RUN apt-get update && apt-get install -y \\
    python3 \\
    python3-pip \\
    openssl \\
    openvpn \\
    wireguard \\
    iptables \\
    && rm -rf /var/lib/apt/lists/*

# Install Python packages
RUN pip3 install requests qrcode[pil]

# Create app directory
WORKDIR /app

# Copy VPN files
COPY *.py /app/
COPY docs/ /app/docs/

# Create directories
RUN mkdir -p /app/openvpn /app/wireguard

# Expose ports
EXPOSE 8090 8443 1194/udp 51820/udp

# Start script
CMD ["python3", "vpn_server.py"]
"""
    
    with open('Dockerfile', 'w') as f:
        f.write(dockerfile_content)
    
    # Create docker-compose.yml
    compose_content = """version: '3.8'

services:
  vpn-server:
    build: .
    ports:
      - "8090:8090"     # API/Dashboard
      - "8443:8443"     # Custom VPN
      - "1194:1194/udp" # OpenVPN
      - "51820:51820/udp" # WireGuard
    volumes:
      - ./data:/app/data
      - ./openvpn:/app/openvpn
      - ./wireguard:/app/wireguard
    cap_add:
      - NET_ADMIN
    sysctls:
      - net.ipv4.ip_forward=1
    environment:
      - VPN_SERVER_HOST=your-server.com
    restart: unless-stopped

  vpn-dashboard:
    build: .
    command: python3 demo_server.py
    ports:
      - "8091:8090"
    volumes:
      - ./data:/app/data
    depends_on:
      - vpn-server
    restart: unless-stopped
"""
    
    with open('docker-compose.yml', 'w') as f:
        f.write(compose_content)
    
    print("✅ Docker files created:")
    print("   - Dockerfile")
    print("   - docker-compose.yml")
    print("\n🚀 To start with Docker:")
    print("   docker-compose up -d")

def show_connection_guide():
    """Show how users can connect"""
    print("\n📱 User Connection Guide:")
    print("=" * 40)
    
    print("\n🔐 Method 1: OpenVPN (Traditional)")
    print("1. Download OpenVPN client for your device")
    print("2. Import the .ovpn profile file")
    print("3. Enter your email/password when prompted")
    print("4. Connect!")
    
    print("\n⚡ Method 2: WireGuard (Modern)")
    print("1. Download WireGuard app for your device")
    print("2. Import config file or scan QR code")
    print("3. Toggle connection ON")
    print("4. No passwords needed!")
    
    print("\n🔧 Method 3: Custom Protocol")
    print("1. Use our custom VPN client")
    print("2. Connect with email/password or license key")
    print("3. Supports advanced features")
    
    print("\n📊 Management:")
    print("- Dashboard: http://your-server:8090/docs/dashboard.html")
    print("- Create users: python vpn_manager_tool.py create-user")
    print("- Generate profiles: python vpn_manager_tool.py generate-profile")

def main():
    """Main setup function"""
    print("🔒 Secure VPN - Protocol Setup Guide")
    print("=" * 50)
    
    # Check requirements
    missing_required, missing_optional = check_requirements()
    
    if missing_required:
        print(f"\n❌ Missing required tools: {', '.join(missing_required)}")
        install_instructions()
        print("\nPlease install required tools and run again.")
        return 1
    
    if missing_optional:
        print(f"\n⚠️  Optional tools not found: {', '.join(missing_optional)}")
        print("Some VPN protocols will not be available.")
    
    print("\n✅ Basic requirements met!")
    
    # Show options
    print("\n🎯 Setup Options:")
    print("1. Set up demo environment")
    print("2. Create Docker deployment")
    print("3. Show connection guide")
    print("4. All of the above")
    
    try:
        choice = input("\nSelect option (1-4): ").strip()
        
        if choice == '1':
            setup_demo()
        elif choice == '2':
            create_docker_setup()
        elif choice == '3':
            show_connection_guide()
        elif choice == '4':
            setup_demo()
            create_docker_setup()
            show_connection_guide()
        else:
            print("❌ Invalid choice")
            return 1
        
        print("\n🎉 Setup completed!")
        print("\n📚 Next Steps:")
        print("1. Start your VPN server: python vpn_server.py")
        print("2. Start the dashboard: python demo_server.py")
        print("3. Create user profiles: python vpn_manager_tool.py generate-profile <EMAIL>")
        print("4. Distribute profiles to users")
        print("5. Monitor via dashboard: http://localhost:8090/docs/dashboard.html")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n🛑 Setup cancelled")
        return 1

if __name__ == "__main__":
    sys.exit(main())
