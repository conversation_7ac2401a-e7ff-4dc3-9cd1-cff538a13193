#!/usr/bin/env python3
"""
Professional VPN Client - Commercial Grade UI
"""

import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
import threading
import time
import json
import socket
from pathlib import Path
import sys

class ProfessionalVPNClient:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        
        self.connected = False
        self.vpn_process = None
        self.connection_time = 0
        self.data_sent = 0
        self.data_received = 0
        
        self.setup_ui()
        self.start_monitoring()
    
    def setup_window(self):
        """Setup main window"""
        self.root.title("VPN-in-a-Box Client")
        self.root.geometry("450x600")
        self.root.resizable(False, False)
        
        # Set icon and styling
        self.root.configure(bg="#1e1e1e")
        
        # Configure styles
        style = ttk.Style()
        style.theme_use("clam")
        style.configure("Title.TLabel", background="#1e1e1e", foreground="#ffffff", font=("Arial", 16, "bold"))
        style.configure("Status.TLabel", background="#1e1e1e", foreground="#00ff00", font=("Arial", 12))
        style.configure("Info.TLabel", background="#1e1e1e", foreground="#cccccc", font=("Arial", 10))
    
    def setup_ui(self):
        """Setup the user interface"""
        # Header
        header_frame = tk.Frame(self.root, bg="#1e1e1e")
        header_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # Logo/Title
        title = tk.Label(header_frame, text="🔒 VPN-in-a-Box", 
                        font=("Arial", 20, "bold"), fg="#ffffff", bg="#1e1e1e")
        title.pack()
        
        subtitle = tk.Label(header_frame, text="Own Your Privacy", 
                           font=("Arial", 12), fg="#888888", bg="#1e1e1e")
        subtitle.pack()
        
        # Connection Status Card
        self.status_frame = tk.Frame(self.root, bg="#2d2d2d", relief=tk.RAISED, bd=1)
        self.status_frame.pack(fill=tk.X, padx=20, pady=10)
        
        # Status indicator
        status_header = tk.Frame(self.status_frame, bg="#2d2d2d")
        status_header.pack(fill=tk.X, padx=15, pady=10)
        
        self.status_indicator = tk.Label(status_header, text="●", 
                                        font=("Arial", 20), fg="#ff4444", bg="#2d2d2d")
        self.status_indicator.pack(side=tk.LEFT)
        
        self.status_text = tk.Label(status_header, text="Disconnected", 
                                   font=("Arial", 14, "bold"), fg="#ffffff", bg="#2d2d2d")
        self.status_text.pack(side=tk.LEFT, padx=10)
        
        # Connection details
        self.details_frame = tk.Frame(self.status_frame, bg="#2d2d2d")
        self.details_frame.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        self.server_label = tk.Label(self.details_frame, text="Server: Not connected", 
                                    font=("Arial", 10), fg="#cccccc", bg="#2d2d2d")
        self.server_label.pack(anchor=tk.W)
        
        self.ip_label = tk.Label(self.details_frame, text="Virtual IP: None", 
                                font=("Arial", 10), fg="#cccccc", bg="#2d2d2d")
        self.ip_label.pack(anchor=tk.W)
        
        self.time_label = tk.Label(self.details_frame, text="Connected: 00:00:00", 
                                  font=("Arial", 10), fg="#cccccc", bg="#2d2d2d")
        self.time_label.pack(anchor=tk.W)
        
        # Connect Button
        self.connect_btn = tk.Button(
            self.root,
            text="CONNECT",
            command=self.toggle_connection,
            font=("Arial", 14, "bold"),
            bg="#00aa44",
            fg="white",
            activebackground="#00cc55",
            activeforeground="white",
            relief=tk.FLAT,
            width=20,
            height=2,
            cursor="hand2"
        )
        self.connect_btn.pack(pady=20)
        
        # Statistics Card
        stats_frame = tk.Frame(self.root, bg="#2d2d2d", relief=tk.RAISED, bd=1)
        stats_frame.pack(fill=tk.X, padx=20, pady=10)
        
        stats_title = tk.Label(stats_frame, text="Statistics", 
                              font=("Arial", 12, "bold"), fg="#ffffff", bg="#2d2d2d")
        stats_title.pack(pady=(10, 5))
        
        stats_content = tk.Frame(stats_frame, bg="#2d2d2d")
        stats_content.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        # Data usage
        self.upload_label = tk.Label(stats_content, text="↑ Uploaded: 0 MB", 
                                    font=("Arial", 10), fg="#cccccc", bg="#2d2d2d")
        self.upload_label.pack(anchor=tk.W)
        
        self.download_label = tk.Label(stats_content, text="↓ Downloaded: 0 MB", 
                                      font=("Arial", 10), fg="#cccccc", bg="#2d2d2d")
        self.download_label.pack(anchor=tk.W)
        
        # Server Info Card
        server_frame = tk.Frame(self.root, bg="#2d2d2d", relief=tk.RAISED, bd=1)
        server_frame.pack(fill=tk.X, padx=20, pady=10)
        
        server_title = tk.Label(server_frame, text="Your VPN Server", 
                               font=("Arial", 12, "bold"), fg="#ffffff", bg="#2d2d2d")
        server_title.pack(pady=(10, 5))
        
        server_content = tk.Frame(server_frame, bg="#2d2d2d")
        server_content.pack(fill=tk.X, padx=15, pady=(0, 15))
        
        server_info = [
            "🌍 Location: Your Server",
            "🔒 Encryption: AES-256",
            "⚡ Protocol: Custom + OpenVPN",
            "🛡️ Privacy: 100% Owned by You",
            "💰 Cost: $0/month"
        ]
        
        for info in server_info:
            label = tk.Label(server_content, text=info, 
                           font=("Arial", 10), fg="#cccccc", bg="#2d2d2d")
            label.pack(anchor=tk.W, pady=1)
        
        # Footer
        footer = tk.Label(self.root, text="VPN-in-a-Box v1.0 - Own Your Privacy", 
                         font=("Arial", 8), fg="#666666", bg="#1e1e1e")
        footer.pack(side=tk.BOTTOM, pady=10)
    
    def toggle_connection(self):
        """Toggle VPN connection"""
        if not self.connected:
            self.connect_vpn()
        else:
            self.disconnect_vpn()
    
    def connect_vpn(self):
        """Connect to VPN"""
        try:
            # Update UI immediately
            self.connect_btn.config(text="CONNECTING...", state=tk.DISABLED, bg="#ffaa00")
            self.root.update()
            
            # Start VPN client
            self.vpn_process = subprocess.Popen([
                sys.executable, "vpn_client.py"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # Wait a moment for connection
            time.sleep(2)
            
            # Update UI for connected state
            self.connected = True
            self.connection_time = time.time()
            
            self.status_indicator.config(fg="#00ff00")
            self.status_text.config(text="Connected")
            self.server_label.config(text="Server: *************:8443")
            self.ip_label.config(text="Virtual IP: ********")
            
            self.connect_btn.config(text="DISCONNECT", state=tk.NORMAL, bg="#ff4444")
            
            messagebox.showinfo("VPN Connected", 
                              "Successfully connected to your VPN server!\n\n"
                              "Your traffic is now encrypted and secure.")
            
        except Exception as e:
            self.connect_btn.config(text="CONNECT", state=tk.NORMAL, bg="#00aa44")
            messagebox.showerror("Connection Failed", f"Failed to connect to VPN:\n{e}")
    
    def disconnect_vpn(self):
        """Disconnect from VPN"""
        try:
            if self.vpn_process:
                self.vpn_process.terminate()
                self.vpn_process = None
            
            # Update UI for disconnected state
            self.connected = False
            self.connection_time = 0
            
            self.status_indicator.config(fg="#ff4444")
            self.status_text.config(text="Disconnected")
            self.server_label.config(text="Server: Not connected")
            self.ip_label.config(text="Virtual IP: None")
            self.time_label.config(text="Connected: 00:00:00")
            
            self.connect_btn.config(text="CONNECT", bg="#00aa44")
            
            messagebox.showinfo("VPN Disconnected", "Disconnected from VPN server.")
            
        except Exception as e:
            messagebox.showerror("Disconnection Failed", f"Failed to disconnect:\n{e}")
    
    def start_monitoring(self):
        """Start monitoring thread"""
        def monitor():
            while True:
                if self.connected and self.connection_time > 0:
                    # Update connection time
                    elapsed = int(time.time() - self.connection_time)
                    hours = elapsed // 3600
                    minutes = (elapsed % 3600) // 60
                    seconds = elapsed % 60
                    
                    time_str = f"Connected: {hours:02d}:{minutes:02d}:{seconds:02d}"
                    self.time_label.config(text=time_str)
                    
                    # Update data usage (simulated for now)
                    self.data_sent += 0.1
                    self.data_received += 0.3
                    
                    self.upload_label.config(text=f"↑ Uploaded: {self.data_sent:.1f} MB")
                    self.download_label.config(text=f"↓ Downloaded: {self.data_received:.1f} MB")
                
                time.sleep(1)
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = ProfessionalVPNClient()
    app.run()
