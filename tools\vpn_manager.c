#include "securevpn.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <getopt.h>
#include <time.h>

// VPN management tool for commercial operations
typedef enum {
    CMD_CREATE_USER,
    CMD_LIST_USERS,
    CMD_UPDATE_SUBSCRIPTION,
    CMD_GENERATE_LICENSE,
    CMD_REVOKE_LICENSE,
    CMD_VIEW_ANALYTICS,
    CMD_CLEANUP_SESSIONS,
    CMD_HELP
} command_t;

// External license manager functions
extern svpn_error_t license_manager_init(const char *db_path, const char *private_key_file, 
                                        const char *public_key_file);
extern void license_manager_cleanup(void);
extern svpn_error_t license_manager_create_user(const char *email, const char *password, 
                                               subscription_plan_t plan, user_account_t *user);
extern svpn_error_t license_manager_generate_license(uint64_t user_id, const char *device_name,
                                                    time_t duration, char *license_key_out, 
                                                    size_t license_key_size);

static void print_usage(const char *program_name) {
    printf("VPN Manager - Commercial VPN Service Management Tool\n");
    printf("Usage: %s [OPTIONS] COMMAND [ARGS]\n\n", program_name);
    
    printf("Global Options:\n");
    printf("  -d, --database PATH     Database file path (default: vpn.db)\n");
    printf("  -k, --private-key PATH  Private key file path\n");
    printf("  -p, --public-key PATH   Public key file path\n");
    printf("  -h, --help             Show this help message\n\n");
    
    printf("Commands:\n");
    printf("  create-user EMAIL PASSWORD PLAN\n");
    printf("                         Create new user account\n");
    printf("                         PLAN: free, basic, premium, enterprise\n\n");
    
    printf("  list-users             List all user accounts\n\n");
    
    printf("  update-subscription USER_ID PLAN DURATION\n");
    printf("                         Update user subscription\n");
    printf("                         DURATION: days (0 for permanent)\n\n");
    
    printf("  generate-license USER_ID DEVICE_NAME [DURATION]\n");
    printf("                         Generate license for user\n");
    printf("                         DURATION: days (default: 30)\n\n");
    
    printf("  revoke-license LICENSE_ID\n");
    printf("                         Revoke a license\n\n");
    
    printf("  view-analytics [USER_ID]\n");
    printf("                         View usage analytics\n\n");
    
    printf("  cleanup-sessions       Remove inactive sessions\n\n");
    
    printf("Examples:\n");
    printf("  %s -d vpn.db -k server.key -p server.pub create-user <EMAIL> password123 premium\n", program_name);
    printf("  %s -d vpn.db list-users\n", program_name);
    printf("  %s -d vpn.db generate-license 1 \"John's Phone\" 30\n", program_name);
}

static command_t parse_command(const char *cmd_str) {
    if (strcmp(cmd_str, "create-user") == 0) return CMD_CREATE_USER;
    if (strcmp(cmd_str, "list-users") == 0) return CMD_LIST_USERS;
    if (strcmp(cmd_str, "update-subscription") == 0) return CMD_UPDATE_SUBSCRIPTION;
    if (strcmp(cmd_str, "generate-license") == 0) return CMD_GENERATE_LICENSE;
    if (strcmp(cmd_str, "revoke-license") == 0) return CMD_REVOKE_LICENSE;
    if (strcmp(cmd_str, "view-analytics") == 0) return CMD_VIEW_ANALYTICS;
    if (strcmp(cmd_str, "cleanup-sessions") == 0) return CMD_CLEANUP_SESSIONS;
    if (strcmp(cmd_str, "help") == 0) return CMD_HELP;
    return CMD_HELP;
}

static int cmd_create_user(int argc, char *argv[]) {
    if (argc < 3) {
        printf("Error: create-user requires EMAIL PASSWORD PLAN\n");
        return 1;
    }
    
    const char *email = argv[0];
    const char *password = argv[1];
    const char *plan_str = argv[2];
    
    subscription_plan_t plan = db_plan_from_string(plan_str);
    user_account_t user;
    
    svpn_error_t result = license_manager_create_user(email, password, plan, &user);
    
    if (result == SVPN_SUCCESS) {
        printf("User created successfully:\n");
        printf("  User ID: %lu\n", user.user_id);
        printf("  Email: %s\n", user.email);
        printf("  Plan: %s\n", db_plan_to_string(user.plan));
        printf("  Max Connections: %u\n", user.max_connections);
        printf("  Daily Limit: %lu bytes\n", user.bytes_limit_daily);
        printf("  Subscription Expires: %s", ctime(&user.subscription_expires));
        return 0;
    } else {
        printf("Error: Failed to create user (%d)\n", result);
        return 1;
    }
}

static int cmd_generate_license(int argc, char *argv[]) {
    if (argc < 2) {
        printf("Error: generate-license requires USER_ID DEVICE_NAME [DURATION]\n");
        return 1;
    }
    
    uint64_t user_id = strtoull(argv[0], NULL, 10);
    const char *device_name = argv[1];
    time_t duration = (argc >= 3) ? atoi(argv[2]) * 24 * 3600 : 30 * 24 * 3600; // Default 30 days
    
    char license_key[1024];
    svpn_error_t result = license_manager_generate_license(user_id, device_name, duration, 
                                                          license_key, sizeof(license_key));
    
    if (result == SVPN_SUCCESS) {
        printf("License generated successfully:\n");
        printf("  User ID: %lu\n", user_id);
        printf("  Device: %s\n", device_name);
        printf("  Duration: %ld days\n", duration / (24 * 3600));
        printf("  License Key: %s\n", license_key);
        
        // Save license to file
        char filename[256];
        snprintf(filename, sizeof(filename), "license_%lu_%s.lic", user_id, device_name);
        
        // Replace spaces with underscores in filename
        for (char *p = filename; *p; p++) {
            if (*p == ' ') *p = '_';
        }
        
        FILE *file = fopen(filename, "w");
        if (file) {
            fprintf(file, "%s\n", license_key);
            fclose(file);
            printf("  License saved to: %s\n", filename);
        }
        
        return 0;
    } else {
        printf("Error: Failed to generate license (%d)\n", result);
        return 1;
    }
}

static int cmd_list_users(void) {
    // This would require additional database functions to list users
    printf("User listing functionality would be implemented here.\n");
    printf("This requires additional database query functions.\n");
    return 0;
}

static int cmd_view_analytics(int argc, char *argv[]) {
    // This would require analytics database functions
    printf("Analytics functionality would be implemented here.\n");
    printf("This requires additional database analytics functions.\n");
    return 0;
}

int main(int argc, char *argv[]) {
    char database_path[512] = "vpn.db";
    char private_key_file[256] = "";
    char public_key_file[256] = "";
    
    // Parse global options
    int opt;
    static struct option long_options[] = {
        {"database", required_argument, 0, 'd'},
        {"private-key", required_argument, 0, 'k'},
        {"public-key", required_argument, 0, 'p'},
        {"help", no_argument, 0, 'h'},
        {0, 0, 0, 0}
    };
    
    while ((opt = getopt_long(argc, argv, "d:k:p:h", long_options, NULL)) != -1) {
        switch (opt) {
            case 'd':
                strncpy(database_path, optarg, sizeof(database_path) - 1);
                break;
            case 'k':
                strncpy(private_key_file, optarg, sizeof(private_key_file) - 1);
                break;
            case 'p':
                strncpy(public_key_file, optarg, sizeof(public_key_file) - 1);
                break;
            case 'h':
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    // Check if command is provided
    if (optind >= argc) {
        printf("Error: No command specified\n\n");
        print_usage(argv[0]);
        return 1;
    }
    
    command_t cmd = parse_command(argv[optind]);
    
    if (cmd == CMD_HELP) {
        print_usage(argv[0]);
        return 0;
    }
    
    // Commands that require key files
    if (cmd == CMD_CREATE_USER || cmd == CMD_GENERATE_LICENSE) {
        if (strlen(private_key_file) == 0 || strlen(public_key_file) == 0) {
            printf("Error: Private and public key files are required for this command\n");
            return 1;
        }
    }
    
    // Initialize license manager
    if (cmd != CMD_HELP) {
        svpn_error_t result = license_manager_init(database_path, private_key_file, public_key_file);
        if (result != SVPN_SUCCESS) {
            printf("Error: Failed to initialize license manager (%d)\n", result);
            return 1;
        }
    }
    
    // Execute command
    int ret = 0;
    int cmd_argc = argc - optind - 1;
    char **cmd_argv = &argv[optind + 1];
    
    switch (cmd) {
        case CMD_CREATE_USER:
            ret = cmd_create_user(cmd_argc, cmd_argv);
            break;
        case CMD_LIST_USERS:
            ret = cmd_list_users();
            break;
        case CMD_GENERATE_LICENSE:
            ret = cmd_generate_license(cmd_argc, cmd_argv);
            break;
        case CMD_VIEW_ANALYTICS:
            ret = cmd_view_analytics(cmd_argc, cmd_argv);
            break;
        default:
            printf("Command not implemented yet\n");
            ret = 1;
            break;
    }
    
    // Cleanup
    license_manager_cleanup();
    
    return ret;
}
