#!/usr/bin/env python3
"""
Simple OpenVPN Setup - Get it working fast!
"""

import os
import sys
import subprocess
import socket
from pathlib import Path
import time

def get_local_ip():
    """Get local IP address"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "127.0.0.1"

def create_directories():
    """Create necessary directories"""
    dirs = [
        "openvpn",
        "openvpn/ca",
        "openvpn/server", 
        "openvpn/client",
        "vpn_profiles"
    ]
    
    for dir_path in dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    print("✅ Directories created")

def generate_certificates():
    """Generate OpenVPN certificates using OpenSSL"""
    print("🔐 Generating certificates...")
    
    ca_dir = Path("openvpn/ca")
    server_dir = Path("openvpn/server")
    
    try:
        # Generate CA key
        subprocess.run([
            "openssl", "genrsa", "-out", str(ca_dir / "ca.key"), "4096"
        ], check=True, capture_output=True)
        
        # Generate CA certificate
        subprocess.run([
            "openssl", "req", "-new", "-x509", "-days", "3650",
            "-key", str(ca_dir / "ca.key"), "-out", str(ca_dir / "ca.crt"),
            "-subj", "/C=US/ST=CA/L=SF/O=SecureVPN/CN=SecureVPN-CA"
        ], check=True, capture_output=True)
        
        # Generate server key
        subprocess.run([
            "openssl", "genrsa", "-out", str(server_dir / "server.key"), "4096"
        ], check=True, capture_output=True)
        
        # Generate server certificate request
        subprocess.run([
            "openssl", "req", "-new", "-key", str(server_dir / "server.key"),
            "-out", str(server_dir / "server.csr"),
            "-subj", "/C=US/ST=CA/L=SF/O=SecureVPN/CN=vpn.localhost.com"
        ], check=True, capture_output=True)
        
        # Sign server certificate
        subprocess.run([
            "openssl", "x509", "-req", "-days", "3650",
            "-in", str(server_dir / "server.csr"), "-CA", str(ca_dir / "ca.crt"),
            "-CAkey", str(ca_dir / "ca.key"), "-CAcreateserial",
            "-out", str(server_dir / "server.crt")
        ], check=True, capture_output=True)
        
        # Generate DH parameters
        subprocess.run([
            "openssl", "dhparam", "-out", str(server_dir / "dh2048.pem"), "2048"
        ], check=True, capture_output=True)
        
        print("✅ Certificates generated")
        return True
        
    except Exception as e:
        print(f"❌ Certificate generation failed: {e}")
        return False

def create_server_config(server_ip):
    """Create OpenVPN server configuration"""
    print("📋 Creating server configuration...")
    
    config = f"""# OpenVPN Server Configuration
port 1194
proto udp
dev tun

# Certificates and keys
ca ca/ca.crt
cert server.crt
key server.key
dh dh2048.pem

# Network configuration
server ******** *************
ifconfig-pool-persist ipp.txt

# Push routes to clients
push "redirect-gateway def1 bypass-dhcp"
push "dhcp-option DNS *******"
push "dhcp-option DNS *******"

# Client management
client-to-client
duplicate-cn
keepalive 10 120
cipher AES-256-GCM
auth SHA256

# Logging
status openvpn-status.log
log openvpn.log
verb 3

# Security
user nobody
group nogroup
persist-key
persist-tun

# Simple auth (for testing)
auth-user-pass-verify auth.py via-env
script-security 3
username-as-common-name
"""
    
    config_file = Path("openvpn/server/server.conf")
    with open(config_file, 'w') as f:
        f.write(config)
    
    print("✅ Server config created")
    return config_file

def create_auth_script():
    """Create simple authentication script"""
    print("🔐 Creating auth script...")
    
    auth_script = """#!/usr/bin/env python3
import os
import sys

# Simple authentication - accept any user for testing
username = os.environ.get('username', '')
password = os.environ.get('password', '')

# For testing, accept <EMAIL> with password 'password'
if username == '<EMAIL>' and password == 'password':
    sys.exit(0)  # Success
else:
    sys.exit(1)  # Failure
"""
    
    auth_file = Path("openvpn/server/auth.py")
    with open(auth_file, 'w') as f:
        f.write(auth_script)
    
    # Make executable
    if os.name != 'nt':  # Not Windows
        os.chmod(auth_file, 0o755)
    
    print("✅ Auth script created")
    return auth_file

def create_client_config(server_ip):
    """Create OpenVPN client configuration"""
    print("📱 Creating client configuration...")
    
    # Read CA certificate
    with open("openvpn/ca/ca.crt", 'r') as f:
        ca_cert = f.read()
    
    config = f"""# OpenVPN Client Configuration
client
dev tun
proto udp
remote {server_ip} 1194
resolv-retry infinite
nobind
persist-key
persist-tun

# Authentication
auth-user-pass
auth-nocache

# Security
cipher AES-256-GCM
auth SHA256

# Logging
verb 3
mute 20

# Certificate
<ca>
{ca_cert}</ca>

# Windows compatibility
route-method exe
route-delay 2
"""
    
    config_file = Path("vpn_profiles/test_openvpn.ovpn")
    with open(config_file, 'w') as f:
        f.write(config)
    
    # Create instructions
    instructions = f"""OpenVPN Setup Instructions
=========================

1. Download OpenVPN Connect:
   https://openvpn.net/client/

2. Import this file: test_openvpn.ovpn

3. Login credentials:
   Username: <EMAIL>
   Password: password

4. Connect!

Server: {server_ip}:1194
"""
    
    instructions_file = Path("vpn_profiles/test_instructions.txt")
    with open(instructions_file, 'w') as f:
        f.write(instructions)
    
    print("✅ Client config created")
    return config_file, instructions_file

def check_openssl():
    """Check if OpenSSL is available"""
    try:
        subprocess.run(["openssl", "version"], check=True, capture_output=True)
        return True
    except:
        return False

def main():
    """Main setup function"""
    print("🚀 Simple OpenVPN Setup")
    print("=" * 30)
    
    # Check OpenSSL
    if not check_openssl():
        print("❌ OpenSSL not found!")
        print("📥 Please install OpenSSL:")
        print("   Windows: https://slproweb.com/products/Win32OpenSSL.html")
        print("   Or use: winget install OpenSSL.Light")
        return False
    
    print("✅ OpenSSL found")
    
    # Get local IP
    local_ip = get_local_ip()
    print(f"🌐 Local IP: {local_ip}")
    
    # Setup steps
    create_directories()
    
    if not generate_certificates():
        return False
    
    create_server_config(local_ip)
    create_auth_script()
    config_file, instructions_file = create_client_config(local_ip)
    
    print("\n🎉 OpenVPN setup complete!")
    print("=" * 30)
    print(f"📱 Client file: {config_file}")
    print(f"📋 Instructions: {instructions_file}")
    print(f"🔑 Username: <EMAIL>")
    print(f"🔑 Password: password")
    print(f"🌐 Server: {local_ip}:1194")
    
    print("\n🚀 To start server:")
    print("   python simple_openvpn_setup.py --start")
    
    return True

def start_server():
    """Start OpenVPN server"""
    print("🎯 Starting OpenVPN server...")
    
    config_file = Path("openvpn/server/server.conf")
    if not config_file.exists():
        print("❌ Server config not found. Run setup first.")
        return False
    
    # Change to server directory
    os.chdir("openvpn/server")
    
    try:
        print("🚀 OpenVPN server starting...")
        print("🔌 Listening on port 1194/UDP")
        print("🛑 Press Ctrl+C to stop")
        print("-" * 30)
        
        # Try to find OpenVPN executable
        openvpn_cmd = "openvpn"
        if os.name == 'nt':  # Windows
            possible_paths = [
                r"C:\Program Files\OpenVPN\bin\openvpn.exe",
                r"C:\Program Files (x86)\OpenVPN\bin\openvpn.exe"
            ]
            for path in possible_paths:
                if os.path.exists(path):
                    openvpn_cmd = path
                    break
        
        subprocess.run([openvpn_cmd, "--config", "server.conf"])
        
    except KeyboardInterrupt:
        print("\n🛑 OpenVPN server stopped")
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        print("💡 Make sure OpenVPN is installed")
        return False
    
    return True

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--start":
        start_server()
    else:
        main()
