#!/usr/bin/env python3
"""
Secure VPN Demo Server
Simulates the commercial VPN service for demonstration
"""

import http.server
import socketserver
import json
import sqlite3
import hashlib
import time
import threading
from urllib.parse import urlparse, parse_qs
import os

# Configuration
PORT = 8090
VPN_PORT = 8443
DB_FILE = "demo_vpn.db"

class VPNDemoHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/api/stats':
            self.send_stats()
        elif parsed_path.path == '/api/users':
            self.send_users()
        elif parsed_path.path == '/health':
            self.send_health()
        elif parsed_path.path == '/metrics':
            self.send_metrics()
        else:
            # Serve static files
            super().do_GET()
    
    def do_POST(self):
        parsed_path = urlparse(self.path)
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length).decode('utf-8')
        
        try:
            data = json.loads(post_data) if post_data else {}
        except:
            data = {}
        
        if parsed_path.path == '/api/users':
            self.create_user(data)
        elif parsed_path.path == '/api/auth':
            self.authenticate_user(data)
        elif parsed_path.path == '/api/licenses':
            self.generate_license(data)
        else:
            self.send_error(404, "Not Found")
    
    def send_json_response(self, data, status=200):
        self.send_response(status)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode('utf-8'))
    
    def send_stats(self):
        # Get REAL stats from production database
        try:
            conn = sqlite3.connect("vpn_production.db")
            cursor = conn.cursor()

            # Total active users
            cursor.execute('SELECT COUNT(*) FROM users WHERE is_active = 1')
            total_users = cursor.fetchone()[0]

            # Active connections
            cursor.execute('SELECT COUNT(*) FROM sessions WHERE is_active = 1')
            active_connections = cursor.fetchone()[0]

            # Total bandwidth
            cursor.execute('SELECT SUM(bytes_sent), SUM(bytes_received) FROM sessions')
            bandwidth_data = cursor.fetchone()
            total_sent = bandwidth_data[0] or 0
            total_received = bandwidth_data[1] or 0
            total_bandwidth_gb = (total_sent + total_received) / (1024**3)

            # Revenue calculation (simplified)
            cursor.execute('''
                SELECT plan, COUNT(*) FROM users WHERE is_active = 1 GROUP BY plan
            ''')
            plan_counts = cursor.fetchall()

            plan_prices = {'free': 0, 'basic': 5, 'premium': 10, 'enterprise': 25}
            monthly_revenue = sum(plan_prices.get(plan, 0) * count for plan, count in plan_counts)

            # Success rate (sessions that transferred data vs total)
            cursor.execute('SELECT COUNT(*) FROM sessions WHERE bytes_sent > 0 OR bytes_received > 0')
            successful_sessions = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM sessions')
            total_sessions = cursor.fetchone()[0]
            success_rate = (successful_sessions / max(total_sessions, 1)) * 100

            conn.close()

            stats = {
                "total_users": total_users,
                "active_connections": active_connections,
                "total_bandwidth_gb": round(total_bandwidth_gb, 3),
                "monthly_revenue": monthly_revenue,
                "server_uptime": "Real-time",
                "success_rate": round(success_rate, 1)
            }

        except Exception as e:
            print(f"Error getting real stats: {e}")
            # Fallback to basic stats
            stats = {
                "total_users": 0,
                "active_connections": 0,
                "total_bandwidth_gb": 0,
                "monthly_revenue": 0,
                "server_uptime": "Unknown",
                "success_rate": 0
            }

        self.send_json_response(stats)
    
    def send_users(self):
        # Get REAL users from production database
        try:
            conn = sqlite3.connect("vpn_production.db")
            cursor = conn.cursor()

            cursor.execute('''
                SELECT u.user_id, u.email, u.plan, u.created_at, u.is_active,
                       MAX(s.last_activity) as last_login
                FROM users u
                LEFT JOIN sessions s ON u.user_id = s.user_id
                GROUP BY u.user_id, u.email, u.plan, u.created_at, u.is_active
                ORDER BY u.created_at DESC
            ''')

            user_data = cursor.fetchall()
            conn.close()

            users = []
            for row in user_data:
                user_id, email, plan, created_at, is_active, last_login = row

                # Convert timestamps to readable dates
                created_date = time.strftime('%Y-%m-%d', time.localtime(created_at))
                last_login_date = time.strftime('%Y-%m-%d', time.localtime(last_login)) if last_login else "Never"

                users.append({
                    "user_id": user_id,
                    "email": email,
                    "plan": plan,
                    "created_at": created_date,
                    "last_login": last_login_date,
                    "status": "active" if is_active else "inactive"
                })

        except Exception as e:
            print(f"Error getting real users: {e}")
            users = []

        self.send_json_response(users)
    
    def send_health(self):
        health = {
            "status": "healthy",
            "timestamp": int(time.time()),
            "services": {
                "vpn_server": "running",
                "database": "connected",
                "payment_system": "operational"
            }
        }
        self.send_json_response(health)
    
    def send_metrics(self):
        # Get REAL metrics from production database
        try:
            conn = sqlite3.connect("vpn_production.db")
            cursor = conn.cursor()

            # Get real metrics
            cursor.execute('SELECT COUNT(*) FROM sessions')
            total_connections = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM sessions WHERE is_active = 1')
            active_connections = cursor.fetchone()[0]

            cursor.execute('SELECT SUM(bytes_sent), SUM(bytes_received) FROM sessions')
            traffic_data = cursor.fetchone()
            bytes_sent = traffic_data[0] or 0
            bytes_received = traffic_data[1] or 0

            cursor.execute('SELECT COUNT(*) FROM users WHERE is_active = 1')
            total_users = cursor.fetchone()[0]

            # Calculate revenue
            cursor.execute('SELECT plan, COUNT(*) FROM users WHERE is_active = 1 GROUP BY plan')
            plan_counts = cursor.fetchall()
            plan_prices = {'free': 0, 'basic': 5, 'premium': 10, 'enterprise': 25}
            monthly_revenue = sum(plan_prices.get(plan, 0) * count for plan, count in plan_counts)

            conn.close()

            metrics = """# Secure VPN Metrics - REAL DATA
# Generated: {}

# Connection metrics
vpn_connections_total {}
vpn_connections_active {}
vpn_connections_failed_total 0

# Traffic metrics
vpn_bytes_sent_total {}
vpn_bytes_received_total {}

# User metrics
vpn_users_total {}
vpn_revenue_monthly {}

# System metrics (simulated)
system_cpu_usage_percent 15.2
system_memory_usage_percent 34.7
system_disk_usage_percent 67.1
""".format(int(time.time()), total_connections, active_connections,
           bytes_sent, bytes_received, total_users, monthly_revenue)

        except Exception as e:
            print(f"Error getting real metrics: {e}")
            metrics = """# Secure VPN Metrics - ERROR
# Generated: {}
# Error: {}
""".format(int(time.time()), str(e))
        
        self.send_response(200)
        self.send_header('Content-Type', 'text/plain')
        self.end_headers()
        self.wfile.write(metrics.encode('utf-8'))
    
    def create_user(self, data):
        email = data.get('email', '')
        password = data.get('password', '')
        plan = data.get('plan', 'free')
        
        if not email or not password:
            self.send_json_response({"error": "Email and password required"}, 400)
            return
        
        # Simulate user creation
        user_id = hash(email) % 10000
        response = {
            "user_id": user_id,
            "email": email,
            "plan": plan,
            "max_connections": {"free": 1, "basic": 3, "premium": 5, "enterprise": 10}.get(plan, 1),
            "created_at": int(time.time())
        }
        
        print(f"✅ Created user: {email} (Plan: {plan}, ID: {user_id})")
        self.send_json_response(response, 201)
    
    def authenticate_user(self, data):
        email = data.get('email', '')
        password = data.get('password', '')
        
        if not email or not password:
            self.send_json_response({"error": "Email and password required"}, 400)
            return
        
        # Simulate authentication
        if email == "<EMAIL>" and password == "admin123":
            response = {
                "user_id": 1,
                "email": email,
                "plan": "enterprise",
                "max_connections": 10,
                "subscription_expires": int(time.time()) + 30*24*3600
            }
            print(f"✅ Authenticated user: {email}")
            self.send_json_response(response)
        else:
            print(f"❌ Authentication failed: {email}")
            self.send_json_response({"error": "Authentication failed"}, 401)
    
    def generate_license(self, data):
        user_id = data.get('user_id', 0)
        device_name = data.get('device_name', 'Unknown Device')
        duration = data.get('duration', 30)
        
        if not user_id:
            self.send_json_response({"error": "User ID required"}, 400)
            return
        
        # Generate demo license key
        license_key = f"SVPN_{user_id}_{device_name.replace(' ', '_')}_{int(time.time())}"
        
        response = {
            "license_key": license_key,
            "user_id": user_id,
            "device_name": device_name,
            "expires_in": duration * 24 * 3600,
            "created_at": int(time.time())
        }
        
        print(f"🔑 Generated license: {license_key} for {device_name}")
        self.send_json_response(response)

def start_demo_server():
    """Start the demo VPN API server"""
    print("🚀 Starting Secure VPN Demo Server...")
    print(f"📡 API Server: http://localhost:{PORT}")
    print(f"🔒 VPN Server: localhost:{VPN_PORT} (simulated)")
    print(f"📊 Dashboard: http://localhost:{PORT}/docs/dashboard.html")
    print(f"📈 Metrics: http://localhost:{PORT}/metrics")
    print(f"❤️  Health: http://localhost:{PORT}/health")
    print()
    print("Default admin credentials:")
    print("  Email: <EMAIL>")
    print("  Password: admin123")
    print()
    print("Press Ctrl+C to stop the server")
    print("=" * 50)
    
    # Change to the project directory to serve static files
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    with socketserver.TCPServer(("", PORT), VPNDemoHandler) as httpd:
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Shutting down demo server...")
            httpd.shutdown()

if __name__ == "__main__":
    start_demo_server()
