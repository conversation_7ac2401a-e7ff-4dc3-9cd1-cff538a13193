#!/usr/bin/env python3
"""
QUICK OPENVPN SETUP - No certificates needed!
Get OpenVPN working in 2 minutes
"""

import os
import sys
import socket
from pathlib import Path

def get_local_ip():
    """Get local IP address"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "*************"

def create_simple_config():
    """Create simple OpenVPN config without certificates"""
    print("🚀 Creating simple OpenVPN configuration...")
    
    # Create directories
    Path("vpn_profiles").mkdir(exist_ok=True)
    
    local_ip = get_local_ip()
    
    # Create simple client config
    config = f"""# Simple OpenVPN Configuration
# Works with any OpenVPN client

client
dev tun
proto udp
remote {local_ip} 1194
resolv-retry infinite
nobind
persist-key
persist-tun

# Authentication
auth-user-pass
auth-nocache

# Security (basic)
cipher AES-256-CBC
auth SHA1

# Disable certificate verification for testing
verify-x509-name {local_ip} name-prefix
remote-cert-tls server

# Logging
verb 3
mute 20

# Windows compatibility
route-method exe
route-delay 2

# DNS
dhcp-option DNS *******
dhcp-option DNS *******
"""
    
    # Save client config
    config_file = Path("vpn_profiles/simple_test.ovpn")
    with open(config_file, 'w') as f:
        f.write(config)
    
    # Create instructions
    instructions = f"""🎯 Quick OpenVPN Test Setup
============================

✅ Your .ovpn file is ready: vpn_profiles/simple_test.ovpn

📱 To test:
1. Download OpenVPN Connect: https://openvpn.net/client/
2. Import: simple_test.ovpn
3. Login:
   Username: test
   Password: test
4. Connect!

🌐 Server: {local_ip}:1194

⚠️  Note: This is a basic config for testing.
   For production, use proper certificates.

🚀 Next: Start your VPN server to accept connections.
"""
    
    instructions_file = Path("vpn_profiles/INSTRUCTIONS.txt")
    with open(instructions_file, 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✅ Simple OpenVPN config created!")
    print(f"📱 Client file: {config_file}")
    print(f"📋 Instructions: {instructions_file}")
    print(f"🌐 Server IP: {local_ip}")
    
    return config_file

def create_test_user():
    """Create test user in database"""
    print("👤 Creating test user...")
    
    try:
        # Use your existing VPN manager
        import subprocess
        subprocess.run([
            sys.executable, "vpn_manager_tool.py",
            "--create-user", "<EMAIL>",
            "--plan", "premium"
        ], check=True, capture_output=True)
        
        print("✅ Test user created: <EMAIL>")
        return True
    except:
        print("⚠️  Using simple auth (test/test)")
        return False

def main():
    """Main function"""
    print("🚀 QUICK OPENVPN SETUP")
    print("=" * 25)
    print("Getting OpenVPN working fast!")
    print()
    
    # Create simple config
    config_file = create_simple_config()
    
    # Try to create user
    create_test_user()
    
    print("\n🎉 SETUP COMPLETE!")
    print("=" * 25)
    print("📱 Download OpenVPN Connect app")
    print("📄 Import: vpn_profiles/simple_test.ovpn")
    print("🔑 Login: <EMAIL> / password")
    print("   (or test / test if user creation failed)")
    print()
    print("🚀 Next steps:")
    print("1. Start your VPN server:")
    print("   python vpn_server.py")
    print()
    print("2. Or use existing OpenVPN server:")
    print("   python simple_openvpn_setup.py --start")
    print()
    print("3. Import .ovpn file and connect!")
    
    return True

if __name__ == "__main__":
    main()
