apiVersion: apps/v1
kind: Deployment
metadata:
  name: securevpn-server
  namespace: securevpn
  labels:
    app: securevpn-server
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: securevpn-server
  template:
    metadata:
      labels:
        app: securevpn-server
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
      - name: securevpn-server
        image: securevpn/server:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8443
          name: vpn
          protocol: UDP
        - containerPort: 8080
          name: api
          protocol: TCP
        - containerPort: 9090
          name: metrics
          protocol: TCP
        env:
        - name: VPN_PORT
          valueFrom:
            configMapKeyRef:
              name: securevpn-config
              key: vpn-port
        - name: API_PORT
          valueFrom:
            configMapKeyRef:
              name: securevpn-config
              key: api-port
        - name: METRICS_PORT
          valueFrom:
            configMapKeyRef:
              name: securevpn-config
              key: metrics-port
        - name: MAX_CLIENTS
          valueFrom:
            configMapKeyRef:
              name: securevpn-config
              key: max-clients
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: securevpn-config
              key: log-level
        - name: DATABASE_PATH
          valueFrom:
            configMapKeyRef:
              name: securevpn-config
              key: database-path
        - name: PRIVATE_KEY_FILE
          valueFrom:
            configMapKeyRef:
              name: securevpn-config
              key: private-key-file
        - name: PUBLIC_KEY_FILE
          valueFrom:
            configMapKeyRef:
              name: securevpn-config
              key: public-key-file
        - name: STRIPE_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: securevpn-secrets
              key: stripe-secret-key
        - name: STRIPE_WEBHOOK_SECRET
          valueFrom:
            secretKeyRef:
              name: securevpn-secrets
              key: stripe-webhook-secret
        - name: POSTGRES_HOST
          value: "securevpn-postgres"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: securevpn-secrets
              key: postgres-password
        - name: REDIS_HOST
          value: "securevpn-redis"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: securevpn-secrets
              key: redis-password
        volumeMounts:
        - name: data-volume
          mountPath: /data
        - name: logs-volume
          mountPath: /logs
        - name: ssl-volume
          mountPath: /ssl
          readOnly: true
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            add:
            - NET_ADMIN
            drop:
            - ALL
      volumes:
      - name: data-volume
        persistentVolumeClaim:
          claimName: securevpn-data
      - name: logs-volume
        persistentVolumeClaim:
          claimName: securevpn-logs
      - name: ssl-volume
        secret:
          secretName: securevpn-ssl
      nodeSelector:
        kubernetes.io/os: linux
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: securevpn-postgres
  namespace: securevpn
  labels:
    app: securevpn-postgres
spec:
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: securevpn-postgres
  template:
    metadata:
      labels:
        app: securevpn-postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          value: "securevpn"
        - name: POSTGRES_USER
          value: "vpnuser"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: securevpn-secrets
              key: postgres-password
        - name: PGDATA
          value: "/var/lib/postgresql/data/pgdata"
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - vpnuser
            - -d
            - securevpn
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - vpnuser
            - -d
            - securevpn
          initialDelaySeconds: 10
          periodSeconds: 10
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-data
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-data
  namespace: securevpn
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: fast-ssd
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: securevpn-redis
  namespace: securevpn
  labels:
    app: securevpn-redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: securevpn-redis
  template:
    metadata:
      labels:
        app: securevpn-redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        command:
        - redis-server
        - --appendonly
        - "yes"
        - --requirepass
        - "$(REDIS_PASSWORD)"
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: securevpn-secrets
              key: redis-password
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 10
          periodSeconds: 10
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-data
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-data
  namespace: securevpn
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
  storageClassName: standard
