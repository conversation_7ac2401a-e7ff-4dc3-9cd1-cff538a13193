# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/d/secure-vpn

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/d/secure-vpn/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/securevpn_core.dir/all
all: CMakeFiles/svpn_client.dir/all
all: CMakeFiles/svpn_server.dir/all
all: CMakeFiles/keygen.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/securevpn_core.dir/clean
clean: CMakeFiles/svpn_client.dir/clean
clean: CMakeFiles/svpn_server.dir/clean
clean: CMakeFiles/keygen.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/securevpn_core.dir

# All Build rule for target.
CMakeFiles/securevpn_core.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mnt/d/secure-vpn/build/CMakeFiles --progress-num=3,4,5,6,7,8,9 "Built target securevpn_core"
.PHONY : CMakeFiles/securevpn_core.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/securevpn_core.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/d/secure-vpn/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/securevpn_core.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/d/secure-vpn/build/CMakeFiles 0
.PHONY : CMakeFiles/securevpn_core.dir/rule

# Convenience name for target.
securevpn_core: CMakeFiles/securevpn_core.dir/rule
.PHONY : securevpn_core

# clean rule for target.
CMakeFiles/securevpn_core.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/securevpn_core.dir/build.make CMakeFiles/securevpn_core.dir/clean
.PHONY : CMakeFiles/securevpn_core.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/svpn_client.dir

# All Build rule for target.
CMakeFiles/svpn_client.dir/all: CMakeFiles/securevpn_core.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/svpn_client.dir/build.make CMakeFiles/svpn_client.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/svpn_client.dir/build.make CMakeFiles/svpn_client.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mnt/d/secure-vpn/build/CMakeFiles --progress-num=10,11 "Built target svpn_client"
.PHONY : CMakeFiles/svpn_client.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/svpn_client.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/d/secure-vpn/build/CMakeFiles 9
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/svpn_client.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/d/secure-vpn/build/CMakeFiles 0
.PHONY : CMakeFiles/svpn_client.dir/rule

# Convenience name for target.
svpn_client: CMakeFiles/svpn_client.dir/rule
.PHONY : svpn_client

# clean rule for target.
CMakeFiles/svpn_client.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/svpn_client.dir/build.make CMakeFiles/svpn_client.dir/clean
.PHONY : CMakeFiles/svpn_client.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/svpn_server.dir

# All Build rule for target.
CMakeFiles/svpn_server.dir/all: CMakeFiles/securevpn_core.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/svpn_server.dir/build.make CMakeFiles/svpn_server.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/svpn_server.dir/build.make CMakeFiles/svpn_server.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mnt/d/secure-vpn/build/CMakeFiles --progress-num=12,13 "Built target svpn_server"
.PHONY : CMakeFiles/svpn_server.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/svpn_server.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/d/secure-vpn/build/CMakeFiles 9
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/svpn_server.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/d/secure-vpn/build/CMakeFiles 0
.PHONY : CMakeFiles/svpn_server.dir/rule

# Convenience name for target.
svpn_server: CMakeFiles/svpn_server.dir/rule
.PHONY : svpn_server

# clean rule for target.
CMakeFiles/svpn_server.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/svpn_server.dir/build.make CMakeFiles/svpn_server.dir/clean
.PHONY : CMakeFiles/svpn_server.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/keygen.dir

# All Build rule for target.
CMakeFiles/keygen.dir/all: CMakeFiles/securevpn_core.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/keygen.dir/build.make CMakeFiles/keygen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/keygen.dir/build.make CMakeFiles/keygen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/mnt/d/secure-vpn/build/CMakeFiles --progress-num=1,2 "Built target keygen"
.PHONY : CMakeFiles/keygen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/keygen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/d/secure-vpn/build/CMakeFiles 9
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/keygen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/d/secure-vpn/build/CMakeFiles 0
.PHONY : CMakeFiles/keygen.dir/rule

# Convenience name for target.
keygen: CMakeFiles/keygen.dir/rule
.PHONY : keygen

# clean rule for target.
CMakeFiles/keygen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/keygen.dir/build.make CMakeFiles/keygen.dir/clean
.PHONY : CMakeFiles/keygen.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

